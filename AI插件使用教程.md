# 🤖 Gemini CLI OpenAI 在AI编程插件中的使用教程

## 📋 目录
- [基本配置](#基本配置)
- [Cline插件使用](#cline插件使用)
- [Roo插件使用](#roo插件使用)
- [其他AI插件配置](#其他ai插件配置)
- [启动和维护流程](#启动和维护流程)
- [使用技巧](#使用技巧)
- [故障排除](#故障排除)

## 🎯 基本配置

### 服务器信息
- **本地地址**: `http://127.0.0.1:8787/v1`
- **网络地址**: `http://*************:8787/v1`
- **API密钥**: `sk-your-secret-api-key-here`
- **可用模型**: 
  - `gemini-2.5-pro` (复杂推理)
  - `gemini-2.5-flash` (快速响应)

### 启动服务器
```bash
cd gemini-cli-openai
npm start
```

## 🔧 Cline插件使用

### 1. 配置步骤

#### 方法一：通过VS Code设置
1. 打开VS Code
2. 安装Cline插件
3. 按 `Ctrl+Shift+P` 打开命令面板
4. 输入 "Cline: Open Settings"
5. 配置API设置：
   ```json
   {
     "apiProvider": "openai-compatible",
     "baseUrl": "http://127.0.0.1:8787/v1",
     "apiKey": "sk-your-secret-api-key-here",
     "model": "gemini-2.5-flash"
   }
   ```

#### 方法二：通过配置文件
创建或编辑 `.vscode/settings.json`：
```json
{
  "cline.apiProvider": "openai-compatible",
  "cline.baseUrl": "http://127.0.0.1:8787/v1",
  "cline.apiKey": "sk-your-secret-api-key-here",
  "cline.model": "gemini-2.5-flash"
}
```

### 2. 使用示例

#### 代码生成
```
用户: 帮我创建一个React登录组件，包含用户名和密码输入框

Cline会通过Gemini生成完整的React组件代码
```

#### 代码重构
```
用户: 重构这个函数，使其更加模块化
[选中代码]

Cline会分析代码并提供重构建议
```

#### 错误调试
```
用户: 这段代码有什么问题？
[粘贴错误代码]

Cline会分析错误并提供解决方案
```

### 3. 高级功能

#### 文件操作
```
用户: 创建一个完整的Express.js API项目结构

Cline会创建多个文件和目录
```

#### 代码审查
```
用户: 审查这个文件的代码质量和安全性
[打开文件]

Cline会提供详细的代码审查报告
```

## 🦘 Roo插件使用

### 1. 安装和配置

#### 安装Roo
```bash
npm install -g @roo-ai/cli
```

#### 配置API
创建 `roo.config.json`：
```json
{
  "apiProvider": "openai-compatible",
  "baseURL": "http://127.0.0.1:8787/v1",
  "apiKey": "sk-your-secret-api-key-here",
  "model": "gemini-2.5-pro",
  "temperature": 0.7,
  "maxTokens": 4000
}
```

### 2. 命令行使用

#### 基本对话
```bash
roo chat "解释这段Python代码的功能"
```

#### 代码生成
```bash
roo generate --type component --name LoginForm --framework react
```

#### 文件分析
```bash
roo analyze src/components/Header.jsx
```

### 3. VS Code集成

#### 安装Roo VS Code扩展
1. 在VS Code扩展市场搜索"Roo"
2. 安装并重启VS Code
3. 配置API设置（同上）

#### 使用快捷键
- `Ctrl+Shift+R`: 打开Roo聊天面板
- `Ctrl+Alt+G`: 生成代码
- `Ctrl+Alt+A`: 分析选中代码

## 🔌 其他AI插件配置

### Continue.dev

#### 配置文件 (`~/.continue/config.json`)
```json
{
  "models": [
    {
      "title": "Gemini 2.5 Flash",
      "provider": "openai",
      "model": "gemini-2.5-flash",
      "apiBase": "http://127.0.0.1:8787/v1",
      "apiKey": "sk-your-secret-api-key-here"
    },
    {
      "title": "Gemini 2.5 Pro",
      "provider": "openai", 
      "model": "gemini-2.5-pro",
      "apiBase": "http://127.0.0.1:8787/v1",
      "apiKey": "sk-your-secret-api-key-here"
    }
  ]
}
```

#### 使用方法
- `Ctrl+I`: 内联编辑
- `Ctrl+L`: 聊天面板
- `Ctrl+Shift+L`: 快速操作

### Cursor IDE

#### 配置步骤
1. 打开Cursor设置
2. 导航到 "Models" 部分
3. 点击 "Add Custom Model"
4. 填写配置：
   ```
   Provider: OpenAI Compatible
   Base URL: http://127.0.0.1:8787/v1
   API Key: sk-your-secret-api-key-here
   Model: gemini-2.5-flash
   ```

#### 使用功能
- `Ctrl+K`: AI编辑
- `Ctrl+L`: AI聊天
- `Ctrl+Shift+L`: 代码解释

### Tabnine

#### 配置方法
1. 打开Tabnine设置
2. 选择 "Custom Model"
3. 配置API端点：
   ```
   Endpoint: http://127.0.0.1:8787/v1/chat/completions
   API Key: sk-your-secret-api-key-here
   Model: gemini-2.5-flash
   ```

### CodeGPT

#### VS Code扩展配置
```json
{
  "codegpt.apiProvider": "OpenAI Compatible",
  "codegpt.apiUrl": "http://127.0.0.1:8787/v1",
  "codegpt.apiKey": "sk-your-secret-api-key-here",
  "codegpt.model": "gemini-2.5-flash"
}
```

## 🚀 启动和维护流程

### 1. 每日启动流程

#### 完整启动检查
```bash
# 1. 检查OAuth token状态
npm run check

# 2. 如果token即将过期（<30分钟），更新token
gemini  # 重新认证
npm run update-oauth  # 更新配置

# 3. 启动服务器
npm start

# 4. 验证服务器状态
curl http://127.0.0.1:8787/health
```

#### 快速启动
```bash
# 直接启动（会自动检查OAuth）
npm start
```

### 2. 服务器状态监控

#### 检查服务器健康状态
```bash
curl http://127.0.0.1:8787/health
```

#### 检查可用模型
```bash
curl -H "Authorization: Bearer sk-your-secret-api-key-here" \
     http://127.0.0.1:8787/v1/models
```

#### 测试API功能
```bash
curl -X POST http://127.0.0.1:8787/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-secret-api-key-here" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [{"role": "user", "content": "Hello from AI plugin test!"}]
  }'
```

### 3. OAuth管理

#### 检查token状态
```bash
npm run check
```

#### 更新OAuth凭据
```bash
# 方法1：自动更新
npm run update-oauth

# 方法2：指定凭据文件
npm run update-oauth path/to/oauth_creds.json

# 方法3：手动更新
gemini  # 重新认证
cp ~/.gemini/oauth_creds.json I:/gemini/oauth_creds.json
npm run update-oauth
```

## 💡 使用技巧

### 1. 模型选择策略

#### 根据任务选择模型
```bash
# 快速代码补全和简单问答
model: "gemini-2.5-flash"

# 复杂代码分析和架构设计
model: "gemini-2.5-pro"

# 长文档生成和深度思考
model: "gemini-2.5-pro"
```

#### 动态模型切换
```javascript
// 在支持的插件中，可以根据任务复杂度切换模型
const simpleTask = {
  model: "gemini-2.5-flash",
  messages: [...]
};

const complexTask = {
  model: "gemini-2.5-pro", 
  messages: [...]
};
```

### 2. 性能优化

#### 并发请求管理
```bash
# 服务器支持并发，但建议控制并发数量
# 同时使用多个插件时注意请求频率
```

#### 缓存策略
```bash
# 对于重复的代码分析任务，可以保存结果
# 避免重复调用相同的API请求
```

### 3. 提示词优化

#### 代码生成提示词
```
请生成一个[具体功能]的[编程语言]代码，要求：
1. 遵循最佳实践
2. 包含错误处理
3. 添加详细注释
4. 提供使用示例
```

#### 代码审查提示词
```
请审查以下代码，重点关注：
1. 代码质量和可读性
2. 性能优化机会
3. 安全漏洞
4. 最佳实践遵循情况
```

## 🚨 故障排除

### 1. 连接问题

#### 插件无法连接到API
```bash
# 检查服务器是否运行
curl http://127.0.0.1:8787/health

# 检查端口是否被占用
netstat -an | grep 8787

# 重启服务器
npm start
```

#### 网络访问问题
```bash
# 检查防火墙设置
# Windows: 允许端口8787通过防火墙
# Linux: sudo ufw allow 8787

# 检查代理设置
# 确保AI插件没有使用代理服务器
```

### 2. 认证问题

#### API密钥错误
```bash
# 检查.dev.vars文件中的API密钥
cat .dev.vars | grep OPENAI_API_KEY

# 确保插件中的API密钥与服务器配置一致
```

#### OAuth token过期
```bash
# 检查token状态
npm run check

# 如果过期，重新认证
gemini
npm run update-oauth
npm start
```

### 3. 性能问题

#### 响应速度慢
```bash
# 检查网络延迟
ping 127.0.0.1

# 检查服务器负载
# 查看npm start的输出日志

# 尝试切换到更快的模型
# gemini-2.5-flash 比 gemini-2.5-pro 更快
```

#### 请求失败
```bash
# 查看服务器日志
# npm start 会显示所有请求日志

# 检查请求格式
# 确保插件发送的是标准OpenAI格式请求
```

### 4. 常见错误解决

#### 错误: "Model not found"
```bash
# 确保使用正确的模型名称
# 支持的模型: gemini-2.5-pro, gemini-2.5-flash
```

#### 错误: "Unauthorized"
```bash
# 检查API密钥配置
# 确保插件和服务器使用相同的API密钥
```

#### 错误: "Connection refused"
```bash
# 确保服务器正在运行
npm start

# 检查端口配置
# 默认端口是8787
```

## 📊 使用场景示例

### 场景1: 全栈开发工作流

#### 1. 前端开发 (使用Cline)
```
用户: 创建一个React购物车组件
Cline: [生成完整的React组件代码]

用户: 添加响应式设计
Cline: [修改CSS样式，添加媒体查询]
```

#### 2. 后端开发 (使用Roo)
```bash
roo generate --type api --name shopping-cart --framework express
# 生成Express.js API路由和控制器
```

#### 3. 数据库设计 (使用Continue.dev)
```
用户: 设计购物车相关的数据库表结构
Continue: [生成SQL DDL语句和ER图说明]
```

### 场景2: 代码维护工作流

#### 1. 代码审查 (使用Cursor)
```
选中代码 → Ctrl+L → "审查这段代码的质量"
Cursor: [提供详细的代码审查报告]
```

#### 2. 重构建议 (使用Cline)
```
用户: 这个函数太复杂了，帮我重构
Cline: [分析函数复杂度，提供重构方案]
```

#### 3. 性能优化 (使用Roo)
```bash
roo analyze --performance src/utils/dataProcessor.js
# 分析性能瓶颈，提供优化建议
```

### 场景3: 学习和探索

#### 1. 技术学习
```
用户: 解释React Hooks的工作原理
AI插件: [详细解释概念，提供代码示例]
```

#### 2. 最佳实践
```
用户: Node.js项目的最佳目录结构是什么？
AI插件: [提供项目结构建议和说明]
```

#### 3. 问题解决
```
用户: 如何解决CORS跨域问题？
AI插件: [提供多种解决方案和代码示例]
```

## 🎉 总结

通过本教程，您现在可以：

- ✅ **配置各种AI插件** 连接到您的Gemini服务器
- ✅ **选择合适的模型** 根据任务复杂度优化性能
- ✅ **管理服务器状态** 确保稳定的API服务
- ✅ **处理常见问题** 快速解决连接和认证问题
- ✅ **优化使用体验** 通过最佳实践提高效率

### 快速开始清单

1. ☐ 启动服务器: `npm start`
2. ☐ 验证API: `curl http://127.0.0.1:8787/health`
3. ☐ 配置AI插件: 使用 `http://127.0.0.1:8787/v1`
4. ☐ 测试功能: 在插件中发送测试消息
5. ☐ 开始编程: 享受Gemini的强大AI能力！

**现在您可以在任何支持OpenAI API的AI编程插件中使用Google Gemini的强大能力了！** 🚀

---

## 📞 技术支持

如果遇到问题：
1. 检查服务器状态: `npm run check`
2. 查看启动日志: `npm start` 的输出
3. 验证API连接: 使用curl测试
4. 更新OAuth: `npm run update-oauth`

祝您编程愉快！ 🎯
