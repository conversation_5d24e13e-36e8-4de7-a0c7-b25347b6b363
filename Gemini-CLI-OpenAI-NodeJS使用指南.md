# Gemini CLI OpenAI - Node.js 本地运行使用指南

## 🚀 项目概述

本指南详细介绍如何在Debian/Linux系统上使用Node.js直接运行Gemini CLI OpenAI项目，无需Docker。该项目将Google Gemini模型转换为OpenAI兼容的API接口。

## 📋 系统要求

- **操作系统**: Debian/Ubuntu/Linux
- **Node.js**: v20.0.0 或更高版本（推荐 v22+）
- **npm**: v8.0.0 或更高版本
- **网络**: 需要访问Google API和GitHub

## 🛠️ 安装步骤

### 1. 检查系统环境

```bash
# 检查Node.js版本
node --version  # 应该显示 v20+ 或更高

# 检查npm版本
npm --version   # 应该显示 v8+ 或更高
```

### 2. 获取OAuth2凭据

#### 方法一：使用Gemini CLI（推荐）

```bash
# 安装Gemini CLI
npm install -g @google/gemini-cli

# 启动认证流程
gemini

# 选择 "● Login with Google"
# 在浏览器中完成Google账户登录
```

#### 方法二：手动查找凭据文件

凭据文件位置：
- **Linux/macOS**: `~/.gemini/oauth_creds.json`
- **Windows**: `C:\Users\<USER>\.gemini\oauth_creds.json`

#### 方法三：使用现有凭据文件

如果您已经有OAuth凭据文件（如 `I:\gemini\oauth_creds.json`），可以直接使用：

```bash
# 查看凭据文件内容
cat I:\gemini\oauth_creds.json

# 或在Windows中
type I:\gemini\oauth_creds.json
```

### 3. 克隆项目

```bash
# 克隆项目到本地
git clone https://github.com/GewoonJaap/gemini-cli-openai.git
cd gemini-cli-openai
```

### 4. 安装依赖

```bash
# 安装项目依赖
npm install

# 全局安装wrangler CLI
npm install -g wrangler
```

### 5. 配置环境变量

```bash
# 复制环境变量模板
cp .dev.vars.example .dev.vars

# 编辑环境变量文件
nano .dev.vars  # 或使用其他编辑器
```

#### 环境变量配置示例

```bash
# 必需：OAuth2凭据（从 ~/.gemini/oauth_creds.json 复制）
GCP_SERVICE_ACCOUNT={"access_token":"ya29.a0AS3H6Nx...","refresh_token":"1//09FtpJYpxOd...","scope":"https://www.googleapis.com/auth/cloud-platform ...","token_type":"Bearer","id_token":"eyJhbGciOiJSUzI1NiIs...","expiry_date":*************}

# 可选：API密钥（用于认证，如不设置则API为公开访问）
OPENAI_API_KEY=sk-your-secret-api-key-here

# 可选：启用虚假思维模式（用于测试）
ENABLE_FAKE_THINKING=true

# 可选：启用真实思维模式
ENABLE_REAL_THINKING=true

# 可选：以内容形式流式传输思维
STREAM_THINKING_AS_CONTENT=true

# 可选：启用自动模型切换
ENABLE_AUTO_MODEL_SWITCHING=true
```

### 6. 启动服务器

```bash
# 启动开发服务器
npm run dev

# 服务器将在以下地址启动：
# - http://127.0.0.1:8787
# - http://0.0.0.0:8787
```

## 🧪 API测试

### 1. 健康检查

```bash
curl http://127.0.0.1:8787/health
```

**预期响应**:
```json
{"status":"ok","timestamp":"2025-01-30T12:00:00.000Z"}
```

### 2. 获取模型列表

```bash
curl -H "Authorization: Bearer sk-your-secret-api-key-here" \
     http://127.0.0.1:8787/v1/models
```

**预期响应**:
```json
{
  "object": "list",
  "data": [
    {
      "id": "gemini-2.5-pro",
      "object": "model",
      "created": **********,
      "owned_by": "google-gemini-cli"
    },
    {
      "id": "gemini-2.5-flash",
      "object": "model",
      "created": **********,
      "owned_by": "google-gemini-cli"
    }
  ]
}
```

### 3. 基本聊天测试

```bash
curl -X POST http://127.0.0.1:8787/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-secret-api-key-here" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [
      {
        "role": "user",
        "content": "你好，请用中文回复"
      }
    ]
  }'
```

## 💻 Node.js代码示例

### 1. 基本聊天示例

```javascript
// chat-example.js
const fetch = require('node-fetch'); // npm install node-fetch

const API_BASE = 'http://127.0.0.1:8787/v1';
const API_KEY = 'sk-your-secret-api-key-here';

async function chatWithGemini(message, model = 'gemini-2.5-flash') {
  try {
    const response = await fetch(`${API_BASE}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: model,
        messages: [
          { role: 'user', content: message }
        ],
        stream: false
      })
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  } catch (error) {
    console.error('聊天错误:', error);
    return null;
  }
}

// 使用示例
async function main() {
  const response = await chatWithGemini('请用中文介绍一下人工智能');
  console.log('Gemini回复:', response);
}

main();
```

### 2. 流式响应示例

```javascript
// stream-example.js
const fetch = require('node-fetch');

const API_BASE = 'http://127.0.0.1:8787/v1';
const API_KEY = 'sk-your-secret-api-key-here';

async function streamChatWithGemini(message) {
  try {
    const response = await fetch(`${API_BASE}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: 'gemini-2.5-flash',
        messages: [
          { role: 'user', content: message }
        ],
        stream: true
      })
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    console.log('Gemini回复:');
    console.log('─'.repeat(50));

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ') && !line.includes('[DONE]')) {
          try {
            const data = JSON.parse(line.slice(6));
            const content = data.choices?.[0]?.delta?.content;
            if (content) {
              process.stdout.write(content);
            }
          } catch (e) {
            // 忽略解析错误
          }
        }
      }
    }

    console.log('\n' + '─'.repeat(50));
  } catch (error) {
    console.error('流式聊天错误:', error);
  }
}

// 使用示例
streamChatWithGemini('请详细解释什么是机器学习，并给出一些实际应用例子');
```

### 3. 图像分析示例

```javascript
// image-example.js
const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');

const API_BASE = 'http://127.0.0.1:8787/v1';
const API_KEY = 'sk-your-secret-api-key-here';

function encodeImageToBase64(imagePath) {
  const imageBuffer = fs.readFileSync(imagePath);
  const base64Image = imageBuffer.toString('base64');
  const ext = path.extname(imagePath).toLowerCase();
  
  let mimeType = 'image/jpeg';
  if (ext === '.png') mimeType = 'image/png';
  else if (ext === '.gif') mimeType = 'image/gif';
  else if (ext === '.webp') mimeType = 'image/webp';
  
  return `data:${mimeType};base64,${base64Image}`;
}

async function analyzeImage(imagePath, question = '请描述这张图片') {
  try {
    const imageDataUrl = encodeImageToBase64(imagePath);
    
    const response = await fetch(`${API_BASE}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: 'gemini-2.5-flash',
        messages: [
          {
            role: 'user',
            content: [
              { type: 'text', text: question },
              { 
                type: 'image_url', 
                image_url: { url: imageDataUrl } 
              }
            ]
          }
        ]
      })
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  } catch (error) {
    console.error('图像分析错误:', error);
    return null;
  }
}

// 使用示例
async function main() {
  const imagePath = './example.jpg'; // 替换为您的图片路径
  if (fs.existsSync(imagePath)) {
    const analysis = await analyzeImage(imagePath, '请详细描述这张图片的内容');
    console.log('图像分析结果:', analysis);
  } else {
    console.log('图片文件不存在:', imagePath);
  }
}

main();
```

### 4. 工具调用示例

```javascript
// function-calling-example.js
const fetch = require('node-fetch');

const API_BASE = 'http://127.0.0.1:8787/v1';
const API_KEY = 'sk-your-secret-api-key-here';

async function chatWithTools(message) {
  try {
    const response = await fetch(`${API_BASE}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: 'gemini-2.5-pro',
        messages: [
          { role: 'user', content: message }
        ],
        tools: [
          {
            type: 'function',
            function: {
              name: 'get_weather',
              description: '获取指定城市的天气信息',
              parameters: {
                type: 'object',
                properties: {
                  city: {
                    type: 'string',
                    description: '城市名称'
                  },
                  unit: {
                    type: 'string',
                    enum: ['celsius', 'fahrenheit'],
                    description: '温度单位'
                  }
                },
                required: ['city']
              }
            }
          }
        ],
        tool_choice: 'auto'
      })
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    const data = await response.json();
    const message = data.choices[0].message;

    if (message.tool_calls) {
      console.log('🔧 模型调用了工具:');
      message.tool_calls.forEach(call => {
        console.log(`- 函数: ${call.function.name}`);
        console.log(`- 参数: ${call.function.arguments}`);
      });
    }

    return message.content || '模型调用了工具函数';
  } catch (error) {
    console.error('工具调用错误:', error);
    return null;
  }
}

// 使用示例
async function main() {
  const response = await chatWithTools('北京今天的天气怎么样？');
  console.log('🤖 回复:', response);
}

main();
```

### 5. 思维模式示例

```javascript
// thinking-example.js
const fetch = require('node-fetch');

const API_BASE = 'http://127.0.0.1:8787/v1';
const API_KEY = 'sk-your-secret-api-key-here';

async function chatWithThinking(message) {
  try {
    const response = await fetch(`${API_BASE}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: 'gemini-2.5-pro',
        messages: [
          { role: 'user', content: message }
        ],
        include_reasoning: true,
        thinking_budget: 2048,
        stream: true
      })
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    console.log('🧠 Gemini思维过程和回复:');
    console.log('─'.repeat(60));

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ') && !line.includes('[DONE]')) {
          try {
            const data = JSON.parse(line.slice(6));
            const delta = data.choices?.[0]?.delta;

            if (delta?.reasoning) {
              console.log('💭 [思维]:', delta.reasoning);
            }

            if (delta?.content) {
              process.stdout.write(delta.content);
            }
          } catch (e) {
            // 忽略解析错误
          }
        }
      }
    }

    console.log('\n' + '─'.repeat(60));
  } catch (error) {
    console.error('思维模式错误:', error);
  }
}

// 使用示例
chatWithThinking('请逐步解决：如果一个圆的半径是5cm，面积和周长分别是多少？');
```

## 🔧 高级配置

### 1. 环境变量详解

| 变量名 | 必需 | 描述 | 示例值 |
|--------|------|------|--------|
| `GCP_SERVICE_ACCOUNT` | ✅ | Google OAuth2凭据JSON | `{"access_token":"..."}` |
| `OPENAI_API_KEY` | ❌ | API认证密钥 | `sk-your-key-here` |
| `GEMINI_PROJECT_ID` | ❌ | Google Cloud项目ID | `my-project-123` |
| `ENABLE_FAKE_THINKING` | ❌ | 启用虚假思维模式 | `true` |
| `ENABLE_REAL_THINKING` | ❌ | 启用真实思维模式 | `true` |
| `STREAM_THINKING_AS_CONTENT` | ❌ | 思维内容流式传输 | `true` |
| `ENABLE_AUTO_MODEL_SWITCHING` | ❌ | 自动模型切换 | `true` |

### 2. 自定义端口

修改 `wrangler.toml` 文件：

```toml
[dev]
ip = "0.0.0.0"
port = 3000  # 改为您想要的端口
```

### 3. 内容安全设置

```bash
# 在 .dev.vars 中添加
GEMINI_MODERATION_HARASSMENT_THRESHOLD=BLOCK_NONE
GEMINI_MODERATION_HATE_SPEECH_THRESHOLD=BLOCK_NONE
GEMINI_MODERATION_SEXUALLY_EXPLICIT_THRESHOLD=BLOCK_SOME
GEMINI_MODERATION_DANGEROUS_CONTENT_THRESHOLD=BLOCK_ONLY_HIGH
```

## 🚨 故障排除

### 1. 常见错误

**错误**: `Missing Authorization header`
**解决**: 确保在请求头中包含正确的API密钥

**错误**: `Token refresh failed`
**解决**: 重新运行 `gemini` 命令获取新的OAuth凭据

**错误**: `Project ID discovery failed`
**解决**: 在 `.dev.vars` 中手动设置 `GEMINI_PROJECT_ID`

### 2. OAuth凭据更换详细指南

当您需要更换OAuth凭据时（例如token过期、更换Google账户等），请按照以下步骤操作：

#### 步骤1：获取新的OAuth凭据

**方法A：重新认证（推荐）**
```bash
# 重新运行Gemini CLI认证
gemini

# 选择 "● Login with Google"
# 使用新的Google账户或重新授权现有账户
```

**方法B：使用现有凭据文件**
如果您有新的凭据文件（如 `I:\gemini\new_oauth_creds.json`）：

```bash
# 查看新凭据文件内容
cat I:\gemini\new_oauth_creds.json
```

#### 步骤2：更新环境变量

**自动更新脚本（推荐）**
```bash
# 创建更新脚本
cat > update_oauth.sh << 'EOF'
#!/bin/bash

# 设置凭据文件路径
CREDS_FILE="I:/gemini/oauth_creds.json"  # 修改为您的凭据文件路径
DEV_VARS_FILE="./gemini-cli-openai/.dev.vars"

# 检查凭据文件是否存在
if [ ! -f "$CREDS_FILE" ]; then
    echo "❌ 错误: 凭据文件不存在: $CREDS_FILE"
    exit 1
fi

# 读取凭据文件内容并压缩为一行
CREDS_JSON=$(cat "$CREDS_FILE" | jq -c .)

# 备份原有配置文件
cp "$DEV_VARS_FILE" "$DEV_VARS_FILE.backup.$(date +%Y%m%d_%H%M%S)"

# 更新GCP_SERVICE_ACCOUNT
sed -i "s|^GCP_SERVICE_ACCOUNT=.*|GCP_SERVICE_ACCOUNT=$CREDS_JSON|" "$DEV_VARS_FILE"

echo "✅ OAuth凭据已更新"
echo "📁 原配置已备份"
echo "🔄 请重启服务器以应用新凭据"
EOF

# 运行更新脚本
chmod +x update_oauth.sh
./update_oauth.sh
```

**手动更新方法**
```bash
# 1. 停止当前运行的服务器（如果正在运行）
# 按 Ctrl+C 停止 npm run dev

# 2. 备份当前配置
cp .dev.vars .dev.vars.backup.$(date +%Y%m%d_%H%M%S)

# 3. 编辑环境变量文件
nano .dev.vars  # 或使用其他编辑器

# 4. 替换GCP_SERVICE_ACCOUNT的值
# 将新的OAuth凭据JSON复制并替换原有内容
```

#### 步骤3：验证新凭据格式

确保新的OAuth凭据包含以下必需字段：

```json
{
  "access_token": "ya29.a0AS3H6Nx...",
  "refresh_token": "1//09FtpJYpxOd...",
  "scope": "openid https://www.googleapis.com/auth/cloud-platform ...",
  "token_type": "Bearer",
  "id_token": "eyJhbGciOiJSUzI1NiIs...",
  "expiry_date": *************
}
```

**验证脚本**
```bash
# 创建验证脚本
cat > validate_oauth.js << 'EOF'
const fs = require('fs');

function validateOAuthCreds(credsPath) {
  try {
    const credsContent = fs.readFileSync(credsPath, 'utf8');
    const creds = JSON.parse(credsContent);

    const requiredFields = [
      'access_token', 'refresh_token', 'scope',
      'token_type', 'id_token', 'expiry_date'
    ];

    console.log('🔍 验证OAuth凭据...');

    for (const field of requiredFields) {
      if (!creds[field]) {
        console.log(`❌ 缺少必需字段: ${field}`);
        return false;
      } else {
        console.log(`✅ ${field}: 存在`);
      }
    }

    // 检查token是否过期
    const now = Date.now();
    const expiryDate = creds.expiry_date;

    if (expiryDate < now) {
      console.log(`⚠️  警告: Token已过期 (过期时间: ${new Date(expiryDate)})`);
      console.log('   建议重新认证获取新token');
    } else {
      const timeLeft = Math.floor((expiryDate - now) / 1000 / 60);
      console.log(`✅ Token有效 (剩余时间: ${timeLeft}分钟)`);
    }

    console.log('✅ OAuth凭据格式验证通过');
    return true;

  } catch (error) {
    console.log('❌ 验证失败:', error.message);
    return false;
  }
}

// 使用示例
const credsPath = process.argv[2] || 'I:/gemini/oauth_creds.json';
validateOAuthCreds(credsPath);
EOF

# 运行验证
node validate_oauth.js I:/gemini/oauth_creds.json
```

#### 步骤4：重启服务器并测试

```bash
# 1. 进入项目目录
cd gemini-cli-openai

# 2. 启动服务器
npm run dev

# 3. 等待服务器启动完成，看到以下信息：
# [wrangler:info] Ready on http://0.0.0.0:8787

# 4. 测试新凭据是否工作
curl http://127.0.0.1:8787/health
```

#### 步骤5：验证API功能

```bash
# 测试模型列表
curl -H "Authorization: Bearer sk-your-secret-api-key-here" \
     http://127.0.0.1:8787/v1/models

# 测试聊天功能
curl -X POST http://127.0.0.1:8787/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-secret-api-key-here" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [{"role": "user", "content": "测试新凭据是否工作"}]
  }'
```

### 3. OAuth凭据管理最佳实践

#### 凭据安全存储

```bash
# 设置适当的文件权限（Linux/macOS）
chmod 600 ~/.gemini/oauth_creds.json
chmod 600 .dev.vars

# 确保凭据文件不被版本控制
echo ".dev.vars" >> .gitignore
echo "oauth_creds.json" >> .gitignore
```

#### 多账户管理

如果您需要管理多个Google账户的凭据：

```bash
# 创建凭据管理目录
mkdir -p ~/.gemini/accounts

# 为不同账户保存凭据
cp ~/.gemini/oauth_creds.json ~/.gemini/accounts/account1_creds.json
cp I:/gemini/oauth_creds.json ~/.gemini/accounts/account2_creds.json

# 创建账户切换脚本
cat > switch_account.sh << 'EOF'
#!/bin/bash

ACCOUNT=$1
CREDS_DIR="$HOME/.gemini/accounts"
DEV_VARS_FILE="./gemini-cli-openai/.dev.vars"

if [ -z "$ACCOUNT" ]; then
    echo "用法: $0 <account_name>"
    echo "可用账户:"
    ls $CREDS_DIR/*_creds.json | sed 's/.*\///;s/_creds.json//'
    exit 1
fi

CREDS_FILE="$CREDS_DIR/${ACCOUNT}_creds.json"

if [ ! -f "$CREDS_FILE" ]; then
    echo "❌ 账户凭据文件不存在: $CREDS_FILE"
    exit 1
fi

# 更新环境变量
CREDS_JSON=$(cat "$CREDS_FILE" | jq -c .)
sed -i "s|^GCP_SERVICE_ACCOUNT=.*|GCP_SERVICE_ACCOUNT=$CREDS_JSON|" "$DEV_VARS_FILE"

echo "✅ 已切换到账户: $ACCOUNT"
echo "🔄 请重启服务器以应用更改"
EOF

chmod +x switch_account.sh

# 使用示例
./switch_account.sh account1
```

#### 自动凭据刷新监控

```javascript
// oauth_monitor.js - OAuth凭据监控脚本
const fs = require('fs');
const path = require('path');

class OAuthMonitor {
  constructor(credsPath, devVarsPath) {
    this.credsPath = credsPath;
    this.devVarsPath = devVarsPath;
  }

  checkTokenExpiry() {
    try {
      const creds = JSON.parse(fs.readFileSync(this.credsPath, 'utf8'));
      const now = Date.now();
      const expiryDate = creds.expiry_date;
      const timeLeft = expiryDate - now;

      // 如果token在1小时内过期，发出警告
      if (timeLeft < 3600000 && timeLeft > 0) {
        const minutesLeft = Math.floor(timeLeft / 60000);
        console.log(`⚠️  警告: OAuth token将在 ${minutesLeft} 分钟后过期`);
        console.log('   建议运行: gemini 重新认证');
        return 'warning';
      } else if (timeLeft <= 0) {
        console.log('❌ OAuth token已过期，需要重新认证');
        console.log('   请运行: gemini');
        return 'expired';
      } else {
        const hoursLeft = Math.floor(timeLeft / 3600000);
        console.log(`✅ OAuth token有效 (剩余: ${hoursLeft}小时)`);
        return 'valid';
      }
    } catch (error) {
      console.log('❌ 检查token状态失败:', error.message);
      return 'error';
    }
  }

  watchCredentials() {
    console.log('🔍 开始监控OAuth凭据...');

    // 立即检查一次
    this.checkTokenExpiry();

    // 每30分钟检查一次
    setInterval(() => {
      console.log('\n📅 定期检查OAuth token状态...');
      this.checkTokenExpiry();
    }, 30 * 60 * 1000);
  }
}

// 使用示例
const monitor = new OAuthMonitor(
  'I:/gemini/oauth_creds.json',
  './gemini-cli-openai/.dev.vars'
);

monitor.watchCredentials();
```

### 4. 常见OAuth问题解决

#### 问题1: "invalid_grant" 错误

```bash
# 原因：refresh_token过期或无效
# 解决方案：重新认证
gemini
# 选择新的Google账户或重新授权
```

#### 问题2: "insufficient_scope" 错误

```bash
# 原因：权限范围不足
# 解决方案：确保scope包含必要权限
# 检查凭据中的scope字段应包含：
# - https://www.googleapis.com/auth/cloud-platform
# - https://www.googleapis.com/auth/userinfo.email
# - https://www.googleapis.com/auth/userinfo.profile
```

#### 问题3: 凭据格式错误

```bash
# 使用jq工具验证和格式化JSON
cat I:/gemini/oauth_creds.json | jq .

# 如果出现语法错误，重新获取凭据
gemini
```

### 5. 一键OAuth凭据更新工具

为了简化OAuth凭据更换过程，我们提供了一个自动化更新工具：

#### 下载和使用更新工具

```bash
# 下载更新工具（如果您有update_oauth_credentials.js文件）
# 或者从项目仓库获取

# 查看帮助信息
node update_oauth_credentials.js --help

# 使用默认凭据路径更新
node update_oauth_credentials.js

# 使用指定凭据文件更新
node update_oauth_credentials.js I:/gemini/oauth_creds.json

# 使用其他路径的凭据文件
node update_oauth_credentials.js ~/.gemini/oauth_creds.json
```

#### 更新工具功能特性

✅ **自动验证**: 检查OAuth凭据格式和有效性
✅ **安全备份**: 自动备份当前配置文件
✅ **智能更新**: 自动更新环境变量文件
✅ **功能测试**: 验证新凭据是否正常工作
✅ **详细日志**: 提供完整的操作反馈信息

#### 更新工具输出示例

```bash
$ node update_oauth_credentials.js I:/gemini/oauth_creds.json

🚀 开始OAuth凭据更新流程...

📁 创建备份目录: ./oauth_backups
🔍 验证凭据文件: I:/gemini/oauth_creds.json
✅ Token有效 (剩余时间: 3456分钟)
✅ 凭据格式验证通过
💾 配置已备份到: ./oauth_backups/.dev.vars.backup.2025-01-30T12-00-00-000Z
🔄 更新环境变量文件...
✅ 环境变量已更新
🧪 测试新凭据...
✅ 服务器响应正常
✅ API功能正常
📋 可用模型: gemini-2.5-pro, gemini-2.5-flash

🎉 OAuth凭据更新完成！
📝 后续步骤:
   1. 重启服务器: cd gemini-cli-openai && npm run dev
   2. 测试API功能
   3. 如有问题，可从备份恢复配置
```

### 6. 快速凭据切换命令

创建便捷的命令别名：

```bash
# 添加到 ~/.bashrc 或 ~/.zshrc
alias update-gemini-oauth='node update_oauth_credentials.js'
alias check-gemini-token='node validate_oauth.js'
alias restart-gemini='cd gemini-cli-openai && npm run dev'

# 重新加载配置
source ~/.bashrc  # 或 source ~/.zshrc

# 现在可以使用简短命令
update-gemini-oauth I:/gemini/oauth_creds.json
check-gemini-token
restart-gemini
```

### 7. 定期凭据维护

#### 设置定期检查

```bash
# 创建定期检查脚本
cat > check_oauth_daily.sh << 'EOF'
#!/bin/bash

echo "📅 $(date): 检查OAuth凭据状态"
node validate_oauth.js I:/gemini/oauth_creds.json

# 如果token在24小时内过期，发送提醒
EXPIRY_CHECK=$(node -e "
const fs = require('fs');
const creds = JSON.parse(fs.readFileSync('I:/gemini/oauth_creds.json', 'utf8'));
const timeLeft = creds.expiry_date - Date.now();
const hoursLeft = timeLeft / 1000 / 60 / 60;
if (hoursLeft < 24 && hoursLeft > 0) {
  console.log('EXPIRING_SOON');
} else if (hoursLeft <= 0) {
  console.log('EXPIRED');
} else {
  console.log('OK');
}
")

if [ "$EXPIRY_CHECK" = "EXPIRING_SOON" ]; then
    echo "⚠️  提醒: OAuth token将在24小时内过期，建议更新"
elif [ "$EXPIRY_CHECK" = "EXPIRED" ]; then
    echo "❌ 警告: OAuth token已过期，需要立即更新"
fi
EOF

chmod +x check_oauth_daily.sh

# 设置每日检查（可选）
# crontab -e
# 添加: 0 9 * * * /path/to/check_oauth_daily.sh
```

#### 凭据轮换策略

```bash
# 创建凭据轮换脚本
cat > rotate_oauth.sh << 'EOF'
#!/bin/bash

echo "🔄 开始OAuth凭据轮换..."

# 1. 备份当前凭据
cp I:/gemini/oauth_creds.json I:/gemini/oauth_creds.json.old

# 2. 重新认证获取新凭据
echo "🔐 请在浏览器中完成重新认证..."
gemini

# 3. 验证新凭据
if node validate_oauth.js ~/.gemini/oauth_creds.json; then
    # 4. 复制新凭据到工作目录
    cp ~/.gemini/oauth_creds.json I:/gemini/oauth_creds.json

    # 5. 更新项目配置
    node update_oauth_credentials.js I:/gemini/oauth_creds.json

    echo "✅ OAuth凭据轮换完成"
else
    echo "❌ 新凭据验证失败，保持原有凭据"
    cp I:/gemini/oauth_creds.json.old I:/gemini/oauth_creds.json
fi
EOF

chmod +x rotate_oauth.sh
```

## 📋 OAuth凭据管理总结

### 🔧 提供的工具

| 工具 | 文件名 | 功能 | 使用场景 |
|------|--------|------|----------|
| **凭据验证工具** | `validate_oauth.js` | 验证OAuth凭据格式和有效性 | 日常检查、故障排除 |
| **凭据更新工具** | `update_oauth_credentials.js` | 自动更新OAuth凭据到项目 | 凭据过期、账户切换 |
| **定期检查脚本** | `check_oauth_daily.sh` | 定期检查token状态 | 预防性维护 |
| **凭据轮换脚本** | `rotate_oauth.sh` | 完整的凭据轮换流程 | 安全性维护 |

### 🚀 快速操作指南

#### 日常使用
```bash
# 检查当前凭据状态
node validate_oauth.js I:/gemini/oauth_creds.json

# 更新凭据（如果有新的凭据文件）
node update_oauth_credentials.js I:/gemini/oauth_creds.json

# 重启服务器应用新凭据
cd gemini-cli-openai && npm run dev
```

#### 紧急情况处理
```bash
# Token过期紧急处理
gemini                                    # 重新认证
cp ~/.gemini/oauth_creds.json I:/gemini/ # 复制新凭据
node update_oauth_credentials.js         # 更新项目配置
cd gemini-cli-openai && npm run dev      # 重启服务器
```

#### 多账户管理
```bash
# 保存不同账户的凭据
mkdir -p ~/.gemini/accounts
cp ~/.gemini/oauth_creds.json ~/.gemini/accounts/account1_creds.json

# 切换账户
./switch_account.sh account1
cd gemini-cli-openai && npm run dev
```

### ⚠️ 重要注意事项

1. **安全性**
   - 凭据文件包含敏感信息，请妥善保管
   - 设置适当的文件权限：`chmod 600 oauth_creds.json`
   - 不要将凭据文件提交到版本控制系统

2. **有效期管理**
   - OAuth token通常有效期为1小时
   - Refresh token可以用于获取新的access token
   - 建议定期检查token状态

3. **备份策略**
   - 更新工具会自动备份配置文件
   - 备份文件保存在 `./oauth_backups/` 目录
   - 可以从备份恢复之前的配置

4. **故障恢复**
   - 如果更新失败，可以从备份恢复
   - 如果凭据损坏，重新运行 `gemini` 命令
   - 如果服务器无法启动，检查 `.dev.vars` 文件格式

### 📞 常见问题快速解决

| 问题 | 症状 | 解决方案 |
|------|------|----------|
| **Token过期** | API返回401错误 | `gemini` → 重新认证 |
| **凭据格式错误** | 服务器启动失败 | `node validate_oauth.js` 检查格式 |
| **权限不足** | API功能受限 | 检查scope权限，重新认证 |
| **文件不存在** | 找不到凭据文件 | 确认文件路径，运行 `gemini` |
| **配置损坏** | 服务异常 | 从备份恢复或重新配置 |

### 🎯 最佳实践建议

1. **定期维护**
   - 每周检查一次token状态
   - 每月轮换一次凭据（可选）
   - 及时清理过期的备份文件

2. **自动化管理**
   - 使用提供的脚本工具
   - 设置定期检查任务
   - 建立监控和告警机制

3. **安全措施**
   - 限制凭据文件访问权限
   - 定期更换认证凭据
   - 监控异常访问活动

4. **文档记录**
   - 记录凭据更新时间
   - 保存重要配置变更
   - 建立操作日志

通过遵循这些指南和使用提供的工具，您可以高效、安全地管理Gemini CLI OpenAI项目的OAuth凭据。

## 🔔 OAuth到期提醒系统

为了避免因token过期导致的服务中断，我们提供了多种OAuth到期提醒工具：

### 1. 快速检查工具

**文件**: `quick_token_check.js`
**用途**: 快速检查当前token状态

```bash
# 快速检查token状态
node quick_token_check.js I:/gemini/oauth_creds.json

# 输出示例:
# ✅ Token状态正常 (剩余 2 小时)
# ⏰ 提醒：Token将在 1 小时 30 分钟后过期
# ⚠️  警告：Token将在 25 分钟后过期
# 🚨 紧急：Token将在 3 分钟后过期！
```

### 2. 实时监控工具

**文件**: `oauth_expiry_monitor.js`
**用途**: 持续监控token状态，自动发送提醒

```bash
# 启动监控（默认5分钟检查一次）
node oauth_expiry_monitor.js I:/gemini/oauth_creds.json

# 自定义检查间隔（每1分钟检查一次）
node oauth_expiry_monitor.js I:/gemini/oauth_creds.json 1

# 查看帮助
node oauth_expiry_monitor.js --help
```

**监控输出示例**:
```
🚀 开始监控OAuth token状态...
📁 凭据文件: I:/gemini/oauth_creds.json
⏰ 检查间隔: 5分钟
🔄 按 Ctrl+C 停止监控

✅ [2025/7/30 08:00:00] OAuth Token状态正常，剩余时间: 2小时 30分钟
⏰ [2025/7/30 09:30:00] OAuth Token将在 1小时 0分钟 后过期
⚠️  [2025/7/30 10:00:00] OAuth Token将在 30分钟 后过期，建议准备更新
🚨 [2025/7/30 10:25:00] 紧急：OAuth Token将在 5分钟 后过期！请立即更新
```

### 3. 带监控的服务器启动器

**文件**: `start_with_monitor.js`
**用途**: 启动服务器的同时自动启动token监控

```bash
# 启动服务器和监控
node start_with_monitor.js

# 查看帮助
node start_with_monitor.js --help
```

**启动输出示例**:
```
🎯 Gemini CLI OpenAI 服务器启动器
==================================================
🔍 检查OAuth token状态...
✅ Token状态正常 (剩余 2 小时 15 分钟)

🚀 启动开发服务器...
👁️  启动OAuth token监控...

📝 使用提示:
   - 服务器地址: http://127.0.0.1:8787
   - 按 Ctrl+C 停止服务器和监控
   - OAuth监控每3分钟检查一次token状态
   - 如收到过期警告，请及时更新token
```

### 4. 提醒级别说明

| 级别 | 图标 | 剩余时间 | 提醒频率 | 建议操作 |
|------|------|----------|----------|----------|
| **正常** | ✅ | > 1小时 | 每小时 | 无需操作 |
| **信息** | ⏰ | 30分钟-1小时 | 每30分钟 | 注意即将过期 |
| **警告** | ⚠️ | 5-30分钟 | 每10分钟 | 建议准备更新 |
| **紧急** | 🚨 | < 5分钟 | 持续提醒 | 立即更新token |

### 5. 集成到开发流程

#### 方法1：替换默认启动命令

```bash
# 将原来的启动命令
npm run dev

# 替换为带监控的启动命令
node start_with_monitor.js
```

#### 方法2：添加到package.json

```json
{
  "scripts": {
    "dev": "wrangler dev --local",
    "dev:monitor": "node start_with_monitor.js",
    "token:check": "node quick_token_check.js I:/gemini/oauth_creds.json",
    "token:monitor": "node oauth_expiry_monitor.js I:/gemini/oauth_creds.json"
  }
}
```

使用新命令：
```bash
npm run dev:monitor    # 启动服务器和监控
npm run token:check    # 快速检查token
npm run token:monitor  # 仅启动监控
```

#### 方法3：创建便捷别名

```bash
# 添加到 ~/.bashrc 或 ~/.zshrc
alias gemini-start='node start_with_monitor.js'
alias gemini-check='node quick_token_check.js I:/gemini/oauth_creds.json'
alias gemini-monitor='node oauth_expiry_monitor.js I:/gemini/oauth_creds.json'

# 重新加载配置
source ~/.bashrc

# 使用别名
gemini-start    # 启动服务器和监控
gemini-check    # 检查token状态
gemini-monitor  # 启动监控
```

### 6. 自动化处理建议

#### 定时检查脚本

```bash
# 创建定时检查脚本
cat > token_health_check.sh << 'EOF'
#!/bin/bash

echo "📅 $(date): OAuth Token健康检查"

# 检查token状态
RESULT=$(node quick_token_check.js I:/gemini/oauth_creds.json 2>&1)
echo "$RESULT"

# 如果包含"紧急"或"过期"，发送邮件通知（可选）
if echo "$RESULT" | grep -E "(紧急|过期)" > /dev/null; then
    echo "⚠️  检测到token即将过期或已过期"
    # 这里可以添加邮件通知或其他告警机制
fi
EOF

chmod +x token_health_check.sh

# 设置定时任务（每小时检查一次）
# crontab -e
# 添加: 0 * * * * /path/to/token_health_check.sh
```

#### 系统服务配置（Linux）

```bash
# 创建systemd服务文件
sudo tee /etc/systemd/system/gemini-oauth-monitor.service << 'EOF'
[Unit]
Description=Gemini OAuth Token Monitor
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/gemini-cli-openai
ExecStart=/usr/bin/node oauth_expiry_monitor.js I:/gemini/oauth_creds.json 5
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl enable gemini-oauth-monitor
sudo systemctl start gemini-oauth-monitor

# 查看服务状态
sudo systemctl status gemini-oauth-monitor
```

### 7. 故障处理

#### 监控工具无法启动

```bash
# 检查Node.js版本
node --version

# 检查文件权限
ls -la oauth_expiry_monitor.js

# 检查凭据文件
node quick_token_check.js I:/gemini/oauth_creds.json
```

#### 提醒不及时

```bash
# 减少检查间隔
node oauth_expiry_monitor.js I:/gemini/oauth_creds.json 1  # 每分钟检查

# 手动检查
node quick_token_check.js I:/gemini/oauth_creds.json
```

#### 系统通知不工作

- **Windows**: 确保PowerShell可用
- **Linux**: 可以集成 `notify-send` 命令
- **macOS**: 可以使用 `osascript` 显示通知

通过这些OAuth到期提醒工具，您可以：
- ✅ 实时监控token状态
- ✅ 提前收到过期警告
- ✅ 避免服务中断
- ✅ 自动化token管理流程

### 2. 调试端点

```bash
# 检查token缓存状态
curl -H "Authorization: Bearer sk-your-secret-api-key-here" \
     http://127.0.0.1:8787/v1/debug/cache

# 测试认证
curl -X POST -H "Authorization: Bearer sk-your-secret-api-key-here" \
     http://127.0.0.1:8787/v1/token-test
```

## 📊 性能优化

### 1. 模型选择建议

- **gemini-2.5-flash**: 适合快速响应，日常对话
- **gemini-2.5-pro**: 适合复杂推理，深度分析

### 2. 批量请求示例

```javascript
async function batchRequests(messages) {
  const promises = messages.map(message =>
    chatWithGemini(message, 'gemini-2.5-flash')
  );

  const results = await Promise.all(promises);
  return results;
}
```

## 🔗 与OpenAI SDK兼容

```javascript
// 使用官方OpenAI SDK
const { Configuration, OpenAIApi } = require('openai');

const configuration = new Configuration({
  apiKey: 'sk-your-secret-api-key-here',
  basePath: 'http://127.0.0.1:8787/v1'
});

const openai = new OpenAIApi(configuration);

async function useOpenAISDK() {
  const completion = await openai.createChatCompletion({
    model: 'gemini-2.5-flash',
    messages: [{ role: 'user', content: '你好' }]
  });

  console.log(completion.data.choices[0].message.content);
}
```

## 📝 总结

✅ **成功在Debian系统上运行gemini-cli-openai项目**
- 使用Node.js直接运行（无需Docker）
- OAuth2认证正常工作
- API功能完全正常
- 支持OpenAI兼容的接口

**服务器地址**: `http://127.0.0.1:8787`
**API兼容性**: 完全兼容OpenAI API格式
**支持的模型**: `gemini-2.5-pro`, `gemini-2.5-flash`

现在您可以像使用OpenAI API一样使用Google的Gemini模型了！🎉
