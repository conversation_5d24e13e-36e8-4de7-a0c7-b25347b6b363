# 🔄 OAuth凭据更新工具使用说明

## 📋 工具概述

`update_oauth_credentials.js` 是一个自动化的OAuth凭据更新工具，用于安全地更新Gemini CLI OpenAI项目的OAuth认证配置。

## 🚀 快速使用

### 基本用法
```bash
# 使用默认凭据路径
node update_oauth_credentials.js

# 指定凭据文件路径
node update_oauth_credentials.js I:/gemini/oauth_creds.json

# 查看帮助信息
node update_oauth_credentials.js --help
```

### 通过npm脚本使用
```bash
# 如果已配置npm脚本
npm run update-oauth

# 指定凭据文件
npm run update-oauth I:/gemini/oauth_creds.json
```

## 🔧 详细功能说明

### 1. 自动验证凭据格式

工具会检查以下必需字段：
- `access_token` - 访问令牌
- `refresh_token` - 刷新令牌
- `scope` - 权限范围
- `token_type` - 令牌类型
- `id_token` - ID令牌
- `expiry_date` - 过期时间

### 2. 智能备份机制

```bash
# 自动创建备份目录
./oauth_backups/

# 备份文件命名格式
.dev.vars.backup.2025-01-30T12-00-00-000Z
```

### 3. 安全更新流程

1. **验证新凭据** - 检查格式和有效性
2. **备份当前配置** - 防止数据丢失
3. **更新环境变量** - 替换.dev.vars中的配置
4. **测试新凭据** - 验证API连接
5. **提供反馈** - 显示操作结果

## 📊 使用场景

### 场景1: Token即将过期

```bash
# 1. 检查当前token状态
npm run check
# 输出: ⚠️ Token将在 25 分钟后过期

# 2. 重新认证
gemini
# 选择 "Login with Google"

# 3. 更新配置
node update_oauth_credentials.js
```

### 场景2: 切换Google账户

```bash
# 1. 使用新账户认证
gemini
# 选择新的Google账户

# 2. 更新项目配置
node update_oauth_credentials.js ~/.gemini/oauth_creds.json
```

### 场景3: 恢复备份配置

```bash
# 查看可用备份
ls oauth_backups/

# 手动恢复备份（如果需要）
cp oauth_backups/.dev.vars.backup.2025-01-30T12-00-00-000Z .dev.vars
```

## 🎯 输出示例

### 成功更新示例
```bash
$ node update_oauth_credentials.js I:/gemini/oauth_creds.json

🚀 开始OAuth凭据更新流程...

📁 创建备份目录: ./oauth_backups
🔍 验证凭据文件: I:/gemini/oauth_creds.json
✅ Token有效 (剩余时间: 3456分钟)
✅ 凭据格式验证通过
💾 配置已备份到: ./oauth_backups/.dev.vars.backup.2025-01-30T12-00-00-000Z
🔄 更新环境变量文件...
✅ 环境变量已更新
🧪 测试新凭据...
✅ 服务器响应正常
✅ API功能正常
📋 可用模型: gemini-2.5-pro, gemini-2.5-flash

🎉 OAuth凭据更新完成！
📝 后续步骤:
   1. 重启服务器: cd gemini-cli-openai && npm run dev
   2. 测试API功能
   3. 如有问题，可从备份恢复配置
```

### Token过期警告示例
```bash
🔍 验证凭据文件: I:/gemini/oauth_creds.json
⚠️  警告: Token已过期 (过期时间: 2025/1/30 08:53:55)
   建议重新运行 gemini 命令获取新token
✅ 凭据格式验证通过
```

## 🚨 错误处理

### 常见错误及解决方案

#### 1. 凭据文件不存在
```bash
❌ 凭据验证失败: 凭据文件不存在: I:/gemini/oauth_creds.json

解决方案:
1. 运行 gemini 命令重新认证
2. 检查文件路径是否正确
3. 确保OAuth认证已完成
```

#### 2. 凭据格式无效
```bash
❌ 凭据验证失败: 缺少必需字段: refresh_token, scope

解决方案:
1. 重新运行 gemini 命令
2. 确保完整完成OAuth流程
3. 检查凭据文件是否被损坏
```

#### 3. 备份失败
```bash
❌ 备份失败: EACCES: permission denied

解决方案:
1. 检查文件权限
2. 确保有写入权限
3. 以管理员身份运行（如果需要）
```

#### 4. 服务器连接失败
```bash
⚠️  无法连接到服务器，请手动启动服务器测试

解决方案:
1. 启动服务器: npm start
2. 检查端口8787是否被占用
3. 验证防火墙设置
```

## 🔧 高级用法

### 1. 批量更新多个项目

```bash
# 创建批量更新脚本
cat > batch_update.sh << 'EOF'
#!/bin/bash

PROJECTS=("project1" "project2" "project3")
CREDS_PATH="I:/gemini/oauth_creds.json"

for project in "${PROJECTS[@]}"; do
    echo "更新项目: $project"
    cd "$project"
    node update_oauth_credentials.js "$CREDS_PATH"
    cd ..
done
EOF

chmod +x batch_update.sh
./batch_update.sh
```

### 2. 定时自动更新

```bash
# 创建定时更新脚本
cat > auto_update.sh << 'EOF'
#!/bin/bash

# 检查token是否即将过期（30分钟内）
EXPIRY_CHECK=$(node -e "
const fs = require('fs');
const creds = JSON.parse(fs.readFileSync('I:/gemini/oauth_creds.json', 'utf8'));
const timeLeft = creds.expiry_date - Date.now();
const minutesLeft = timeLeft / 1000 / 60;
console.log(minutesLeft < 30 ? 'EXPIRING' : 'OK');
")

if [ "$EXPIRY_CHECK" = "EXPIRING" ]; then
    echo "Token即将过期，尝试自动更新..."
    node update_oauth_credentials.js
fi
EOF

# 设置定时任务（每15分钟检查一次）
# crontab -e
# */15 * * * * /path/to/auto_update.sh
```

### 3. 集成到CI/CD流水线

```yaml
# GitHub Actions示例
name: Update OAuth Credentials
on:
  schedule:
    - cron: '0 */6 * * *'  # 每6小时检查一次

jobs:
  update-oauth:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '20'
      - name: Update OAuth
        run: |
          if node update_oauth_credentials.js; then
            echo "OAuth更新成功"
          else
            echo "OAuth更新失败，需要手动处理"
            exit 1
          fi
```

## 📁 文件结构说明

### 生成的文件和目录

```
gemini-cli-openai/
├── .dev.vars                    # 主配置文件
├── .dev.vars.example           # 配置模板
├── oauth_backups/              # 自动备份目录
│   ├── .dev.vars.backup.2025-01-30T12-00-00-000Z
│   ├── .dev.vars.backup.2025-01-30T13-00-00-000Z
│   └── ...
└── update_oauth_credentials.js # 更新工具
```

### 配置文件格式

#### .dev.vars 文件结构
```bash
GCP_SERVICE_ACCOUNT={"access_token":"ya29...","refresh_token":"1//...","scope":"...","token_type":"Bearer","id_token":"eyJ...","expiry_date":*************}

# 可选配置
OPENAI_API_KEY=sk-your-secret-api-key-here
GEMINI_PROJECT_ID=your-project-id
ENABLE_FAKE_THINKING=true
ENABLE_REAL_THINKING=true
```

## 🛡️ 安全最佳实践

### 1. 文件权限管理
```bash
# 设置适当的文件权限
chmod 600 .dev.vars
chmod 600 I:/gemini/oauth_creds.json
chmod 700 oauth_backups/
```

### 2. 敏感信息保护
```bash
# 确保敏感文件不被版本控制
echo ".dev.vars" >> .gitignore
echo "oauth_creds.json" >> .gitignore
echo "oauth_backups/" >> .gitignore
```

### 3. 定期清理备份
```bash
# 清理30天前的备份文件
find oauth_backups/ -name "*.backup.*" -mtime +30 -delete
```

## 🎉 总结

OAuth凭据更新工具提供了：

- ✅ **自动化流程** - 减少手动操作错误
- ✅ **安全备份** - 防止配置丢失
- ✅ **格式验证** - 确保凭据有效性
- ✅ **连接测试** - 验证更新结果
- ✅ **详细反馈** - 清晰的操作状态

### 推荐工作流程

1. **定期检查**: `npm run check`
2. **及时更新**: 收到过期警告时运行更新工具
3. **验证结果**: 确保API功能正常
4. **维护备份**: 定期清理旧备份文件

通过使用这个工具，您可以轻松管理OAuth凭据，确保Gemini CLI OpenAI服务的持续稳定运行！ 🚀
