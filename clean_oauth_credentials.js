#!/usr/bin/env node

/**
 * OAuth凭据清理工具
 * 清理OAuth凭据中的Unicode字符，解决ByteString转换错误
 */

const fs = require('fs');
const path = require('path');

class OAuthCleaner {
  constructor() {
    this.inputPath = 'I:/gemini/oauth_creds.json';
    this.outputPath = 'I:/gemini/oauth_creds_clean.json';
    this.backupPath = 'I:/gemini/oauth_creds_backup.json';
  }

  // 清理字符串中的非ASCII字符
  cleanString(str) {
    if (typeof str !== 'string') return str;
    
    // 替换非ASCII字符为安全的ASCII字符
    return str.replace(/[^\x00-\x7F]/g, '?');
  }

  // 递归清理对象中的所有字符串
  cleanObject(obj) {
    if (typeof obj === 'string') {
      return this.cleanString(obj);
    } else if (Array.isArray(obj)) {
      return obj.map(item => this.cleanObject(item));
    } else if (obj !== null && typeof obj === 'object') {
      const cleaned = {};
      for (const [key, value] of Object.entries(obj)) {
        cleaned[key] = this.cleanObject(value);
      }
      return cleaned;
    }
    return obj;
  }

  // 主清理流程
  clean() {
    try {
      console.log('🧹 开始清理OAuth凭据...');

      // 检查输入文件
      if (!fs.existsSync(this.inputPath)) {
        throw new Error(`凭据文件不存在: ${this.inputPath}`);
      }

      // 读取原始凭据
      console.log(`📖 读取凭据文件: ${this.inputPath}`);
      const originalContent = fs.readFileSync(this.inputPath, 'utf8');
      const originalCreds = JSON.parse(originalContent);

      // 备份原始文件
      console.log(`💾 备份原始文件: ${this.backupPath}`);
      fs.copyFileSync(this.inputPath, this.backupPath);

      // 清理凭据
      console.log('🔧 清理Unicode字符...');
      const cleanedCreds = this.cleanObject(originalCreds);

      // 保存清理后的凭据
      console.log(`💾 保存清理后的凭据: ${this.outputPath}`);
      fs.writeFileSync(this.outputPath, JSON.stringify(cleanedCreds, null, 2));

      // 替换原文件
      console.log('🔄 替换原始凭据文件...');
      fs.copyFileSync(this.outputPath, this.inputPath);

      // 验证清理结果
      console.log('✅ 验证清理结果...');
      const verifyContent = fs.readFileSync(this.inputPath, 'utf8');
      const verifyCreds = JSON.parse(verifyContent);

      // 检查是否还有非ASCII字符
      const hasUnicode = JSON.stringify(verifyCreds).match(/[^\x00-\x7F]/);
      if (hasUnicode) {
        console.log('⚠️  警告: 仍然存在非ASCII字符');
      } else {
        console.log('✅ 所有非ASCII字符已清理');
      }

      console.log('\n🎉 OAuth凭据清理完成！');
      console.log('📝 文件说明:');
      console.log(`   - 原始备份: ${this.backupPath}`);
      console.log(`   - 清理版本: ${this.outputPath}`);
      console.log(`   - 当前使用: ${this.inputPath}`);
      console.log('\n📋 后续步骤:');
      console.log('   1. 重启服务器: npm start');
      console.log('   2. 测试API连接');
      console.log('   3. 如有问题，可从备份恢复');

      return true;

    } catch (error) {
      console.error('❌ 清理失败:', error.message);
      return false;
    }
  }

  // 恢复备份
  restore() {
    try {
      if (!fs.existsSync(this.backupPath)) {
        throw new Error(`备份文件不存在: ${this.backupPath}`);
      }

      console.log('🔄 恢复原始凭据...');
      fs.copyFileSync(this.backupPath, this.inputPath);
      console.log('✅ 已恢复原始凭据');
      return true;

    } catch (error) {
      console.error('❌ 恢复失败:', error.message);
      return false;
    }
  }

  // 显示帮助信息
  showHelp() {
    console.log(`
📖 OAuth凭据清理工具使用说明

用法:
  node clean_oauth_credentials.js [选项]

选项:
  --clean     清理OAuth凭据中的Unicode字符 (默认)
  --restore   恢复原始备份文件
  --help      显示帮助信息

示例:
  node clean_oauth_credentials.js          # 清理凭据
  node clean_oauth_credentials.js --clean  # 清理凭据
  node clean_oauth_credentials.js --restore # 恢复备份

功能:
  ✅ 清理非ASCII字符
  ✅ 自动备份原始文件
  ✅ 验证清理结果
  ✅ 支持恢复操作

注意事项:
  - 清理会将非ASCII字符替换为 '?'
  - 原始文件会自动备份
  - 清理后需要重启服务器
`);
  }
}

// 主程序
function main() {
  const args = process.argv.slice(2);
  const cleaner = new OAuthCleaner();

  if (args.includes('--help') || args.includes('-h')) {
    cleaner.showHelp();
    return;
  }

  if (args.includes('--restore')) {
    const success = cleaner.restore();
    process.exit(success ? 0 : 1);
  } else {
    // 默认执行清理
    const success = cleaner.clean();
    process.exit(success ? 0 : 1);
  }
}

if (require.main === module) {
  main();
}

module.exports = OAuthCleaner;
