# Gemini CLI OpenAI Worker Environment Variables

# Required: OAuth2 credentials <PERSON><PERSON><PERSON> from Gemini CLI authentication
# Get this by running `gemini auth` and copying the contents of ~/.gemini/oauth_creds.json
GCP_SERVICE_ACCOUNT={"access_token":"************************************************************************************************************************************************************************************************************************************","refresh_token":"1//0gXthdrkeerK_CgYIARAAGBASNwF-L9Ir-h9uzK8WDxf0FC7Pl1vj50Pc6O_XRP7mA44yHn_uGOvWYlzL9oY0ifY01I_kMo-0YyU","scope":"openid https://www.googleapis.com/auth/cloud-platform https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile","token_type":"Bearer","id_token":"eyJhbGciOiJSUzI1NiIsImtpZCI6ImRkNTMwMTIwNGZjMWQ2YTBkNjhjNzgzYTM1Y2M5YzEwYjI1ZTFmNGEiLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Qh5Y3Qs4_NpTIga6Ul_Lr4Hl9N6GLof9LpSnDveA-srdz2LQFbsuH47BrWpKIqmYhaOGWjds9uFWyJFDXvxF27R1WFdO5I9WgEONPG9jcOmLpW1Iw11uQctMkv7BNFVf4PG9vONlcptmX4Paaj-ynYRbGJ_6UsZCczK-HyRWZfKHJNXYDQxAI4ew1y5XR-qvadLCwSrbjFX140-8JY2BtxAxB3Jb52qbIb4iM57GgVCyt6T9hwmaxPAiLOW_IZflcSw071eyVU3CHhoAVf824ckMo_YdMDBbWy3oe8AVmOzqsKSJ7gimhyGOIMivji-5mUv7lGEn2uKGDUTGvvSm4Q","expiry_date":1753836835964}

# Optional: Google Cloud Project ID (auto-discovered if not set)
# GEMINI_PROJECT_ID=your-project-id

# Optional: API key for authentication (if not set, API is public)
OPENAI_API_KEY=sk-19pX9ydDx7T0i43HQKnQr3jv4cc28xwwFCIW9AZVuagAamIX
# When set, clients must include "Authorization: Bearer <your-api-key>" header
# Example: sk-1234567890abcdef1234567890abcdef

# Optional: Enable fake thinking output for thinking models (set to "true" to enable)
# When enabled, models marked with thinking: true will generate synthetic reasoning text
# before providing their actual response, similar to OpenAI's o3 model behavior
ENABLE_FAKE_THINKING=true

# Optional: Enable real Gemini thinking output (set to "true" to enable)
# When enabled, requests with include_reasoning=true will use Gemini's native thinking
# This requires thinking-capable models and provides genuine reasoning from Gemini
ENABLE_REAL_THINKING=true

# Optional: Stream thinking as content with <thinking> tags (DeepSeek R1 style)
# When enabled along with either thinking mode, reasoning will be streamed as regular content
# wrapped in <thinking></thinking> tags instead of using the reasoning field
STREAM_THINKING_AS_CONTENT=true

# Optional: Auto switch from Pro to flash when you are getting rate-limited
ENABLE_AUTO_MODEL_SWITCHING=true

# Optional: Gemini Moderation Settings
# Configure content safety thresholds. Valid values: BLOCK_NONE, BLOCK_FEW, BLOCK_SOME, BLOCK_ONLY_HIGH, HARM_BLOCK_THRESHOLD_UNSPECIFIED
# Example: BLOCK_NONE will disable blocking for that category.
# GEMINI_MODERATION_HARASSMENT_THRESHOLD=BLOCK_NONE
# GEMINI_MODERATION_HATE_SPEECH_THRESHOLD=BLOCK_NONE
# GEMINI_MODERATION_SEXUALLY_EXPLICIT_THRESHOLD=BLOCK_NONE
# GEMINI_MODERATION_DANGEROUS_CONTENT_THRESHOLD=BLOCK_NONE