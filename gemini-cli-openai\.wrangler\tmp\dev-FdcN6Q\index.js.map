{"version": 3, "sources": ["../../../node_modules/unenv/dist/runtime/_internal/utils.mjs", "../../../node_modules/unenv/dist/runtime/node/internal/perf_hooks/performance.mjs", "../../../node_modules/@cloudflare/unenv-preset/dist/runtime/polyfill/performance.mjs", "../../../node_modules/unenv/dist/runtime/node/console.mjs", "../../../node_modules/unenv/dist/runtime/mock/noop.mjs", "../../../node_modules/@cloudflare/unenv-preset/dist/runtime/node/console.mjs", "../../../node_modules/wrangler/_virtual_unenv_global_polyfill-@cloudflare-unenv-preset-node-console", "../../../node_modules/unenv/dist/runtime/node/internal/process/hrtime.mjs", "../../../node_modules/unenv/dist/runtime/node/internal/process/process.mjs", "../../../node_modules/unenv/dist/runtime/node/internal/tty/write-stream.mjs", "../../../node_modules/unenv/dist/runtime/node/internal/tty/read-stream.mjs", "../../../node_modules/unenv/dist/runtime/node/internal/process/node-version.mjs", "../../../node_modules/@cloudflare/unenv-preset/dist/runtime/node/process.mjs", "../../../node_modules/wrangler/_virtual_unenv_global_polyfill-@cloudflare-unenv-preset-node-process", "../../../node_modules/hono/dist/compose.js", "../../../node_modules/hono/dist/request/constants.js", "../../../node_modules/hono/dist/utils/body.js", "../../../node_modules/hono/dist/utils/url.js", "../../../node_modules/hono/dist/request.js", "../../../node_modules/hono/dist/utils/html.js", "../../../node_modules/hono/dist/context.js", "../../../node_modules/hono/dist/router.js", "../../../node_modules/hono/dist/utils/constants.js", "../../../node_modules/hono/dist/hono-base.js", "../../../node_modules/hono/dist/router/reg-exp-router/node.js", "../../../node_modules/hono/dist/router/reg-exp-router/trie.js", "../../../node_modules/hono/dist/router/reg-exp-router/router.js", "../../../node_modules/hono/dist/router/smart-router/router.js", "../../../node_modules/hono/dist/router/trie-router/node.js", "../../../node_modules/hono/dist/router/trie-router/router.js", "../../../node_modules/hono/dist/hono.js", "../../../src/models.ts", "../../../src/config.ts", "../../../src/constants.ts", "../../../src/auth.ts", "../../../src/utils/image-utils.ts", "../../../src/helpers/generation-config-validator.ts", "../../../src/helpers/auto-model-switching.ts", "../../../src/gemini-client.ts", "../../../src/stream-transformer.ts", "../../../src/routes/openai.ts", "../../../src/routes/debug.ts", "../../../src/middlewares/auth.ts", "../../../src/middlewares/logging.ts", "../../../src/index.ts", "../../../node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-jKQFiY/middleware-insertion-facade.js", "../../../node_modules/wrangler/templates/middleware/common.ts", "../bundle-jKQFiY/middleware-loader.entry.ts"], "sourceRoot": "I:\\gemini\\gemini-cli-openai\\.wrangler\\tmp\\dev-FdcN6Q", "sourcesContent": ["/* @__NO_SIDE_EFFECTS__ */\nexport function rawHeaders(headers) {\n\tconst rawHeaders = [];\n\tfor (const key in headers) {\n\t\tif (Array.isArray(headers[key])) {\n\t\t\tfor (const h of headers[key]) {\n\t\t\t\trawHeaders.push(key, h);\n\t\t\t}\n\t\t} else {\n\t\t\trawHeaders.push(key, headers[key]);\n\t\t}\n\t}\n\treturn rawHeaders;\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function mergeFns(...functions) {\n\treturn function(...args) {\n\t\tfor (const fn of functions) {\n\t\t\tfn(...args);\n\t\t}\n\t};\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function createNotImplementedError(name) {\n\treturn new Error(`[unenv] ${name} is not implemented yet!`);\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function notImplemented(name) {\n\tconst fn = () => {\n\t\tthrow createNotImplementedError(name);\n\t};\n\treturn Object.assign(fn, { __unenv__: true });\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function notImplementedAsync(name) {\n\tconst fn = notImplemented(name);\n\tfn.__promisify__ = () => notImplemented(name + \".__promisify__\");\n\tfn.native = fn;\n\treturn fn;\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function notImplementedClass(name) {\n\treturn class {\n\t\t__unenv__ = true;\n\t\tconstructor() {\n\t\t\tthrow new Error(`[unenv] ${name} is not implemented yet!`);\n\t\t}\n\t};\n}\n", "import { createNotImplementedError } from \"../../../_internal/utils.mjs\";\nconst _timeOrigin = globalThis.performance?.timeOrigin ?? Date.now();\nconst _performanceNow = globalThis.performance?.now ? globalThis.performance.now.bind(globalThis.performance) : () => Date.now() - _timeOrigin;\nconst nodeTiming = {\n\tname: \"node\",\n\tentryType: \"node\",\n\tstartTime: 0,\n\tduration: 0,\n\tnodeStart: 0,\n\tv8Start: 0,\n\tbootstrapComplete: 0,\n\tenvironment: 0,\n\tloopStart: 0,\n\tloopExit: 0,\n\tidleTime: 0,\n\tuvMetricsInfo: {\n\t\tloopCount: 0,\n\t\tevents: 0,\n\t\teventsWaiting: 0\n\t},\n\tdetail: undefined,\n\ttoJSON() {\n\t\treturn this;\n\t}\n};\nexport class PerformanceEntry {\n\t__unenv__ = true;\n\tdetail;\n\tentryType = \"event\";\n\tname;\n\tstartTime;\n\tconstructor(name, options) {\n\t\tthis.name = name;\n\t\tthis.startTime = options?.startTime || _performanceNow();\n\t\tthis.detail = options?.detail;\n\t}\n\tget duration() {\n\t\treturn _performanceNow() - this.startTime;\n\t}\n\ttoJSON() {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tentryType: this.entryType,\n\t\t\tstartTime: this.startTime,\n\t\t\tduration: this.duration,\n\t\t\tdetail: this.detail\n\t\t};\n\t}\n}\nexport const PerformanceMark = class PerformanceMark extends PerformanceEntry {\n\tentryType = \"mark\";\n\tconstructor() {\n\t\tsuper(...arguments);\n\t}\n\tget duration() {\n\t\treturn 0;\n\t}\n};\nexport class PerformanceMeasure extends PerformanceEntry {\n\tentryType = \"measure\";\n}\nexport class PerformanceResourceTiming extends PerformanceEntry {\n\tentryType = \"resource\";\n\tserverTiming = [];\n\tconnectEnd = 0;\n\tconnectStart = 0;\n\tdecodedBodySize = 0;\n\tdomainLookupEnd = 0;\n\tdomainLookupStart = 0;\n\tencodedBodySize = 0;\n\tfetchStart = 0;\n\tinitiatorType = \"\";\n\tname = \"\";\n\tnextHopProtocol = \"\";\n\tredirectEnd = 0;\n\tredirectStart = 0;\n\trequestStart = 0;\n\tresponseEnd = 0;\n\tresponseStart = 0;\n\tsecureConnectionStart = 0;\n\tstartTime = 0;\n\ttransferSize = 0;\n\tworkerStart = 0;\n\tresponseStatus = 0;\n}\nexport class PerformanceObserverEntryList {\n\t__unenv__ = true;\n\tgetEntries() {\n\t\treturn [];\n\t}\n\tgetEntriesByName(_name, _type) {\n\t\treturn [];\n\t}\n\tgetEntriesByType(type) {\n\t\treturn [];\n\t}\n}\nexport class Performance {\n\t__unenv__ = true;\n\ttimeOrigin = _timeOrigin;\n\teventCounts = new Map();\n\t_entries = [];\n\t_resourceTimingBufferSize = 0;\n\tnavigation = undefined;\n\ttiming = undefined;\n\ttimerify(_fn, _options) {\n\t\tthrow createNotImplementedError(\"Performance.timerify\");\n\t}\n\tget nodeTiming() {\n\t\treturn nodeTiming;\n\t}\n\teventLoopUtilization() {\n\t\treturn {};\n\t}\n\tmarkResourceTiming() {\n\t\treturn new PerformanceResourceTiming(\"\");\n\t}\n\tonresourcetimingbufferfull = null;\n\tnow() {\n\t\tif (this.timeOrigin === _timeOrigin) {\n\t\t\treturn _performanceNow();\n\t\t}\n\t\treturn Date.now() - this.timeOrigin;\n\t}\n\tclearMarks(markName) {\n\t\tthis._entries = markName ? this._entries.filter((e) => e.name !== markName) : this._entries.filter((e) => e.entryType !== \"mark\");\n\t}\n\tclearMeasures(measureName) {\n\t\tthis._entries = measureName ? this._entries.filter((e) => e.name !== measureName) : this._entries.filter((e) => e.entryType !== \"measure\");\n\t}\n\tclearResourceTimings() {\n\t\tthis._entries = this._entries.filter((e) => e.entryType !== \"resource\" || e.entryType !== \"navigation\");\n\t}\n\tgetEntries() {\n\t\treturn this._entries;\n\t}\n\tgetEntriesByName(name, type) {\n\t\treturn this._entries.filter((e) => e.name === name && (!type || e.entryType === type));\n\t}\n\tgetEntriesByType(type) {\n\t\treturn this._entries.filter((e) => e.entryType === type);\n\t}\n\tmark(name, options) {\n\t\tconst entry = new PerformanceMark(name, options);\n\t\tthis._entries.push(entry);\n\t\treturn entry;\n\t}\n\tmeasure(measureName, startOrMeasureOptions, endMark) {\n\t\tlet start;\n\t\tlet end;\n\t\tif (typeof startOrMeasureOptions === \"string\") {\n\t\t\tstart = this.getEntriesByName(startOrMeasureOptions, \"mark\")[0]?.startTime;\n\t\t\tend = this.getEntriesByName(endMark, \"mark\")[0]?.startTime;\n\t\t} else {\n\t\t\tstart = Number.parseFloat(startOrMeasureOptions?.start) || this.now();\n\t\t\tend = Number.parseFloat(startOrMeasureOptions?.end) || this.now();\n\t\t}\n\t\tconst entry = new PerformanceMeasure(measureName, {\n\t\t\tstartTime: start,\n\t\t\tdetail: {\n\t\t\t\tstart,\n\t\t\t\tend\n\t\t\t}\n\t\t});\n\t\tthis._entries.push(entry);\n\t\treturn entry;\n\t}\n\tsetResourceTimingBufferSize(maxSize) {\n\t\tthis._resourceTimingBufferSize = maxSize;\n\t}\n\taddEventListener(type, listener, options) {\n\t\tthrow createNotImplementedError(\"Performance.addEventListener\");\n\t}\n\tremoveEventListener(type, listener, options) {\n\t\tthrow createNotImplementedError(\"Performance.removeEventListener\");\n\t}\n\tdispatchEvent(event) {\n\t\tthrow createNotImplementedError(\"Performance.dispatchEvent\");\n\t}\n\ttoJSON() {\n\t\treturn this;\n\t}\n}\nexport class PerformanceObserver {\n\t__unenv__ = true;\n\tstatic supportedEntryTypes = [];\n\t_callback = null;\n\tconstructor(callback) {\n\t\tthis._callback = callback;\n\t}\n\ttakeRecords() {\n\t\treturn [];\n\t}\n\tdisconnect() {\n\t\tthrow createNotImplementedError(\"PerformanceObserver.disconnect\");\n\t}\n\tobserve(options) {\n\t\tthrow createNotImplementedError(\"PerformanceObserver.observe\");\n\t}\n\tbind(fn) {\n\t\treturn fn;\n\t}\n\trunInAsyncScope(fn, thisArg, ...args) {\n\t\treturn fn.call(thisArg, ...args);\n\t}\n\tasyncId() {\n\t\treturn 0;\n\t}\n\ttriggerAsyncId() {\n\t\treturn 0;\n\t}\n\temitDestroy() {\n\t\treturn this;\n\t}\n}\nexport const performance = globalThis.performance && \"addEventListener\" in globalThis.performance ? globalThis.performance : new Performance();\n", "import {\n  performance,\n  Performance,\n  PerformanceEntry,\n  PerformanceMark,\n  PerformanceMeasure,\n  PerformanceObserver,\n  PerformanceObserverEntryList,\n  PerformanceResourceTiming\n} from \"node:perf_hooks\";\nglobalThis.performance = performance;\nglobalThis.Performance = Performance;\nglobalThis.PerformanceEntry = PerformanceEntry;\nglobalThis.PerformanceMark = PerformanceMark;\nglobalThis.PerformanceMeasure = PerformanceMeasure;\nglobalThis.PerformanceObserver = PerformanceObserver;\nglobalThis.PerformanceObserverEntryList = PerformanceObserverEntryList;\nglobalThis.PerformanceResourceTiming = PerformanceResourceTiming;\n", "import { Writable } from \"node:stream\";\nimport noop from \"../mock/noop.mjs\";\nimport { notImplemented, notImplementedClass } from \"../_internal/utils.mjs\";\nconst _console = globalThis.console;\nexport const _ignoreErrors = true;\nexport const _stderr = new Writable();\nexport const _stdout = new Writable();\nexport const log = _console?.log ?? noop;\nexport const info = _console?.info ?? log;\nexport const trace = _console?.trace ?? info;\nexport const debug = _console?.debug ?? log;\nexport const table = _console?.table ?? log;\nexport const error = _console?.error ?? log;\nexport const warn = _console?.warn ?? error;\nexport const createTask = _console?.createTask ?? /* @__PURE__ */ notImplemented(\"console.createTask\");\nexport const assert = /* @__PURE__ */ notImplemented(\"console.assert\");\nexport const clear = _console?.clear ?? noop;\nexport const count = _console?.count ?? noop;\nexport const countReset = _console?.countReset ?? noop;\nexport const dir = _console?.dir ?? noop;\nexport const dirxml = _console?.dirxml ?? noop;\nexport const group = _console?.group ?? noop;\nexport const groupEnd = _console?.groupEnd ?? noop;\nexport const groupCollapsed = _console?.groupCollapsed ?? noop;\nexport const profile = _console?.profile ?? noop;\nexport const profileEnd = _console?.profileEnd ?? noop;\nexport const time = _console?.time ?? noop;\nexport const timeEnd = _console?.timeEnd ?? noop;\nexport const timeLog = _console?.timeLog ?? noop;\nexport const timeStamp = _console?.timeStamp ?? noop;\nexport const Console = _console?.Console ?? /* @__PURE__ */ notImplementedClass(\"console.Console\");\nexport const _times = /* @__PURE__ */ new Map();\nexport function context() {\n\treturn _console;\n}\nexport const _stdoutErrorHandler = noop;\nexport const _stderrErrorHandler = noop;\nexport default {\n\t_times,\n\t_ignoreErrors,\n\t_stdoutErrorHandler,\n\t_stderrErrorHandler,\n\t_stdout,\n\t_stderr,\n\tassert,\n\tclear,\n\tConsole,\n\tcount,\n\tcountReset,\n\tdebug,\n\tdir,\n\tdirxml,\n\terror,\n\tcontext,\n\tcreateTask,\n\tgroup,\n\tgroupEnd,\n\tgroupCollapsed,\n\tinfo,\n\tlog,\n\tprofile,\n\tprofileEnd,\n\ttable,\n\ttime,\n\ttimeEnd,\n\ttimeLog,\n\ttimeStamp,\n\ttrace,\n\twarn\n};\n", "export default Object.assign(() => {}, { __unenv__: true });\n", "import {\n  _ignoreErrors,\n  _stderr,\n  _stderr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  _stdout,\n  _stdoutError<PERSON>and<PERSON>,\n  _times,\n  Console\n} from \"unenv/node/console\";\nexport {\n  Console,\n  _ignoreErrors,\n  _stderr,\n  _stderrError<PERSON><PERSON><PERSON>,\n  _stdout,\n  _stdoutErrorHandler,\n  _times\n} from \"unenv/node/console\";\nconst workerdConsole = globalThis[\"console\"];\nexport const {\n  assert,\n  clear,\n  // @ts-expect-error undocumented public API\n  context,\n  count,\n  countReset,\n  // @ts-expect-error undocumented public API\n  createTask,\n  debug,\n  dir,\n  dirxml,\n  error,\n  group,\n  groupCollapsed,\n  groupEnd,\n  info,\n  log,\n  profile,\n  profileEnd,\n  table,\n  time,\n  timeEnd,\n  timeLog,\n  timeStamp,\n  trace,\n  warn\n} = workerdConsole;\nObject.assign(workerdConsole, {\n  Console,\n  _ignoreErrors,\n  _stderr,\n  _stderrError<PERSON>and<PERSON>,\n  _stdout,\n  _stdoutErrorHandler,\n  _times\n});\nexport default workerdConsole;\n", "import { default as defaultExport } from \"@cloudflare/unenv-preset/node/console\";\nglobalThis.console = defaultExport;", "export const hrtime = /* @__PURE__ */ Object.assign(function hrtime(startTime) {\n\tconst now = Date.now();\n\tconst seconds = Math.trunc(now / 1e3);\n\tconst nanos = now % 1e3 * 1e6;\n\tif (startTime) {\n\t\tlet diffSeconds = seconds - startTime[0];\n\t\tlet diffNanos = nanos - startTime[0];\n\t\tif (diffNanos < 0) {\n\t\t\tdiffSeconds = diffSeconds - 1;\n\t\t\tdiffNanos = 1e9 + diffNanos;\n\t\t}\n\t\treturn [diffSeconds, diffNanos];\n\t}\n\treturn [seconds, nanos];\n}, { bigint: function bigint() {\n\treturn BigInt(Date.now() * 1e6);\n} });\n", "import { EventEmitter } from \"node:events\";\nimport { ReadStream, WriteStream } from \"node:tty\";\nimport { notImplemented, createNotImplementedError } from \"../../../_internal/utils.mjs\";\nimport { NODE_VERSION } from \"./node-version.mjs\";\nexport class Process extends EventEmitter {\n\tenv;\n\thrtime;\n\tnextTick;\n\tconstructor(impl) {\n\t\tsuper();\n\t\tthis.env = impl.env;\n\t\tthis.hrtime = impl.hrtime;\n\t\tthis.nextTick = impl.nextTick;\n\t\tfor (const prop of [...Object.getOwnPropertyNames(Process.prototype), ...Object.getOwnPropertyNames(EventEmitter.prototype)]) {\n\t\t\tconst value = this[prop];\n\t\t\tif (typeof value === \"function\") {\n\t\t\t\tthis[prop] = value.bind(this);\n\t\t\t}\n\t\t}\n\t}\n\temitWarning(warning, type, code) {\n\t\tconsole.warn(`${code ? `[${code}] ` : \"\"}${type ? `${type}: ` : \"\"}${warning}`);\n\t}\n\temit(...args) {\n\t\treturn super.emit(...args);\n\t}\n\tlisteners(eventName) {\n\t\treturn super.listeners(eventName);\n\t}\n\t#stdin;\n\t#stdout;\n\t#stderr;\n\tget stdin() {\n\t\treturn this.#stdin ??= new ReadStream(0);\n\t}\n\tget stdout() {\n\t\treturn this.#stdout ??= new WriteStream(1);\n\t}\n\tget stderr() {\n\t\treturn this.#stderr ??= new WriteStream(2);\n\t}\n\t#cwd = \"/\";\n\tchdir(cwd) {\n\t\tthis.#cwd = cwd;\n\t}\n\tcwd() {\n\t\treturn this.#cwd;\n\t}\n\tarch = \"\";\n\tplatform = \"\";\n\targv = [];\n\targv0 = \"\";\n\texecArgv = [];\n\texecPath = \"\";\n\ttitle = \"\";\n\tpid = 200;\n\tppid = 100;\n\tget version() {\n\t\treturn `v${NODE_VERSION}`;\n\t}\n\tget versions() {\n\t\treturn { node: NODE_VERSION };\n\t}\n\tget allowedNodeEnvironmentFlags() {\n\t\treturn new Set();\n\t}\n\tget sourceMapsEnabled() {\n\t\treturn false;\n\t}\n\tget debugPort() {\n\t\treturn 0;\n\t}\n\tget throwDeprecation() {\n\t\treturn false;\n\t}\n\tget traceDeprecation() {\n\t\treturn false;\n\t}\n\tget features() {\n\t\treturn {};\n\t}\n\tget release() {\n\t\treturn {};\n\t}\n\tget connected() {\n\t\treturn false;\n\t}\n\tget config() {\n\t\treturn {};\n\t}\n\tget moduleLoadList() {\n\t\treturn [];\n\t}\n\tconstrainedMemory() {\n\t\treturn 0;\n\t}\n\tavailableMemory() {\n\t\treturn 0;\n\t}\n\tuptime() {\n\t\treturn 0;\n\t}\n\tresourceUsage() {\n\t\treturn {};\n\t}\n\tref() {}\n\tunref() {}\n\tumask() {\n\t\tthrow createNotImplementedError(\"process.umask\");\n\t}\n\tgetBuiltinModule() {\n\t\treturn undefined;\n\t}\n\tgetActiveResourcesInfo() {\n\t\tthrow createNotImplementedError(\"process.getActiveResourcesInfo\");\n\t}\n\texit() {\n\t\tthrow createNotImplementedError(\"process.exit\");\n\t}\n\treallyExit() {\n\t\tthrow createNotImplementedError(\"process.reallyExit\");\n\t}\n\tkill() {\n\t\tthrow createNotImplementedError(\"process.kill\");\n\t}\n\tabort() {\n\t\tthrow createNotImplementedError(\"process.abort\");\n\t}\n\tdlopen() {\n\t\tthrow createNotImplementedError(\"process.dlopen\");\n\t}\n\tsetSourceMapsEnabled() {\n\t\tthrow createNotImplementedError(\"process.setSourceMapsEnabled\");\n\t}\n\tloadEnvFile() {\n\t\tthrow createNotImplementedError(\"process.loadEnvFile\");\n\t}\n\tdisconnect() {\n\t\tthrow createNotImplementedError(\"process.disconnect\");\n\t}\n\tcpuUsage() {\n\t\tthrow createNotImplementedError(\"process.cpuUsage\");\n\t}\n\tsetUncaughtExceptionCaptureCallback() {\n\t\tthrow createNotImplementedError(\"process.setUncaughtExceptionCaptureCallback\");\n\t}\n\thasUncaughtExceptionCaptureCallback() {\n\t\tthrow createNotImplementedError(\"process.hasUncaughtExceptionCaptureCallback\");\n\t}\n\tinitgroups() {\n\t\tthrow createNotImplementedError(\"process.initgroups\");\n\t}\n\topenStdin() {\n\t\tthrow createNotImplementedError(\"process.openStdin\");\n\t}\n\tassert() {\n\t\tthrow createNotImplementedError(\"process.assert\");\n\t}\n\tbinding() {\n\t\tthrow createNotImplementedError(\"process.binding\");\n\t}\n\tpermission = { has: /* @__PURE__ */ notImplemented(\"process.permission.has\") };\n\treport = {\n\t\tdirectory: \"\",\n\t\tfilename: \"\",\n\t\tsignal: \"SIGUSR2\",\n\t\tcompact: false,\n\t\treportOnFatalError: false,\n\t\treportOnSignal: false,\n\t\treportOnUncaughtException: false,\n\t\tgetReport: /* @__PURE__ */ notImplemented(\"process.report.getReport\"),\n\t\twriteReport: /* @__PURE__ */ notImplemented(\"process.report.writeReport\")\n\t};\n\tfinalization = {\n\t\tregister: /* @__PURE__ */ notImplemented(\"process.finalization.register\"),\n\t\tunregister: /* @__PURE__ */ notImplemented(\"process.finalization.unregister\"),\n\t\tregisterBeforeExit: /* @__PURE__ */ notImplemented(\"process.finalization.registerBeforeExit\")\n\t};\n\tmemoryUsage = Object.assign(() => ({\n\t\tarrayBuffers: 0,\n\t\trss: 0,\n\t\texternal: 0,\n\t\theapTotal: 0,\n\t\theapUsed: 0\n\t}), { rss: () => 0 });\n\tmainModule = undefined;\n\tdomain = undefined;\n\tsend = undefined;\n\texitCode = undefined;\n\tchannel = undefined;\n\tgetegid = undefined;\n\tgeteuid = undefined;\n\tgetgid = undefined;\n\tgetgroups = undefined;\n\tgetuid = undefined;\n\tsetegid = undefined;\n\tseteuid = undefined;\n\tsetgid = undefined;\n\tsetgroups = undefined;\n\tsetuid = undefined;\n\t_events = undefined;\n\t_eventsCount = undefined;\n\t_exiting = undefined;\n\t_maxListeners = undefined;\n\t_debugEnd = undefined;\n\t_debugProcess = undefined;\n\t_fatalException = undefined;\n\t_getActiveHandles = undefined;\n\t_getActiveRequests = undefined;\n\t_kill = undefined;\n\t_preload_modules = undefined;\n\t_rawDebug = undefined;\n\t_startProfilerIdleNotifier = undefined;\n\t_stopProfilerIdleNotifier = undefined;\n\t_tickCallback = undefined;\n\t_disconnect = undefined;\n\t_handleQueue = undefined;\n\t_pendingMessage = undefined;\n\t_channel = undefined;\n\t_send = undefined;\n\t_linkedBinding = undefined;\n}\n", "export class WriteStream {\n\tfd;\n\tcolumns = 80;\n\trows = 24;\n\tisTTY = false;\n\tconstructor(fd) {\n\t\tthis.fd = fd;\n\t}\n\tclearLine(dir, callback) {\n\t\tcallback && callback();\n\t\treturn false;\n\t}\n\tclearScreenDown(callback) {\n\t\tcallback && callback();\n\t\treturn false;\n\t}\n\tcursorTo(x, y, callback) {\n\t\tcallback && typeof callback === \"function\" && callback();\n\t\treturn false;\n\t}\n\tmoveCursor(dx, dy, callback) {\n\t\tcallback && callback();\n\t\treturn false;\n\t}\n\tgetColorDepth(env) {\n\t\treturn 1;\n\t}\n\thasColors(count, env) {\n\t\treturn false;\n\t}\n\tgetWindowSize() {\n\t\treturn [this.columns, this.rows];\n\t}\n\twrite(str, encoding, cb) {\n\t\tif (str instanceof Uint8Array) {\n\t\t\tstr = new TextDecoder().decode(str);\n\t\t}\n\t\ttry {\n\t\t\tconsole.log(str);\n\t\t} catch {}\n\t\tcb && typeof cb === \"function\" && cb();\n\t\treturn false;\n\t}\n}\n", "export class ReadStream {\n\tfd;\n\tisRaw = false;\n\tisTTY = false;\n\tconstructor(fd) {\n\t\tthis.fd = fd;\n\t}\n\tsetRawMode(mode) {\n\t\tthis.isRaw = mode;\n\t\treturn this;\n\t}\n}\n", "export const NODE_VERSION = \"22.14.0\";\n", "import { hrtime as UnenvHrTime } from \"unenv/node/internal/process/hrtime\";\nimport { Process as UnenvProcess } from \"unenv/node/internal/process/process\";\nconst globalProcess = globalThis[\"process\"];\nexport const getBuiltinModule = globalProcess.getBuiltinModule;\nexport const { exit, platform, nextTick } = getBuiltinModule(\n  \"node:process\"\n);\nconst unenvProcess = new UnenvProcess({\n  env: globalProcess.env,\n  hrtime: UnenvHrTime,\n  nextTick\n});\nexport const {\n  abort,\n  addListener,\n  allowedNodeEnvironmentFlags,\n  hasUncaughtExceptionCaptureCallback,\n  setUncaughtExceptionCaptureCallback,\n  loadEnvFile,\n  sourceMapsEnabled,\n  arch,\n  argv,\n  argv0,\n  chdir,\n  config,\n  connected,\n  constrainedMemory,\n  availableMemory,\n  cpuUsage,\n  cwd,\n  debugPort,\n  dlopen,\n  disconnect,\n  emit,\n  emitWarning,\n  env,\n  eventNames,\n  execArgv,\n  execPath,\n  finalization,\n  features,\n  getActiveResourcesInfo,\n  getMaxListeners,\n  hrtime,\n  kill,\n  listeners,\n  listenerCount,\n  memoryUsage,\n  on,\n  off,\n  once,\n  pid,\n  ppid,\n  prependListener,\n  prependOnceListener,\n  rawListeners,\n  release,\n  removeAllListeners,\n  removeListener,\n  report,\n  resourceUsage,\n  setMaxListeners,\n  setSourceMapsEnabled,\n  stderr,\n  stdin,\n  stdout,\n  title,\n  throwDeprecation,\n  traceDeprecation,\n  umask,\n  uptime,\n  version,\n  versions,\n  domain,\n  initgroups,\n  moduleLoadList,\n  reallyExit,\n  openStdin,\n  assert,\n  binding,\n  send,\n  exitCode,\n  channel,\n  getegid,\n  geteuid,\n  getgid,\n  getgroups,\n  getuid,\n  setegid,\n  seteuid,\n  setgid,\n  setgroups,\n  setuid,\n  permission,\n  mainModule,\n  _events,\n  _eventsCount,\n  _exiting,\n  _maxListeners,\n  _debugEnd,\n  _debugProcess,\n  _fatalException,\n  _getActiveHandles,\n  _getActiveRequests,\n  _kill,\n  _preload_modules,\n  _rawDebug,\n  _startProfilerIdleNotifier,\n  _stopProfilerIdleNotifier,\n  _tickCallback,\n  _disconnect,\n  _handleQueue,\n  _pendingMessage,\n  _channel,\n  _send,\n  _linkedBinding\n} = unenvProcess;\nconst _process = {\n  abort,\n  addListener,\n  allowedNodeEnvironmentFlags,\n  hasUncaughtExceptionCaptureCallback,\n  setUncaughtExceptionCaptureCallback,\n  loadEnvFile,\n  sourceMapsEnabled,\n  arch,\n  argv,\n  argv0,\n  chdir,\n  config,\n  connected,\n  constrainedMemory,\n  availableMemory,\n  cpuUsage,\n  cwd,\n  debugPort,\n  dlopen,\n  disconnect,\n  emit,\n  emitWarning,\n  env,\n  eventNames,\n  execArgv,\n  execPath,\n  exit,\n  finalization,\n  features,\n  getBuiltinModule,\n  getActiveResourcesInfo,\n  getMaxListeners,\n  hrtime,\n  kill,\n  listeners,\n  listenerCount,\n  memoryUsage,\n  nextTick,\n  on,\n  off,\n  once,\n  pid,\n  platform,\n  ppid,\n  prependListener,\n  prependOnceListener,\n  rawListeners,\n  release,\n  removeAllListeners,\n  removeListener,\n  report,\n  resourceUsage,\n  setMaxListeners,\n  setSourceMapsEnabled,\n  stderr,\n  stdin,\n  stdout,\n  title,\n  throwDeprecation,\n  traceDeprecation,\n  umask,\n  uptime,\n  version,\n  versions,\n  // @ts-expect-error old API\n  domain,\n  initgroups,\n  moduleLoadList,\n  reallyExit,\n  openStdin,\n  assert,\n  binding,\n  send,\n  exitCode,\n  channel,\n  getegid,\n  geteuid,\n  getgid,\n  getgroups,\n  getuid,\n  setegid,\n  seteuid,\n  setgid,\n  setgroups,\n  setuid,\n  permission,\n  mainModule,\n  _events,\n  _eventsCount,\n  _exiting,\n  _maxListeners,\n  _debugEnd,\n  _debugProcess,\n  _fatalException,\n  _getActiveHandles,\n  _getActiveRequests,\n  _kill,\n  _preload_modules,\n  _rawDebug,\n  _startProfilerIdleNotifier,\n  _stopProfilerIdleNotifier,\n  _tickCallback,\n  _disconnect,\n  _handleQueue,\n  _pendingMessage,\n  _channel,\n  _send,\n  _linkedBinding\n};\nexport default _process;\n", "import { default as defaultExport } from \"@cloudflare/unenv-preset/node/process\";\nglobalThis.process = defaultExport;", "// src/compose.ts\nvar compose = (middleware, onError, onNotFound) => {\n  return (context, next) => {\n    let index = -1;\n    return dispatch(0);\n    async function dispatch(i) {\n      if (i <= index) {\n        throw new Error(\"next() called multiple times\");\n      }\n      index = i;\n      let res;\n      let isError = false;\n      let handler;\n      if (middleware[i]) {\n        handler = middleware[i][0][0];\n        context.req.routeIndex = i;\n      } else {\n        handler = i === middleware.length && next || void 0;\n      }\n      if (handler) {\n        try {\n          res = await handler(context, () => dispatch(i + 1));\n        } catch (err) {\n          if (err instanceof Error && onError) {\n            context.error = err;\n            res = await onError(err, context);\n            isError = true;\n          } else {\n            throw err;\n          }\n        }\n      } else {\n        if (context.finalized === false && onNotFound) {\n          res = await onNotFound(context);\n        }\n      }\n      if (res && (context.finalized === false || isError)) {\n        context.res = res;\n      }\n      return context;\n    }\n  };\n};\nexport {\n  compose\n};\n", "// src/request/constants.ts\nvar GET_MATCH_RESULT = Symbol();\nexport {\n  GET_MATCH_RESULT\n};\n", "// src/utils/body.ts\nimport { HonoRequest } from \"../request.js\";\nvar parseBody = async (request, options = /* @__PURE__ */ Object.create(null)) => {\n  const { all = false, dot = false } = options;\n  const headers = request instanceof HonoRequest ? request.raw.headers : request.headers;\n  const contentType = headers.get(\"Content-Type\");\n  if (contentType?.startsWith(\"multipart/form-data\") || contentType?.startsWith(\"application/x-www-form-urlencoded\")) {\n    return parseFormData(request, { all, dot });\n  }\n  return {};\n};\nasync function parseFormData(request, options) {\n  const formData = await request.formData();\n  if (formData) {\n    return convertFormDataToBodyData(formData, options);\n  }\n  return {};\n}\nfunction convertFormDataToBodyData(formData, options) {\n  const form = /* @__PURE__ */ Object.create(null);\n  formData.forEach((value, key) => {\n    const shouldParseAllValues = options.all || key.endsWith(\"[]\");\n    if (!shouldParseAllValues) {\n      form[key] = value;\n    } else {\n      handleParsingAllValues(form, key, value);\n    }\n  });\n  if (options.dot) {\n    Object.entries(form).forEach(([key, value]) => {\n      const shouldParseDotValues = key.includes(\".\");\n      if (shouldParseDotValues) {\n        handleParsingNestedValues(form, key, value);\n        delete form[key];\n      }\n    });\n  }\n  return form;\n}\nvar handleParsingAllValues = (form, key, value) => {\n  if (form[key] !== void 0) {\n    if (Array.isArray(form[key])) {\n      ;\n      form[key].push(value);\n    } else {\n      form[key] = [form[key], value];\n    }\n  } else {\n    if (!key.endsWith(\"[]\")) {\n      form[key] = value;\n    } else {\n      form[key] = [value];\n    }\n  }\n};\nvar handleParsingNestedValues = (form, key, value) => {\n  let nestedForm = form;\n  const keys = key.split(\".\");\n  keys.forEach((key2, index) => {\n    if (index === keys.length - 1) {\n      nestedForm[key2] = value;\n    } else {\n      if (!nestedForm[key2] || typeof nestedForm[key2] !== \"object\" || Array.isArray(nestedForm[key2]) || nestedForm[key2] instanceof File) {\n        nestedForm[key2] = /* @__PURE__ */ Object.create(null);\n      }\n      nestedForm = nestedForm[key2];\n    }\n  });\n};\nexport {\n  parseBody\n};\n", "// src/utils/url.ts\nvar splitPath = (path) => {\n  const paths = path.split(\"/\");\n  if (paths[0] === \"\") {\n    paths.shift();\n  }\n  return paths;\n};\nvar splitRoutingPath = (routePath) => {\n  const { groups, path } = extractGroupsFromPath(routePath);\n  const paths = splitPath(path);\n  return replaceGroupMarks(paths, groups);\n};\nvar extractGroupsFromPath = (path) => {\n  const groups = [];\n  path = path.replace(/\\{[^}]+\\}/g, (match, index) => {\n    const mark = `@${index}`;\n    groups.push([mark, match]);\n    return mark;\n  });\n  return { groups, path };\n};\nvar replaceGroupMarks = (paths, groups) => {\n  for (let i = groups.length - 1; i >= 0; i--) {\n    const [mark] = groups[i];\n    for (let j = paths.length - 1; j >= 0; j--) {\n      if (paths[j].includes(mark)) {\n        paths[j] = paths[j].replace(mark, groups[i][1]);\n        break;\n      }\n    }\n  }\n  return paths;\n};\nvar patternCache = {};\nvar getPattern = (label, next) => {\n  if (label === \"*\") {\n    return \"*\";\n  }\n  const match = label.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n  if (match) {\n    const cacheKey = `${label}#${next}`;\n    if (!patternCache[cacheKey]) {\n      if (match[2]) {\n        patternCache[cacheKey] = next && next[0] !== \":\" && next[0] !== \"*\" ? [cacheKey, match[1], new RegExp(`^${match[2]}(?=/${next})`)] : [label, match[1], new RegExp(`^${match[2]}$`)];\n      } else {\n        patternCache[cacheKey] = [label, match[1], true];\n      }\n    }\n    return patternCache[cacheKey];\n  }\n  return null;\n};\nvar tryDecode = (str, decoder) => {\n  try {\n    return decoder(str);\n  } catch {\n    return str.replace(/(?:%[0-9A-Fa-f]{2})+/g, (match) => {\n      try {\n        return decoder(match);\n      } catch {\n        return match;\n      }\n    });\n  }\n};\nvar tryDecodeURI = (str) => tryDecode(str, decodeURI);\nvar getPath = (request) => {\n  const url = request.url;\n  const start = url.indexOf(\n    \"/\",\n    url.charCodeAt(9) === 58 ? 13 : 8\n  );\n  let i = start;\n  for (; i < url.length; i++) {\n    const charCode = url.charCodeAt(i);\n    if (charCode === 37) {\n      const queryIndex = url.indexOf(\"?\", i);\n      const path = url.slice(start, queryIndex === -1 ? void 0 : queryIndex);\n      return tryDecodeURI(path.includes(\"%25\") ? path.replace(/%25/g, \"%2525\") : path);\n    } else if (charCode === 63) {\n      break;\n    }\n  }\n  return url.slice(start, i);\n};\nvar getQueryStrings = (url) => {\n  const queryIndex = url.indexOf(\"?\", 8);\n  return queryIndex === -1 ? \"\" : \"?\" + url.slice(queryIndex + 1);\n};\nvar getPathNoStrict = (request) => {\n  const result = getPath(request);\n  return result.length > 1 && result.at(-1) === \"/\" ? result.slice(0, -1) : result;\n};\nvar mergePath = (base, sub, ...rest) => {\n  if (rest.length) {\n    sub = mergePath(sub, ...rest);\n  }\n  return `${base?.[0] === \"/\" ? \"\" : \"/\"}${base}${sub === \"/\" ? \"\" : `${base?.at(-1) === \"/\" ? \"\" : \"/\"}${sub?.[0] === \"/\" ? sub.slice(1) : sub}`}`;\n};\nvar checkOptionalParameter = (path) => {\n  if (path.charCodeAt(path.length - 1) !== 63 || !path.includes(\":\")) {\n    return null;\n  }\n  const segments = path.split(\"/\");\n  const results = [];\n  let basePath = \"\";\n  segments.forEach((segment) => {\n    if (segment !== \"\" && !/\\:/.test(segment)) {\n      basePath += \"/\" + segment;\n    } else if (/\\:/.test(segment)) {\n      if (/\\?/.test(segment)) {\n        if (results.length === 0 && basePath === \"\") {\n          results.push(\"/\");\n        } else {\n          results.push(basePath);\n        }\n        const optionalSegment = segment.replace(\"?\", \"\");\n        basePath += \"/\" + optionalSegment;\n        results.push(basePath);\n      } else {\n        basePath += \"/\" + segment;\n      }\n    }\n  });\n  return results.filter((v, i, a) => a.indexOf(v) === i);\n};\nvar _decodeURI = (value) => {\n  if (!/[%+]/.test(value)) {\n    return value;\n  }\n  if (value.indexOf(\"+\") !== -1) {\n    value = value.replace(/\\+/g, \" \");\n  }\n  return value.indexOf(\"%\") !== -1 ? tryDecode(value, decodeURIComponent_) : value;\n};\nvar _getQueryParam = (url, key, multiple) => {\n  let encoded;\n  if (!multiple && key && !/[%+]/.test(key)) {\n    let keyIndex2 = url.indexOf(`?${key}`, 8);\n    if (keyIndex2 === -1) {\n      keyIndex2 = url.indexOf(`&${key}`, 8);\n    }\n    while (keyIndex2 !== -1) {\n      const trailingKeyCode = url.charCodeAt(keyIndex2 + key.length + 1);\n      if (trailingKeyCode === 61) {\n        const valueIndex = keyIndex2 + key.length + 2;\n        const endIndex = url.indexOf(\"&\", valueIndex);\n        return _decodeURI(url.slice(valueIndex, endIndex === -1 ? void 0 : endIndex));\n      } else if (trailingKeyCode == 38 || isNaN(trailingKeyCode)) {\n        return \"\";\n      }\n      keyIndex2 = url.indexOf(`&${key}`, keyIndex2 + 1);\n    }\n    encoded = /[%+]/.test(url);\n    if (!encoded) {\n      return void 0;\n    }\n  }\n  const results = {};\n  encoded ??= /[%+]/.test(url);\n  let keyIndex = url.indexOf(\"?\", 8);\n  while (keyIndex !== -1) {\n    const nextKeyIndex = url.indexOf(\"&\", keyIndex + 1);\n    let valueIndex = url.indexOf(\"=\", keyIndex);\n    if (valueIndex > nextKeyIndex && nextKeyIndex !== -1) {\n      valueIndex = -1;\n    }\n    let name = url.slice(\n      keyIndex + 1,\n      valueIndex === -1 ? nextKeyIndex === -1 ? void 0 : nextKeyIndex : valueIndex\n    );\n    if (encoded) {\n      name = _decodeURI(name);\n    }\n    keyIndex = nextKeyIndex;\n    if (name === \"\") {\n      continue;\n    }\n    let value;\n    if (valueIndex === -1) {\n      value = \"\";\n    } else {\n      value = url.slice(valueIndex + 1, nextKeyIndex === -1 ? void 0 : nextKeyIndex);\n      if (encoded) {\n        value = _decodeURI(value);\n      }\n    }\n    if (multiple) {\n      if (!(results[name] && Array.isArray(results[name]))) {\n        results[name] = [];\n      }\n      ;\n      results[name].push(value);\n    } else {\n      results[name] ??= value;\n    }\n  }\n  return key ? results[key] : results;\n};\nvar getQueryParam = _getQueryParam;\nvar getQueryParams = (url, key) => {\n  return _getQueryParam(url, key, true);\n};\nvar decodeURIComponent_ = decodeURIComponent;\nexport {\n  checkOptionalParameter,\n  decodeURIComponent_,\n  getPath,\n  getPathNoStrict,\n  getPattern,\n  getQueryParam,\n  getQueryParams,\n  getQueryStrings,\n  mergePath,\n  splitPath,\n  splitRoutingPath,\n  tryDecode\n};\n", "// src/request.ts\nimport { GET_MATCH_RESULT } from \"./request/constants.js\";\nimport { parseBody } from \"./utils/body.js\";\nimport { decodeURIComponent_, getQueryParam, getQueryParams, tryDecode } from \"./utils/url.js\";\nvar tryDecodeURIComponent = (str) => tryDecode(str, decodeURIComponent_);\nvar HonoRequest = class {\n  raw;\n  #validatedData;\n  #matchResult;\n  routeIndex = 0;\n  path;\n  bodyCache = {};\n  constructor(request, path = \"/\", matchResult = [[]]) {\n    this.raw = request;\n    this.path = path;\n    this.#matchResult = matchResult;\n    this.#validatedData = {};\n  }\n  param(key) {\n    return key ? this.#getDecodedParam(key) : this.#getAllDecodedParams();\n  }\n  #getDecodedParam(key) {\n    const paramKey = this.#matchResult[0][this.routeIndex][1][key];\n    const param = this.#getParamValue(paramKey);\n    return param ? /\\%/.test(param) ? tryDecodeURIComponent(param) : param : void 0;\n  }\n  #getAllDecodedParams() {\n    const decoded = {};\n    const keys = Object.keys(this.#matchResult[0][this.routeIndex][1]);\n    for (const key of keys) {\n      const value = this.#getParamValue(this.#matchResult[0][this.routeIndex][1][key]);\n      if (value && typeof value === \"string\") {\n        decoded[key] = /\\%/.test(value) ? tryDecodeURIComponent(value) : value;\n      }\n    }\n    return decoded;\n  }\n  #getParamValue(paramKey) {\n    return this.#matchResult[1] ? this.#matchResult[1][paramKey] : paramKey;\n  }\n  query(key) {\n    return getQueryParam(this.url, key);\n  }\n  queries(key) {\n    return getQueryParams(this.url, key);\n  }\n  header(name) {\n    if (name) {\n      return this.raw.headers.get(name) ?? void 0;\n    }\n    const headerData = {};\n    this.raw.headers.forEach((value, key) => {\n      headerData[key] = value;\n    });\n    return headerData;\n  }\n  async parseBody(options) {\n    return this.bodyCache.parsedBody ??= await parseBody(this, options);\n  }\n  #cachedBody = (key) => {\n    const { bodyCache, raw } = this;\n    const cachedBody = bodyCache[key];\n    if (cachedBody) {\n      return cachedBody;\n    }\n    const anyCachedKey = Object.keys(bodyCache)[0];\n    if (anyCachedKey) {\n      return bodyCache[anyCachedKey].then((body) => {\n        if (anyCachedKey === \"json\") {\n          body = JSON.stringify(body);\n        }\n        return new Response(body)[key]();\n      });\n    }\n    return bodyCache[key] = raw[key]();\n  };\n  json() {\n    return this.#cachedBody(\"text\").then((text) => JSON.parse(text));\n  }\n  text() {\n    return this.#cachedBody(\"text\");\n  }\n  arrayBuffer() {\n    return this.#cachedBody(\"arrayBuffer\");\n  }\n  blob() {\n    return this.#cachedBody(\"blob\");\n  }\n  formData() {\n    return this.#cachedBody(\"formData\");\n  }\n  addValidatedData(target, data) {\n    this.#validatedData[target] = data;\n  }\n  valid(target) {\n    return this.#validatedData[target];\n  }\n  get url() {\n    return this.raw.url;\n  }\n  get method() {\n    return this.raw.method;\n  }\n  get [GET_MATCH_RESULT]() {\n    return this.#matchResult;\n  }\n  get matchedRoutes() {\n    return this.#matchResult[0].map(([[, route]]) => route);\n  }\n  get routePath() {\n    return this.#matchResult[0].map(([[, route]]) => route)[this.routeIndex].path;\n  }\n};\nexport {\n  HonoRequest\n};\n", "// src/utils/html.ts\nvar HtmlEscapedCallbackPhase = {\n  Stringify: 1,\n  BeforeStream: 2,\n  Stream: 3\n};\nvar raw = (value, callbacks) => {\n  const escapedString = new String(value);\n  escapedString.isEscaped = true;\n  escapedString.callbacks = callbacks;\n  return escapedString;\n};\nvar escapeRe = /[&<>'\"]/;\nvar stringBufferToString = async (buffer, callbacks) => {\n  let str = \"\";\n  callbacks ||= [];\n  const resolvedBuffer = await Promise.all(buffer);\n  for (let i = resolvedBuffer.length - 1; ; i--) {\n    str += resolvedBuffer[i];\n    i--;\n    if (i < 0) {\n      break;\n    }\n    let r = resolvedBuffer[i];\n    if (typeof r === \"object\") {\n      callbacks.push(...r.callbacks || []);\n    }\n    const isEscaped = r.isEscaped;\n    r = await (typeof r === \"object\" ? r.toString() : r);\n    if (typeof r === \"object\") {\n      callbacks.push(...r.callbacks || []);\n    }\n    if (r.isEscaped ?? isEscaped) {\n      str += r;\n    } else {\n      const buf = [str];\n      escapeToBuffer(r, buf);\n      str = buf[0];\n    }\n  }\n  return raw(str, callbacks);\n};\nvar escapeToBuffer = (str, buffer) => {\n  const match = str.search(escapeRe);\n  if (match === -1) {\n    buffer[0] += str;\n    return;\n  }\n  let escape;\n  let index;\n  let lastIndex = 0;\n  for (index = match; index < str.length; index++) {\n    switch (str.charCodeAt(index)) {\n      case 34:\n        escape = \"&quot;\";\n        break;\n      case 39:\n        escape = \"&#39;\";\n        break;\n      case 38:\n        escape = \"&amp;\";\n        break;\n      case 60:\n        escape = \"&lt;\";\n        break;\n      case 62:\n        escape = \"&gt;\";\n        break;\n      default:\n        continue;\n    }\n    buffer[0] += str.substring(lastIndex, index) + escape;\n    lastIndex = index + 1;\n  }\n  buffer[0] += str.substring(lastIndex, index);\n};\nvar resolveCallbackSync = (str) => {\n  const callbacks = str.callbacks;\n  if (!callbacks?.length) {\n    return str;\n  }\n  const buffer = [str];\n  const context = {};\n  callbacks.forEach((c) => c({ phase: HtmlEscapedCallbackPhase.Stringify, buffer, context }));\n  return buffer[0];\n};\nvar resolveCallback = async (str, phase, preserveCallbacks, context, buffer) => {\n  if (typeof str === \"object\" && !(str instanceof String)) {\n    if (!(str instanceof Promise)) {\n      str = str.toString();\n    }\n    if (str instanceof Promise) {\n      str = await str;\n    }\n  }\n  const callbacks = str.callbacks;\n  if (!callbacks?.length) {\n    return Promise.resolve(str);\n  }\n  if (buffer) {\n    buffer[0] += str;\n  } else {\n    buffer = [str];\n  }\n  const resStr = Promise.all(callbacks.map((c) => c({ phase, buffer, context }))).then(\n    (res) => Promise.all(\n      res.filter(Boolean).map((str2) => resolveCallback(str2, phase, false, context, buffer))\n    ).then(() => buffer[0])\n  );\n  if (preserveCallbacks) {\n    return raw(await resStr, callbacks);\n  } else {\n    return resStr;\n  }\n};\nexport {\n  HtmlEscapedCallbackPhase,\n  escapeToBuffer,\n  raw,\n  resolveCallback,\n  resolveCallbackSync,\n  stringBufferToString\n};\n", "// src/context.ts\nimport { HonoRequest } from \"./request.js\";\nimport { HtmlEscapedCallbackPhase, resolveCallback } from \"./utils/html.js\";\nvar TEXT_PLAIN = \"text/plain; charset=UTF-8\";\nvar setDefaultContentType = (contentType, headers) => {\n  return {\n    \"Content-Type\": contentType,\n    ...headers\n  };\n};\nvar Context = class {\n  #rawRequest;\n  #req;\n  env = {};\n  #var;\n  finalized = false;\n  error;\n  #status;\n  #executionCtx;\n  #res;\n  #layout;\n  #renderer;\n  #notFoundHandler;\n  #preparedHeaders;\n  #matchResult;\n  #path;\n  constructor(req, options) {\n    this.#rawRequest = req;\n    if (options) {\n      this.#executionCtx = options.executionCtx;\n      this.env = options.env;\n      this.#notFoundHandler = options.notFoundHandler;\n      this.#path = options.path;\n      this.#matchResult = options.matchResult;\n    }\n  }\n  get req() {\n    this.#req ??= new HonoRequest(this.#rawRequest, this.#path, this.#matchResult);\n    return this.#req;\n  }\n  get event() {\n    if (this.#executionCtx && \"respondWith\" in this.#executionCtx) {\n      return this.#executionCtx;\n    } else {\n      throw Error(\"This context has no FetchEvent\");\n    }\n  }\n  get executionCtx() {\n    if (this.#executionCtx) {\n      return this.#executionCtx;\n    } else {\n      throw Error(\"This context has no ExecutionContext\");\n    }\n  }\n  get res() {\n    return this.#res ||= new Response(null, {\n      headers: this.#preparedHeaders ??= new Headers()\n    });\n  }\n  set res(_res) {\n    if (this.#res && _res) {\n      _res = new Response(_res.body, _res);\n      for (const [k, v] of this.#res.headers.entries()) {\n        if (k === \"content-type\") {\n          continue;\n        }\n        if (k === \"set-cookie\") {\n          const cookies = this.#res.headers.getSetCookie();\n          _res.headers.delete(\"set-cookie\");\n          for (const cookie of cookies) {\n            _res.headers.append(\"set-cookie\", cookie);\n          }\n        } else {\n          _res.headers.set(k, v);\n        }\n      }\n    }\n    this.#res = _res;\n    this.finalized = true;\n  }\n  render = (...args) => {\n    this.#renderer ??= (content) => this.html(content);\n    return this.#renderer(...args);\n  };\n  setLayout = (layout) => this.#layout = layout;\n  getLayout = () => this.#layout;\n  setRenderer = (renderer) => {\n    this.#renderer = renderer;\n  };\n  header = (name, value, options) => {\n    if (this.finalized) {\n      this.#res = new Response(this.#res.body, this.#res);\n    }\n    const headers = this.#res ? this.#res.headers : this.#preparedHeaders ??= new Headers();\n    if (value === void 0) {\n      headers.delete(name);\n    } else if (options?.append) {\n      headers.append(name, value);\n    } else {\n      headers.set(name, value);\n    }\n  };\n  status = (status) => {\n    this.#status = status;\n  };\n  set = (key, value) => {\n    this.#var ??= /* @__PURE__ */ new Map();\n    this.#var.set(key, value);\n  };\n  get = (key) => {\n    return this.#var ? this.#var.get(key) : void 0;\n  };\n  get var() {\n    if (!this.#var) {\n      return {};\n    }\n    return Object.fromEntries(this.#var);\n  }\n  #newResponse(data, arg, headers) {\n    const responseHeaders = this.#res ? new Headers(this.#res.headers) : this.#preparedHeaders ?? new Headers();\n    if (typeof arg === \"object\" && \"headers\" in arg) {\n      const argHeaders = arg.headers instanceof Headers ? arg.headers : new Headers(arg.headers);\n      for (const [key, value] of argHeaders) {\n        if (key.toLowerCase() === \"set-cookie\") {\n          responseHeaders.append(key, value);\n        } else {\n          responseHeaders.set(key, value);\n        }\n      }\n    }\n    if (headers) {\n      for (const [k, v] of Object.entries(headers)) {\n        if (typeof v === \"string\") {\n          responseHeaders.set(k, v);\n        } else {\n          responseHeaders.delete(k);\n          for (const v2 of v) {\n            responseHeaders.append(k, v2);\n          }\n        }\n      }\n    }\n    const status = typeof arg === \"number\" ? arg : arg?.status ?? this.#status;\n    return new Response(data, { status, headers: responseHeaders });\n  }\n  newResponse = (...args) => this.#newResponse(...args);\n  body = (data, arg, headers) => this.#newResponse(data, arg, headers);\n  text = (text, arg, headers) => {\n    return !this.#preparedHeaders && !this.#status && !arg && !headers && !this.finalized ? new Response(text) : this.#newResponse(\n      text,\n      arg,\n      setDefaultContentType(TEXT_PLAIN, headers)\n    );\n  };\n  json = (object, arg, headers) => {\n    return this.#newResponse(\n      JSON.stringify(object),\n      arg,\n      setDefaultContentType(\"application/json\", headers)\n    );\n  };\n  html = (html, arg, headers) => {\n    const res = (html2) => this.#newResponse(html2, arg, setDefaultContentType(\"text/html; charset=UTF-8\", headers));\n    return typeof html === \"object\" ? resolveCallback(html, HtmlEscapedCallbackPhase.Stringify, false, {}).then(res) : res(html);\n  };\n  redirect = (location, status) => {\n    this.header(\"Location\", String(location));\n    return this.newResponse(null, status ?? 302);\n  };\n  notFound = () => {\n    this.#notFoundHandler ??= () => new Response();\n    return this.#notFoundHandler(this);\n  };\n};\nexport {\n  Context,\n  TEXT_PLAIN\n};\n", "// src/router.ts\nvar METHOD_NAME_ALL = \"ALL\";\nvar METHOD_NAME_ALL_LOWERCASE = \"all\";\nvar METHODS = [\"get\", \"post\", \"put\", \"delete\", \"options\", \"patch\"];\nvar MESSAGE_MATCHER_IS_ALREADY_BUILT = \"Can not add a route since the matcher is already built.\";\nvar UnsupportedPathError = class extends Error {\n};\nexport {\n  MESSAGE_MATCHER_IS_ALREADY_BUILT,\n  METHODS,\n  METHOD_NAME_ALL,\n  METHOD_NAME_ALL_LOWERCASE,\n  UnsupportedPathError\n};\n", "// src/utils/constants.ts\nvar COMPOSED_HANDLER = \"__COMPOSED_HANDLER\";\nexport {\n  COMPOSED_HANDLER\n};\n", "// src/hono-base.ts\nimport { compose } from \"./compose.js\";\nimport { Context } from \"./context.js\";\nimport { METHODS, METHOD_NAME_ALL, METHOD_NAME_ALL_LOWERCASE } from \"./router.js\";\nimport { COMPOSED_HANDLER } from \"./utils/constants.js\";\nimport { getPath, getPathNoStrict, mergePath } from \"./utils/url.js\";\nvar notFoundHandler = (c) => {\n  return c.text(\"404 Not Found\", 404);\n};\nvar errorHandler = (err, c) => {\n  if (\"getResponse\" in err) {\n    const res = err.getResponse();\n    return c.newResponse(res.body, res);\n  }\n  console.error(err);\n  return c.text(\"Internal Server Error\", 500);\n};\nvar Hono = class {\n  get;\n  post;\n  put;\n  delete;\n  options;\n  patch;\n  all;\n  on;\n  use;\n  router;\n  getPath;\n  _basePath = \"/\";\n  #path = \"/\";\n  routes = [];\n  constructor(options = {}) {\n    const allMethods = [...METHODS, METHOD_NAME_ALL_LOWERCASE];\n    allMethods.forEach((method) => {\n      this[method] = (args1, ...args) => {\n        if (typeof args1 === \"string\") {\n          this.#path = args1;\n        } else {\n          this.#addRoute(method, this.#path, args1);\n        }\n        args.forEach((handler) => {\n          this.#addRoute(method, this.#path, handler);\n        });\n        return this;\n      };\n    });\n    this.on = (method, path, ...handlers) => {\n      for (const p of [path].flat()) {\n        this.#path = p;\n        for (const m of [method].flat()) {\n          handlers.map((handler) => {\n            this.#addRoute(m.toUpperCase(), this.#path, handler);\n          });\n        }\n      }\n      return this;\n    };\n    this.use = (arg1, ...handlers) => {\n      if (typeof arg1 === \"string\") {\n        this.#path = arg1;\n      } else {\n        this.#path = \"*\";\n        handlers.unshift(arg1);\n      }\n      handlers.forEach((handler) => {\n        this.#addRoute(METHOD_NAME_ALL, this.#path, handler);\n      });\n      return this;\n    };\n    const { strict, ...optionsWithoutStrict } = options;\n    Object.assign(this, optionsWithoutStrict);\n    this.getPath = strict ?? true ? options.getPath ?? getPath : getPathNoStrict;\n  }\n  #clone() {\n    const clone = new Hono({\n      router: this.router,\n      getPath: this.getPath\n    });\n    clone.errorHandler = this.errorHandler;\n    clone.#notFoundHandler = this.#notFoundHandler;\n    clone.routes = this.routes;\n    return clone;\n  }\n  #notFoundHandler = notFoundHandler;\n  errorHandler = errorHandler;\n  route(path, app) {\n    const subApp = this.basePath(path);\n    app.routes.map((r) => {\n      let handler;\n      if (app.errorHandler === errorHandler) {\n        handler = r.handler;\n      } else {\n        handler = async (c, next) => (await compose([], app.errorHandler)(c, () => r.handler(c, next))).res;\n        handler[COMPOSED_HANDLER] = r.handler;\n      }\n      subApp.#addRoute(r.method, r.path, handler);\n    });\n    return this;\n  }\n  basePath(path) {\n    const subApp = this.#clone();\n    subApp._basePath = mergePath(this._basePath, path);\n    return subApp;\n  }\n  onError = (handler) => {\n    this.errorHandler = handler;\n    return this;\n  };\n  notFound = (handler) => {\n    this.#notFoundHandler = handler;\n    return this;\n  };\n  mount(path, applicationHandler, options) {\n    let replaceRequest;\n    let optionHandler;\n    if (options) {\n      if (typeof options === \"function\") {\n        optionHandler = options;\n      } else {\n        optionHandler = options.optionHandler;\n        if (options.replaceRequest === false) {\n          replaceRequest = (request) => request;\n        } else {\n          replaceRequest = options.replaceRequest;\n        }\n      }\n    }\n    const getOptions = optionHandler ? (c) => {\n      const options2 = optionHandler(c);\n      return Array.isArray(options2) ? options2 : [options2];\n    } : (c) => {\n      let executionContext = void 0;\n      try {\n        executionContext = c.executionCtx;\n      } catch {\n      }\n      return [c.env, executionContext];\n    };\n    replaceRequest ||= (() => {\n      const mergedPath = mergePath(this._basePath, path);\n      const pathPrefixLength = mergedPath === \"/\" ? 0 : mergedPath.length;\n      return (request) => {\n        const url = new URL(request.url);\n        url.pathname = url.pathname.slice(pathPrefixLength) || \"/\";\n        return new Request(url, request);\n      };\n    })();\n    const handler = async (c, next) => {\n      const res = await applicationHandler(replaceRequest(c.req.raw), ...getOptions(c));\n      if (res) {\n        return res;\n      }\n      await next();\n    };\n    this.#addRoute(METHOD_NAME_ALL, mergePath(path, \"*\"), handler);\n    return this;\n  }\n  #addRoute(method, path, handler) {\n    method = method.toUpperCase();\n    path = mergePath(this._basePath, path);\n    const r = { basePath: this._basePath, path, method, handler };\n    this.router.add(method, path, [handler, r]);\n    this.routes.push(r);\n  }\n  #handleError(err, c) {\n    if (err instanceof Error) {\n      return this.errorHandler(err, c);\n    }\n    throw err;\n  }\n  #dispatch(request, executionCtx, env, method) {\n    if (method === \"HEAD\") {\n      return (async () => new Response(null, await this.#dispatch(request, executionCtx, env, \"GET\")))();\n    }\n    const path = this.getPath(request, { env });\n    const matchResult = this.router.match(method, path);\n    const c = new Context(request, {\n      path,\n      matchResult,\n      env,\n      executionCtx,\n      notFoundHandler: this.#notFoundHandler\n    });\n    if (matchResult[0].length === 1) {\n      let res;\n      try {\n        res = matchResult[0][0][0][0](c, async () => {\n          c.res = await this.#notFoundHandler(c);\n        });\n      } catch (err) {\n        return this.#handleError(err, c);\n      }\n      return res instanceof Promise ? res.then(\n        (resolved) => resolved || (c.finalized ? c.res : this.#notFoundHandler(c))\n      ).catch((err) => this.#handleError(err, c)) : res ?? this.#notFoundHandler(c);\n    }\n    const composed = compose(matchResult[0], this.errorHandler, this.#notFoundHandler);\n    return (async () => {\n      try {\n        const context = await composed(c);\n        if (!context.finalized) {\n          throw new Error(\n            \"Context is not finalized. Did you forget to return a Response object or `await next()`?\"\n          );\n        }\n        return context.res;\n      } catch (err) {\n        return this.#handleError(err, c);\n      }\n    })();\n  }\n  fetch = (request, ...rest) => {\n    return this.#dispatch(request, rest[1], rest[0], request.method);\n  };\n  request = (input, requestInit, Env, executionCtx) => {\n    if (input instanceof Request) {\n      return this.fetch(requestInit ? new Request(input, requestInit) : input, Env, executionCtx);\n    }\n    input = input.toString();\n    return this.fetch(\n      new Request(\n        /^https?:\\/\\//.test(input) ? input : `http://localhost${mergePath(\"/\", input)}`,\n        requestInit\n      ),\n      Env,\n      executionCtx\n    );\n  };\n  fire = () => {\n    addEventListener(\"fetch\", (event) => {\n      event.respondWith(this.#dispatch(event.request, event, void 0, event.request.method));\n    });\n  };\n};\nexport {\n  Hono as HonoBase\n};\n", "// src/router/reg-exp-router/node.ts\nvar LABEL_REG_EXP_STR = \"[^/]+\";\nvar ONLY_WILDCARD_REG_EXP_STR = \".*\";\nvar TAIL_WILDCARD_REG_EXP_STR = \"(?:|/.*)\";\nvar PATH_ERROR = Symbol();\nvar regExpMetaChars = new Set(\".\\\\+*[^]$()\");\nfunction compareKey(a, b) {\n  if (a.length === 1) {\n    return b.length === 1 ? a < b ? -1 : 1 : -1;\n  }\n  if (b.length === 1) {\n    return 1;\n  }\n  if (a === ONLY_WILDCARD_REG_EXP_STR || a === TAIL_WILDCARD_REG_EXP_STR) {\n    return 1;\n  } else if (b === ONLY_WILDCARD_REG_EXP_STR || b === TAIL_WILDCARD_REG_EXP_STR) {\n    return -1;\n  }\n  if (a === LABEL_REG_EXP_STR) {\n    return 1;\n  } else if (b === LABEL_REG_EXP_STR) {\n    return -1;\n  }\n  return a.length === b.length ? a < b ? -1 : 1 : b.length - a.length;\n}\nvar Node = class {\n  #index;\n  #varIndex;\n  #children = /* @__PURE__ */ Object.create(null);\n  insert(tokens, index, paramMap, context, pathErrorCheckOnly) {\n    if (tokens.length === 0) {\n      if (this.#index !== void 0) {\n        throw PATH_ERROR;\n      }\n      if (pathErrorCheckOnly) {\n        return;\n      }\n      this.#index = index;\n      return;\n    }\n    const [token, ...restTokens] = tokens;\n    const pattern = token === \"*\" ? restTokens.length === 0 ? [\"\", \"\", ONLY_WILDCARD_REG_EXP_STR] : [\"\", \"\", LABEL_REG_EXP_STR] : token === \"/*\" ? [\"\", \"\", TAIL_WILDCARD_REG_EXP_STR] : token.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n    let node;\n    if (pattern) {\n      const name = pattern[1];\n      let regexpStr = pattern[2] || LABEL_REG_EXP_STR;\n      if (name && pattern[2]) {\n        regexpStr = regexpStr.replace(/^\\((?!\\?:)(?=[^)]+\\)$)/, \"(?:\");\n        if (/\\((?!\\?:)/.test(regexpStr)) {\n          throw PATH_ERROR;\n        }\n      }\n      node = this.#children[regexpStr];\n      if (!node) {\n        if (Object.keys(this.#children).some(\n          (k) => k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR\n        )) {\n          throw PATH_ERROR;\n        }\n        if (pathErrorCheckOnly) {\n          return;\n        }\n        node = this.#children[regexpStr] = new Node();\n        if (name !== \"\") {\n          node.#varIndex = context.varIndex++;\n        }\n      }\n      if (!pathErrorCheckOnly && name !== \"\") {\n        paramMap.push([name, node.#varIndex]);\n      }\n    } else {\n      node = this.#children[token];\n      if (!node) {\n        if (Object.keys(this.#children).some(\n          (k) => k.length > 1 && k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR\n        )) {\n          throw PATH_ERROR;\n        }\n        if (pathErrorCheckOnly) {\n          return;\n        }\n        node = this.#children[token] = new Node();\n      }\n    }\n    node.insert(restTokens, index, paramMap, context, pathErrorCheckOnly);\n  }\n  buildRegExpStr() {\n    const childKeys = Object.keys(this.#children).sort(compareKey);\n    const strList = childKeys.map((k) => {\n      const c = this.#children[k];\n      return (typeof c.#varIndex === \"number\" ? `(${k})@${c.#varIndex}` : regExpMetaChars.has(k) ? `\\\\${k}` : k) + c.buildRegExpStr();\n    });\n    if (typeof this.#index === \"number\") {\n      strList.unshift(`#${this.#index}`);\n    }\n    if (strList.length === 0) {\n      return \"\";\n    }\n    if (strList.length === 1) {\n      return strList[0];\n    }\n    return \"(?:\" + strList.join(\"|\") + \")\";\n  }\n};\nexport {\n  Node,\n  PATH_ERROR\n};\n", "// src/router/reg-exp-router/trie.ts\nimport { Node } from \"./node.js\";\nvar Trie = class {\n  #context = { varIndex: 0 };\n  #root = new Node();\n  insert(path, index, pathErrorCheckOnly) {\n    const paramAssoc = [];\n    const groups = [];\n    for (let i = 0; ; ) {\n      let replaced = false;\n      path = path.replace(/\\{[^}]+\\}/g, (m) => {\n        const mark = `@\\\\${i}`;\n        groups[i] = [mark, m];\n        i++;\n        replaced = true;\n        return mark;\n      });\n      if (!replaced) {\n        break;\n      }\n    }\n    const tokens = path.match(/(?::[^\\/]+)|(?:\\/\\*$)|./g) || [];\n    for (let i = groups.length - 1; i >= 0; i--) {\n      const [mark] = groups[i];\n      for (let j = tokens.length - 1; j >= 0; j--) {\n        if (tokens[j].indexOf(mark) !== -1) {\n          tokens[j] = tokens[j].replace(mark, groups[i][1]);\n          break;\n        }\n      }\n    }\n    this.#root.insert(tokens, index, paramAssoc, this.#context, pathErrorCheckOnly);\n    return paramAssoc;\n  }\n  buildRegExp() {\n    let regexp = this.#root.buildRegExpStr();\n    if (regexp === \"\") {\n      return [/^$/, [], []];\n    }\n    let captureIndex = 0;\n    const indexReplacementMap = [];\n    const paramReplacementMap = [];\n    regexp = regexp.replace(/#(\\d+)|@(\\d+)|\\.\\*\\$/g, (_, handlerIndex, paramIndex) => {\n      if (handlerIndex !== void 0) {\n        indexReplacementMap[++captureIndex] = Number(handlerIndex);\n        return \"$()\";\n      }\n      if (paramIndex !== void 0) {\n        paramReplacementMap[Number(paramIndex)] = ++captureIndex;\n        return \"\";\n      }\n      return \"\";\n    });\n    return [new RegExp(`^${regexp}`), indexReplacementMap, paramReplacementMap];\n  }\n};\nexport {\n  Trie\n};\n", "// src/router/reg-exp-router/router.ts\nimport {\n  MESSAGE_MATCHER_IS_ALREADY_BUILT,\n  METHOD_NAME_ALL,\n  UnsupportedPathError\n} from \"../../router.js\";\nimport { checkOptionalParameter } from \"../../utils/url.js\";\nimport { PATH_ERROR } from \"./node.js\";\nimport { Trie } from \"./trie.js\";\nvar emptyParam = [];\nvar nullMatcher = [/^$/, [], /* @__PURE__ */ Object.create(null)];\nvar wildcardRegExpCache = /* @__PURE__ */ Object.create(null);\nfunction buildWildcardRegExp(path) {\n  return wildcardRegExpCache[path] ??= new RegExp(\n    path === \"*\" ? \"\" : `^${path.replace(\n      /\\/\\*$|([.\\\\+*[^\\]$()])/g,\n      (_, metaChar) => metaChar ? `\\\\${metaChar}` : \"(?:|/.*)\"\n    )}$`\n  );\n}\nfunction clearWildcardRegExpCache() {\n  wildcardRegExpCache = /* @__PURE__ */ Object.create(null);\n}\nfunction buildMatcherFromPreprocessedRoutes(routes) {\n  const trie = new Trie();\n  const handlerData = [];\n  if (routes.length === 0) {\n    return nullMatcher;\n  }\n  const routesWithStaticPathFlag = routes.map(\n    (route) => [!/\\*|\\/:/.test(route[0]), ...route]\n  ).sort(\n    ([isStaticA, pathA], [isStaticB, pathB]) => isStaticA ? 1 : isStaticB ? -1 : pathA.length - pathB.length\n  );\n  const staticMap = /* @__PURE__ */ Object.create(null);\n  for (let i = 0, j = -1, len = routesWithStaticPathFlag.length; i < len; i++) {\n    const [pathErrorCheckOnly, path, handlers] = routesWithStaticPathFlag[i];\n    if (pathErrorCheckOnly) {\n      staticMap[path] = [handlers.map(([h]) => [h, /* @__PURE__ */ Object.create(null)]), emptyParam];\n    } else {\n      j++;\n    }\n    let paramAssoc;\n    try {\n      paramAssoc = trie.insert(path, j, pathErrorCheckOnly);\n    } catch (e) {\n      throw e === PATH_ERROR ? new UnsupportedPathError(path) : e;\n    }\n    if (pathErrorCheckOnly) {\n      continue;\n    }\n    handlerData[j] = handlers.map(([h, paramCount]) => {\n      const paramIndexMap = /* @__PURE__ */ Object.create(null);\n      paramCount -= 1;\n      for (; paramCount >= 0; paramCount--) {\n        const [key, value] = paramAssoc[paramCount];\n        paramIndexMap[key] = value;\n      }\n      return [h, paramIndexMap];\n    });\n  }\n  const [regexp, indexReplacementMap, paramReplacementMap] = trie.buildRegExp();\n  for (let i = 0, len = handlerData.length; i < len; i++) {\n    for (let j = 0, len2 = handlerData[i].length; j < len2; j++) {\n      const map = handlerData[i][j]?.[1];\n      if (!map) {\n        continue;\n      }\n      const keys = Object.keys(map);\n      for (let k = 0, len3 = keys.length; k < len3; k++) {\n        map[keys[k]] = paramReplacementMap[map[keys[k]]];\n      }\n    }\n  }\n  const handlerMap = [];\n  for (const i in indexReplacementMap) {\n    handlerMap[i] = handlerData[indexReplacementMap[i]];\n  }\n  return [regexp, handlerMap, staticMap];\n}\nfunction findMiddleware(middleware, path) {\n  if (!middleware) {\n    return void 0;\n  }\n  for (const k of Object.keys(middleware).sort((a, b) => b.length - a.length)) {\n    if (buildWildcardRegExp(k).test(path)) {\n      return [...middleware[k]];\n    }\n  }\n  return void 0;\n}\nvar RegExpRouter = class {\n  name = \"RegExpRouter\";\n  #middleware;\n  #routes;\n  constructor() {\n    this.#middleware = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };\n    this.#routes = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };\n  }\n  add(method, path, handler) {\n    const middleware = this.#middleware;\n    const routes = this.#routes;\n    if (!middleware || !routes) {\n      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);\n    }\n    if (!middleware[method]) {\n      ;\n      [middleware, routes].forEach((handlerMap) => {\n        handlerMap[method] = /* @__PURE__ */ Object.create(null);\n        Object.keys(handlerMap[METHOD_NAME_ALL]).forEach((p) => {\n          handlerMap[method][p] = [...handlerMap[METHOD_NAME_ALL][p]];\n        });\n      });\n    }\n    if (path === \"/*\") {\n      path = \"*\";\n    }\n    const paramCount = (path.match(/\\/:/g) || []).length;\n    if (/\\*$/.test(path)) {\n      const re = buildWildcardRegExp(path);\n      if (method === METHOD_NAME_ALL) {\n        Object.keys(middleware).forEach((m) => {\n          middleware[m][path] ||= findMiddleware(middleware[m], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];\n        });\n      } else {\n        middleware[method][path] ||= findMiddleware(middleware[method], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];\n      }\n      Object.keys(middleware).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          Object.keys(middleware[m]).forEach((p) => {\n            re.test(p) && middleware[m][p].push([handler, paramCount]);\n          });\n        }\n      });\n      Object.keys(routes).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          Object.keys(routes[m]).forEach(\n            (p) => re.test(p) && routes[m][p].push([handler, paramCount])\n          );\n        }\n      });\n      return;\n    }\n    const paths = checkOptionalParameter(path) || [path];\n    for (let i = 0, len = paths.length; i < len; i++) {\n      const path2 = paths[i];\n      Object.keys(routes).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          routes[m][path2] ||= [\n            ...findMiddleware(middleware[m], path2) || findMiddleware(middleware[METHOD_NAME_ALL], path2) || []\n          ];\n          routes[m][path2].push([handler, paramCount - len + i + 1]);\n        }\n      });\n    }\n  }\n  match(method, path) {\n    clearWildcardRegExpCache();\n    const matchers = this.#buildAllMatchers();\n    this.match = (method2, path2) => {\n      const matcher = matchers[method2] || matchers[METHOD_NAME_ALL];\n      const staticMatch = matcher[2][path2];\n      if (staticMatch) {\n        return staticMatch;\n      }\n      const match = path2.match(matcher[0]);\n      if (!match) {\n        return [[], emptyParam];\n      }\n      const index = match.indexOf(\"\", 1);\n      return [matcher[1][index], match];\n    };\n    return this.match(method, path);\n  }\n  #buildAllMatchers() {\n    const matchers = /* @__PURE__ */ Object.create(null);\n    Object.keys(this.#routes).concat(Object.keys(this.#middleware)).forEach((method) => {\n      matchers[method] ||= this.#buildMatcher(method);\n    });\n    this.#middleware = this.#routes = void 0;\n    return matchers;\n  }\n  #buildMatcher(method) {\n    const routes = [];\n    let hasOwnRoute = method === METHOD_NAME_ALL;\n    [this.#middleware, this.#routes].forEach((r) => {\n      const ownRoute = r[method] ? Object.keys(r[method]).map((path) => [path, r[method][path]]) : [];\n      if (ownRoute.length !== 0) {\n        hasOwnRoute ||= true;\n        routes.push(...ownRoute);\n      } else if (method !== METHOD_NAME_ALL) {\n        routes.push(\n          ...Object.keys(r[METHOD_NAME_ALL]).map((path) => [path, r[METHOD_NAME_ALL][path]])\n        );\n      }\n    });\n    if (!hasOwnRoute) {\n      return null;\n    } else {\n      return buildMatcherFromPreprocessedRoutes(routes);\n    }\n  }\n};\nexport {\n  RegExpRouter\n};\n", "// src/router/smart-router/router.ts\nimport { MESSAGE_MATCHER_IS_ALREADY_BUILT, UnsupportedPathError } from \"../../router.js\";\nvar SmartRouter = class {\n  name = \"SmartRouter\";\n  #routers = [];\n  #routes = [];\n  constructor(init) {\n    this.#routers = init.routers;\n  }\n  add(method, path, handler) {\n    if (!this.#routes) {\n      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);\n    }\n    this.#routes.push([method, path, handler]);\n  }\n  match(method, path) {\n    if (!this.#routes) {\n      throw new Error(\"Fatal error\");\n    }\n    const routers = this.#routers;\n    const routes = this.#routes;\n    const len = routers.length;\n    let i = 0;\n    let res;\n    for (; i < len; i++) {\n      const router = routers[i];\n      try {\n        for (let i2 = 0, len2 = routes.length; i2 < len2; i2++) {\n          router.add(...routes[i2]);\n        }\n        res = router.match(method, path);\n      } catch (e) {\n        if (e instanceof UnsupportedPathError) {\n          continue;\n        }\n        throw e;\n      }\n      this.match = router.match.bind(router);\n      this.#routers = [router];\n      this.#routes = void 0;\n      break;\n    }\n    if (i === len) {\n      throw new Error(\"Fatal error\");\n    }\n    this.name = `SmartRouter + ${this.activeRouter.name}`;\n    return res;\n  }\n  get activeRouter() {\n    if (this.#routes || this.#routers.length !== 1) {\n      throw new Error(\"No active router has been determined yet.\");\n    }\n    return this.#routers[0];\n  }\n};\nexport {\n  SmartRouter\n};\n", "// src/router/trie-router/node.ts\nimport { METHOD_NAME_ALL } from \"../../router.js\";\nimport { getPattern, splitPath, splitRoutingPath } from \"../../utils/url.js\";\nvar emptyParams = /* @__PURE__ */ Object.create(null);\nvar Node = class {\n  #methods;\n  #children;\n  #patterns;\n  #order = 0;\n  #params = emptyParams;\n  constructor(method, handler, children) {\n    this.#children = children || /* @__PURE__ */ Object.create(null);\n    this.#methods = [];\n    if (method && handler) {\n      const m = /* @__PURE__ */ Object.create(null);\n      m[method] = { handler, possibleKeys: [], score: 0 };\n      this.#methods = [m];\n    }\n    this.#patterns = [];\n  }\n  insert(method, path, handler) {\n    this.#order = ++this.#order;\n    let curNode = this;\n    const parts = splitRoutingPath(path);\n    const possibleKeys = [];\n    for (let i = 0, len = parts.length; i < len; i++) {\n      const p = parts[i];\n      const nextP = parts[i + 1];\n      const pattern = getPattern(p, nextP);\n      const key = Array.isArray(pattern) ? pattern[0] : p;\n      if (key in curNode.#children) {\n        curNode = curNode.#children[key];\n        if (pattern) {\n          possibleKeys.push(pattern[1]);\n        }\n        continue;\n      }\n      curNode.#children[key] = new Node();\n      if (pattern) {\n        curNode.#patterns.push(pattern);\n        possibleKeys.push(pattern[1]);\n      }\n      curNode = curNode.#children[key];\n    }\n    curNode.#methods.push({\n      [method]: {\n        handler,\n        possibleKeys: possibleKeys.filter((v, i, a) => a.indexOf(v) === i),\n        score: this.#order\n      }\n    });\n    return curNode;\n  }\n  #getHandlerSets(node, method, nodeParams, params) {\n    const handlerSets = [];\n    for (let i = 0, len = node.#methods.length; i < len; i++) {\n      const m = node.#methods[i];\n      const handlerSet = m[method] || m[METHOD_NAME_ALL];\n      const processedSet = {};\n      if (handlerSet !== void 0) {\n        handlerSet.params = /* @__PURE__ */ Object.create(null);\n        handlerSets.push(handlerSet);\n        if (nodeParams !== emptyParams || params && params !== emptyParams) {\n          for (let i2 = 0, len2 = handlerSet.possibleKeys.length; i2 < len2; i2++) {\n            const key = handlerSet.possibleKeys[i2];\n            const processed = processedSet[handlerSet.score];\n            handlerSet.params[key] = params?.[key] && !processed ? params[key] : nodeParams[key] ?? params?.[key];\n            processedSet[handlerSet.score] = true;\n          }\n        }\n      }\n    }\n    return handlerSets;\n  }\n  search(method, path) {\n    const handlerSets = [];\n    this.#params = emptyParams;\n    const curNode = this;\n    let curNodes = [curNode];\n    const parts = splitPath(path);\n    const curNodesQueue = [];\n    for (let i = 0, len = parts.length; i < len; i++) {\n      const part = parts[i];\n      const isLast = i === len - 1;\n      const tempNodes = [];\n      for (let j = 0, len2 = curNodes.length; j < len2; j++) {\n        const node = curNodes[j];\n        const nextNode = node.#children[part];\n        if (nextNode) {\n          nextNode.#params = node.#params;\n          if (isLast) {\n            if (nextNode.#children[\"*\"]) {\n              handlerSets.push(\n                ...this.#getHandlerSets(nextNode.#children[\"*\"], method, node.#params)\n              );\n            }\n            handlerSets.push(...this.#getHandlerSets(nextNode, method, node.#params));\n          } else {\n            tempNodes.push(nextNode);\n          }\n        }\n        for (let k = 0, len3 = node.#patterns.length; k < len3; k++) {\n          const pattern = node.#patterns[k];\n          const params = node.#params === emptyParams ? {} : { ...node.#params };\n          if (pattern === \"*\") {\n            const astNode = node.#children[\"*\"];\n            if (astNode) {\n              handlerSets.push(...this.#getHandlerSets(astNode, method, node.#params));\n              astNode.#params = params;\n              tempNodes.push(astNode);\n            }\n            continue;\n          }\n          if (!part) {\n            continue;\n          }\n          const [key, name, matcher] = pattern;\n          const child = node.#children[key];\n          const restPathString = parts.slice(i).join(\"/\");\n          if (matcher instanceof RegExp) {\n            const m = matcher.exec(restPathString);\n            if (m) {\n              params[name] = m[0];\n              handlerSets.push(...this.#getHandlerSets(child, method, node.#params, params));\n              if (Object.keys(child.#children).length) {\n                child.#params = params;\n                const componentCount = m[0].match(/\\//)?.length ?? 0;\n                const targetCurNodes = curNodesQueue[componentCount] ||= [];\n                targetCurNodes.push(child);\n              }\n              continue;\n            }\n          }\n          if (matcher === true || matcher.test(part)) {\n            params[name] = part;\n            if (isLast) {\n              handlerSets.push(...this.#getHandlerSets(child, method, params, node.#params));\n              if (child.#children[\"*\"]) {\n                handlerSets.push(\n                  ...this.#getHandlerSets(child.#children[\"*\"], method, params, node.#params)\n                );\n              }\n            } else {\n              child.#params = params;\n              tempNodes.push(child);\n            }\n          }\n        }\n      }\n      curNodes = tempNodes.concat(curNodesQueue.shift() ?? []);\n    }\n    if (handlerSets.length > 1) {\n      handlerSets.sort((a, b) => {\n        return a.score - b.score;\n      });\n    }\n    return [handlerSets.map(({ handler, params }) => [handler, params])];\n  }\n};\nexport {\n  Node\n};\n", "// src/router/trie-router/router.ts\nimport { checkOptionalParameter } from \"../../utils/url.js\";\nimport { Node } from \"./node.js\";\nvar TrieRouter = class {\n  name = \"TrieRouter\";\n  #node;\n  constructor() {\n    this.#node = new Node();\n  }\n  add(method, path, handler) {\n    const results = checkOptionalParameter(path);\n    if (results) {\n      for (let i = 0, len = results.length; i < len; i++) {\n        this.#node.insert(method, results[i], handler);\n      }\n      return;\n    }\n    this.#node.insert(method, path, handler);\n  }\n  match(method, path) {\n    return this.#node.search(method, path);\n  }\n};\nexport {\n  TrieRouter\n};\n", "// src/hono.ts\nimport { HonoBase } from \"./hono-base.js\";\nimport { RegExpRouter } from \"./router/reg-exp-router/index.js\";\nimport { SmartRouter } from \"./router/smart-router/index.js\";\nimport { TrieRouter } from \"./router/trie-router/index.js\";\nvar Hono = class extends HonoBase {\n  constructor(options = {}) {\n    super(options);\n    this.router = options.router ?? new SmartRouter({\n      routers: [new RegExpRouter(), new TrieRouter()]\n    });\n  }\n};\nexport {\n  Hono\n};\n", "import { ModelInfo } from \"./types\";\n\n// --- Gemini CLI Models Configuration ---\nexport const geminiCliModels: Record<string, ModelInfo> = {\n\t\"gemini-2.5-pro\": {\n\t\tmaxTokens: 65536,\n\t\tcontextWindow: 1_048_576,\n\t\tsupportsImages: true,\n\t\tsupportsPromptCache: false,\n\t\tinputPrice: 0,\n\t\toutputPrice: 0,\n\t\tdescription: \"Google's Gemini 2.5 Pro model via OAuth (free tier)\",\n\t\tthinking: true\n\t},\n\t\"gemini-2.5-flash\": {\n\t\tmaxTokens: 65536,\n\t\tcontextWindow: 1_048_576,\n\t\tsupportsImages: true,\n\t\tsupportsPromptCache: false,\n\t\tinputPrice: 0,\n\t\toutputPrice: 0,\n\t\tdescription: \"Google's Gemini 2.5 Flash model via OAuth (free tier)\",\n\t\tthinking: true\n\t}\n};\n\n// --- Default Model ---\nexport const DEFAULT_MODEL = \"gemini-2.5-flash\";\n\n// --- Helper Functions ---\nexport function getModelInfo(modelId: string): ModelInfo | null {\n\treturn geminiCliModels[modelId] || null;\n}\n\nexport function getAllModelIds(): string[] {\n\treturn Object.keys(geminiCliModels);\n}\n\nexport function isValidModel(modelId: string): boolean {\n\treturn modelId in geminiCliModels;\n}\n", "// --- Google Code Assist API Constants ---\nexport const CODE_ASSIST_ENDPOINT = \"https://cloudcode-pa.googleapis.com\";\nexport const CODE_ASSIST_API_VERSION = \"v1internal\";\n\n// --- OAuth2 Configuration ---\nexport const OAUTH_CLIENT_ID = \"681255809395-oo8ft2oprdrnp9e3aqf6av3hmdib135j.apps.googleusercontent.com\";\nexport const OAUTH_CLIENT_SECRET = \"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\";\nexport const OAUTH_REFRESH_URL = \"https://oauth2.googleapis.com/token\";\n\n// --- Token Management ---\nexport const TOKEN_BUFFER_TIME = 5 * 60 * 1000; // 5 minutes in milliseconds\nexport const KV_TOKEN_KEY = \"oauth_token_cache\";\n\n// --- OpenAI API Constants ---\nexport const OPENAI_CHAT_COMPLETION_OBJECT = \"chat.completion.chunk\";\nexport const OPENAI_MODEL_OWNER = \"google-gemini-cli\";\n", "/**\n * Constants for the Gemini CLI OpenAI Worker\n */\n\n// Static reasoning messages for thinking models\nexport const REASONING_MESSAGES = [\n\t'🔍 **Analyzing the request: \"{requestPreview}\"**\\n\\n',\n\t\"🤔 Let me think about this step by step... \",\n\t\"💭 I need to consider the context and provide a comprehensive response. \",\n\t\"🎯 Based on my understanding, I should address the key points while being accurate and helpful. \",\n\t\"✨ Let me formulate a clear and structured answer.\\n\\n\"\n];\n\n// Default reasoning delay between chunks (in milliseconds)\nexport const REASONING_CHUNK_DELAY = 100;\n\n// Default chunk size for streaming thinking content (in characters)\n// This controls how many characters are sent per chunk when streaming thinking content\n// Smaller values create smoother streaming but more network requests\nexport const THINKING_CONTENT_CHUNK_SIZE = 15;\n\n// Thinking budget constants\nexport const DEFAULT_THINKING_BUDGET = -1; // -1 means dynamic allocation by Gemini (recommended)\nexport const DISABLED_THINKING_BUDGET = 0; // 0 disables thinking entirely\n\n// Generation config defaults\nexport const DEFAULT_TEMPERATURE = 0.7;\n\n// Auto model switching configuration\nexport const AUTO_SWITCH_MODEL_MAP = {\n\t\"gemini-2.5-pro\": \"gemini-2.5-flash\"\n} as const;\n\n// HTTP status codes for rate limiting\nexport const RATE_LIMIT_STATUS_CODES = [429, 503] as const;\n\n// Reasoning effort mapping to thinking budgets\nexport const REASONING_EFFORT_BUDGETS = {\n\tnone: 0,\n\tlow: 1024,\n\tmedium: {\n\t\tflash: 12288,\n\t\tdefault: 16384\n\t},\n\thigh: {\n\t\tflash: 24576,\n\t\tdefault: 32768\n\t}\n} as const;\n\n// Gemini safety categories\nexport const GEMINI_SAFETY_CATEGORIES = {\n\tHARASSMENT: \"HARM_CATEGORY_HARASSMENT\",\n\tHATE_SPEECH: \"HARM_CATEGORY_HATE_SPEECH\",\n\tSEXUALLY_EXPLICIT: \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n\tDANGEROUS_CONTENT: \"HARM_CATEGORY_DANGEROUS_CONTENT\"\n} as const;\n", "import { Env, OAuth2Credentials } from \"./types\";\nimport {\n\tCODE_ASSIST_ENDPOINT,\n\tCODE_ASSIST_API_VERSION,\n\tOAUTH_CLIENT_ID,\n\tOAUTH_CLIENT_SECRET,\n\tOAUTH_REFRESH_URL,\n\tTOKEN_BUFFER_TIME,\n\tKV_TOKEN_KEY\n} from \"./config\";\n\n// Auth-related interfaces\ninterface TokenRefreshResponse {\n\taccess_token: string;\n\texpires_in: number;\n}\n\ninterface CachedTokenData {\n\taccess_token: string;\n\texpiry_date: number;\n\tcached_at: number;\n}\n\ninterface TokenCacheInfo {\n\tcached: boolean;\n\tcached_at?: string;\n\texpires_at?: string;\n\ttime_until_expiry_seconds?: number;\n\tis_expired?: boolean;\n\tmessage?: string;\n\terror?: string;\n}\n\n/**\n * Handles OAuth2 authentication and Google Code Assist API communication.\n * Manages token caching, refresh, and API calls.\n */\nexport class AuthManager {\n\tprivate env: Env;\n\tprivate accessToken: string | null = null;\n\n\tconstructor(env: Env) {\n\t\tthis.env = env;\n\t}\n\n\t/**\n\t * Initializes authentication using OAuth2 credentials with KV storage caching.\n\t */\n\tpublic async initializeAuth(): Promise<void> {\n\t\tif (!this.env.GCP_SERVICE_ACCOUNT) {\n\t\t\tthrow new Error(\"`GCP_SERVICE_ACCOUNT` environment variable not set. Please provide OAuth2 credentials JSON.\");\n\t\t}\n\n\t\ttry {\n\t\t\t// First, try to get a cached token from KV storage\n\t\t\tlet cachedTokenData = null;\n\n\t\t\ttry {\n\t\t\t\tconst cachedToken = await this.env.GEMINI_CLI_KV.get(KV_TOKEN_KEY, \"json\");\n\t\t\t\tif (cachedToken) {\n\t\t\t\t\tcachedTokenData = cachedToken as CachedTokenData;\n\t\t\t\t\tconsole.log(\"Found cached token in KV storage\");\n\t\t\t\t}\n\t\t\t} catch (kvError) {\n\t\t\t\tconsole.log(\"No cached token found in KV storage or KV error:\", kvError);\n\t\t\t}\n\n\t\t\t// Check if cached token is still valid (with buffer)\n\t\t\tif (cachedTokenData) {\n\t\t\t\tconst timeUntilExpiry = cachedTokenData.expiry_date - Date.now();\n\t\t\t\tif (timeUntilExpiry > TOKEN_BUFFER_TIME) {\n\t\t\t\t\tthis.accessToken = cachedTokenData.access_token;\n\t\t\t\t\tconsole.log(`Using cached token, valid for ${Math.floor(timeUntilExpiry / 1000)} more seconds`);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconsole.log(\"Cached token expired or expiring soon\");\n\t\t\t}\n\n\t\t\t// Parse original credentials from environment\n\t\t\tconst oauth2Creds: OAuth2Credentials = JSON.parse(this.env.GCP_SERVICE_ACCOUNT);\n\n\t\t\t// Check if the original token is still valid\n\t\t\tconst timeUntilExpiry = oauth2Creds.expiry_date - Date.now();\n\t\t\tif (timeUntilExpiry > TOKEN_BUFFER_TIME) {\n\t\t\t\t// Original token is still valid, cache it and use it\n\t\t\t\tthis.accessToken = oauth2Creds.access_token;\n\t\t\t\tconsole.log(`Original token is valid for ${Math.floor(timeUntilExpiry / 1000)} more seconds`);\n\n\t\t\t\t// Cache the token in KV storage\n\t\t\t\tawait this.cacheTokenInKV(oauth2Creds.access_token, oauth2Creds.expiry_date);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Both original and cached tokens are expired, refresh the token\n\t\t\tconsole.log(\"All tokens expired, refreshing...\");\n\t\t\tawait this.refreshAndCacheToken(oauth2Creds.refresh_token);\n\t\t} catch (e: unknown) {\n\t\t\tconst errorMessage = e instanceof Error ? e.message : String(e);\n\t\t\tconsole.error(\"Failed to initialize authentication:\", e);\n\t\t\tthrow new Error(\"Authentication failed: \" + errorMessage);\n\t\t}\n\t}\n\n\t/**\n\t * Refresh the OAuth token and cache it in KV storage.\n\t */\n\tprivate async refreshAndCacheToken(refreshToken: string): Promise<void> {\n\t\tconsole.log(\"Refreshing OAuth token...\");\n\n\t\tconst refreshResponse = await fetch(OAUTH_REFRESH_URL, {\n\t\t\tmethod: \"POST\",\n\t\t\theaders: {\n\t\t\t\t\"Content-Type\": \"application/x-www-form-urlencoded\"\n\t\t\t},\n\t\t\tbody: new URLSearchParams({\n\t\t\t\tclient_id: OAUTH_CLIENT_ID,\n\t\t\t\tclient_secret: OAUTH_CLIENT_SECRET,\n\t\t\t\trefresh_token: refreshToken,\n\t\t\t\tgrant_type: \"refresh_token\"\n\t\t\t})\n\t\t});\n\n\t\tif (!refreshResponse.ok) {\n\t\t\tconst errorText = await refreshResponse.text();\n\t\t\tconsole.error(\"Token refresh failed:\", errorText);\n\t\t\tthrow new Error(`Token refresh failed: ${errorText}`);\n\t\t}\n\n\t\tconst refreshData = (await refreshResponse.json()) as TokenRefreshResponse;\n\t\tthis.accessToken = refreshData.access_token;\n\n\t\t// Calculate expiry time (typically 1 hour from now)\n\t\tconst expiryTime = Date.now() + refreshData.expires_in * 1000;\n\n\t\tconsole.log(\"Token refreshed successfully\");\n\t\tconsole.log(`New token expires in ${refreshData.expires_in} seconds`);\n\n\t\t// Cache the new token in KV storage\n\t\tawait this.cacheTokenInKV(refreshData.access_token, expiryTime);\n\t}\n\n\t/**\n\t * Cache the access token in KV storage.\n\t */\n\tprivate async cacheTokenInKV(accessToken: string, expiryDate: number): Promise<void> {\n\t\ttry {\n\t\t\tconst tokenData = {\n\t\t\t\taccess_token: accessToken,\n\t\t\t\texpiry_date: expiryDate,\n\t\t\t\tcached_at: Date.now()\n\t\t\t};\n\n\t\t\t// Cache for slightly less than the token expiry to be safe\n\t\t\tconst ttlSeconds = Math.floor((expiryDate - Date.now()) / 1000) - 300; // 5 minutes buffer\n\n\t\t\tif (ttlSeconds > 0) {\n\t\t\t\tawait this.env.GEMINI_CLI_KV.put(KV_TOKEN_KEY, JSON.stringify(tokenData), {\n\t\t\t\t\texpirationTtl: ttlSeconds\n\t\t\t\t});\n\t\t\t\tconsole.log(`Token cached in KV storage with TTL of ${ttlSeconds} seconds`);\n\t\t\t} else {\n\t\t\t\tconsole.log(\"Token expires too soon, not caching in KV\");\n\t\t\t}\n\t\t} catch (kvError) {\n\t\t\tconsole.error(\"Failed to cache token in KV storage:\", kvError);\n\t\t\t// Don't throw an error here as the token is still valid, just not cached\n\t\t}\n\t}\n\n\t/**\n\t * Clear cached token from KV storage.\n\t */\n\tpublic async clearTokenCache(): Promise<void> {\n\t\ttry {\n\t\t\tawait this.env.GEMINI_CLI_KV.delete(KV_TOKEN_KEY);\n\t\t\tconsole.log(\"Cleared cached token from KV storage\");\n\t\t} catch (kvError) {\n\t\t\tconsole.log(\"Error clearing KV cache:\", kvError);\n\t\t}\n\t}\n\n\t/**\n\t * Get cached token info from KV storage.\n\t */\n\tpublic async getCachedTokenInfo(): Promise<TokenCacheInfo> {\n\t\ttry {\n\t\t\tconst cachedToken = await this.env.GEMINI_CLI_KV.get(KV_TOKEN_KEY, \"json\");\n\t\t\tif (cachedToken) {\n\t\t\t\tconst tokenData = cachedToken as CachedTokenData;\n\t\t\t\tconst timeUntilExpiry = tokenData.expiry_date - Date.now();\n\n\t\t\t\treturn {\n\t\t\t\t\tcached: true,\n\t\t\t\t\tcached_at: new Date(tokenData.cached_at).toISOString(),\n\t\t\t\t\texpires_at: new Date(tokenData.expiry_date).toISOString(),\n\t\t\t\t\ttime_until_expiry_seconds: Math.floor(timeUntilExpiry / 1000),\n\t\t\t\t\tis_expired: timeUntilExpiry < 0\n\t\t\t\t\t// Removed token_preview for security\n\t\t\t\t};\n\t\t\t}\n\t\t\treturn { cached: false, message: \"No token found in cache\" };\n\t\t} catch (e: unknown) {\n\t\t\tconst errorMessage = e instanceof Error ? e.message : String(e);\n\t\t\treturn { cached: false, error: errorMessage };\n\t\t}\n\t}\n\n\t/**\n\t * A generic method to call a Code Assist API endpoint.\n\t */\n\tpublic async callEndpoint(method: string, body: Record<string, unknown>, isRetry: boolean = false): Promise<unknown> {\n\t\tawait this.initializeAuth();\n\n\t\tconst response = await fetch(`${CODE_ASSIST_ENDPOINT}/${CODE_ASSIST_API_VERSION}:${method}`, {\n\t\t\tmethod: \"POST\",\n\t\t\theaders: {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\tAuthorization: `Bearer ${this.accessToken}`\n\t\t\t},\n\t\t\tbody: JSON.stringify(body)\n\t\t});\n\n\t\tif (!response.ok) {\n\t\t\tif (response.status === 401 && !isRetry) {\n\t\t\t\tconsole.log(\"Got 401 error, clearing token cache and retrying...\");\n\t\t\t\tthis.accessToken = null; // Clear cached token\n\t\t\t\tawait this.clearTokenCache(); // Clear KV cache\n\t\t\t\tawait this.initializeAuth(); // This will refresh the token\n\t\t\t\treturn this.callEndpoint(method, body, true); // Retry once\n\t\t\t}\n\t\t\tconst errorText = await response.text();\n\t\t\tthrow new Error(`API call failed with status ${response.status}: ${errorText}`);\n\t\t}\n\n\t\treturn response.json();\n\t}\n\n\t/**\n\t * Get the current access token.\n\t */\n\tpublic getAccessToken(): string | null {\n\t\treturn this.accessToken;\n\t}\n}\n", "/**\n * Utility functions for image processing and validation\n */\n\nexport interface ImageValidationResult {\n\tisValid: boolean;\n\terror?: string;\n\tmimeType?: string;\n\tformat?: string;\n}\n\nexport interface DataUrlComponents {\n\tmimeType: string;\n\tdata: string;\n}\n\nexport interface ModelInfo {\n\tsupportsImages?: boolean;\n}\n\nexport type ModelRegistry = Record<string, ModelInfo>;\n\n/**\n * Validates an image URL or base64 data URL\n */\nexport function validateImageUrl(imageUrl: string): ImageValidationResult {\n\tif (!imageUrl) {\n\t\treturn { isValid: false, error: \"Image URL is required\" };\n\t}\n\n\tif (imageUrl.startsWith(\"data:image/\")) {\n\t\t// Validate base64 image\n\t\tconst [mimeTypePart, base64Part] = imageUrl.split(\",\");\n\n\t\tif (!base64Part) {\n\t\t\treturn { isValid: false, error: \"Invalid base64 image format\" };\n\t\t}\n\n\t\tconst mimeType = mimeTypePart.split(\":\")[1].split(\";\")[0];\n\t\tconst format = mimeType.split(\"/\")[1];\n\n\t\tconst supportedFormats = [\"jpeg\", \"jpg\", \"png\", \"gif\", \"webp\"];\n\t\tif (!supportedFormats.includes(format.toLowerCase())) {\n\t\t\treturn {\n\t\t\t\tisValid: false,\n\t\t\t\terror: `Unsupported image format: ${format}. Supported formats: ${supportedFormats.join(\", \")}`\n\t\t\t};\n\t\t}\n\n\t\t// Basic base64 validation\n\t\ttry {\n\t\t\tatob(base64Part.substring(0, 100)); // Test a small portion\n\t\t} catch {\n\t\t\treturn { isValid: false, error: \"Invalid base64 encoding\" };\n\t\t}\n\n\t\treturn { isValid: true, mimeType, format };\n\t}\n\n\tif (imageUrl.startsWith(\"http://\") || imageUrl.startsWith(\"https://\")) {\n\t\t// Basic URL validation\n\t\ttry {\n\t\t\tnew URL(imageUrl);\n\t\t\treturn { isValid: true, mimeType: \"image/jpeg\" }; // Default assumption for URLs\n\t\t} catch {\n\t\t\treturn { isValid: false, error: \"Invalid URL format\" };\n\t\t}\n\t}\n\n\treturn { isValid: false, error: \"Image URL must be a base64 data URL or HTTP/HTTPS URL\" };\n}\n\n/**\n * Extracts image information from a data URL\n */\nexport function parseDataUrl(dataUrl: string): DataUrlComponents | null {\n\tif (!dataUrl.startsWith(\"data:\")) {\n\t\treturn null;\n\t}\n\n\tconst [mimeTypePart, data] = dataUrl.split(\",\");\n\tconst mimeType = mimeTypePart.split(\":\")[1].split(\";\")[0];\n\n\treturn { mimeType, data };\n}\n\n/**\n * Validates if a model supports image inputs\n */\nexport function modelSupportsImages(modelId: string, models: ModelRegistry): boolean {\n\treturn models[modelId]?.supportsImages || false;\n}\n\n/**\n * Estimates the token count for an image (rough approximation)\n * This is a simplified estimation - actual token usage may vary\n */\nexport function estimateImageTokens(imageUrl: string, detail: \"low\" | \"high\" | \"auto\" = \"auto\"): number {\n\tif (detail === \"low\") {\n\t\treturn 85; // Low detail images use a fixed token count\n\t}\n\n\t// For high detail, estimate based on image size (simplified)\n\t// In practice, this would require analyzing actual image dimensions\n\tif (imageUrl.startsWith(\"data:\")) {\n\t\tconst base64Data = imageUrl.split(\",\")[1];\n\t\tconst sizeKB = (base64Data.length * 3) / 4 / 1024; // Rough base64 to bytes conversion\n\n\t\t// Rough estimation: ~170 tokens per 512x512 tile\n\t\tif (sizeKB < 100) return 170; // Small image\n\t\tif (sizeKB < 500) return 340; // Medium image\n\t\treturn 680; // Large image\n\t}\n\n\treturn 340; // Default estimate for URL images\n}\n", "import { geminiCliModels } from \"../models\";\nimport {\n\tDEFAULT_THINKING_BUDGET,\n\tDEFAULT_TEMPERATURE,\n\tREASONING_EFFORT_BUDGETS,\n\tGEMINI_SAFETY_CATEGORIES\n} from \"../constants\";\nimport { ChatCompletionRequest, Env, EffortLevel, SafetyThreshold } from \"../types\";\n\n/**\n * Helper class to validate and correct generation configurations for different Gemini models.\n * Handles model-specific limitations and provides sensible defaults.\n */\nexport class GenerationConfigValidator {\n\t/**\n\t * Maps reasoning effort to thinking budget based on model type.\n\t * @param effort - The reasoning effort level\n\t * @param modelId - The model ID to determine if it's a flash model\n\t * @returns The corresponding thinking budget\n\t */\n\tstatic mapEffortToThinkingBudget(effort: EffortLevel, modelId: string): number {\n\t\tconst isFlashModel = modelId.includes(\"flash\");\n\n\t\tswitch (effort) {\n\t\t\tcase \"none\":\n\t\t\t\treturn REASONING_EFFORT_BUDGETS.none;\n\t\t\tcase \"low\":\n\t\t\t\treturn REASONING_EFFORT_BUDGETS.low;\n\t\t\tcase \"medium\":\n\t\t\t\treturn isFlashModel ? REASONING_EFFORT_BUDGETS.medium.flash : REASONING_EFFORT_BUDGETS.medium.default;\n\t\t\tcase \"high\":\n\t\t\t\treturn isFlashModel ? REASONING_EFFORT_BUDGETS.high.flash : REASONING_EFFORT_BUDGETS.high.default;\n\t\t\tdefault:\n\t\t\t\treturn DEFAULT_THINKING_BUDGET;\n\t\t}\n\t}\n\n\t/**\n\t * Type guard to check if a value is a valid EffortLevel.\n\t * @param value - The value to check\n\t * @returns True if the value is a valid EffortLevel\n\t */\n\tstatic isValidEffortLevel(value: unknown): value is EffortLevel {\n\t\treturn typeof value === \"string\" && [\"none\", \"low\", \"medium\", \"high\"].includes(value);\n\t}\n\n\t/**\n\t * Creates safety settings configuration for Gemini API.\n\t * @param env - Environment variables containing safety thresholds\n\t * @returns Safety settings configuration\n\t */\n\tstatic createSafetySettings(env: Env): Array<{ category: string; threshold: SafetyThreshold }> {\n\t\tconst safetySettings: Array<{ category: string; threshold: SafetyThreshold }> = [];\n\n\t\tif (env.GEMINI_MODERATION_HARASSMENT_THRESHOLD) {\n\t\t\tsafetySettings.push({\n\t\t\t\tcategory: GEMINI_SAFETY_CATEGORIES.HARASSMENT,\n\t\t\t\tthreshold: env.GEMINI_MODERATION_HARASSMENT_THRESHOLD\n\t\t\t});\n\t\t}\n\n\t\tif (env.GEMINI_MODERATION_HATE_SPEECH_THRESHOLD) {\n\t\t\tsafetySettings.push({\n\t\t\t\tcategory: GEMINI_SAFETY_CATEGORIES.HATE_SPEECH,\n\t\t\t\tthreshold: env.GEMINI_MODERATION_HATE_SPEECH_THRESHOLD\n\t\t\t});\n\t\t}\n\n\t\tif (env.GEMINI_MODERATION_SEXUALLY_EXPLICIT_THRESHOLD) {\n\t\t\tsafetySettings.push({\n\t\t\t\tcategory: GEMINI_SAFETY_CATEGORIES.SEXUALLY_EXPLICIT,\n\t\t\t\tthreshold: env.GEMINI_MODERATION_SEXUALLY_EXPLICIT_THRESHOLD\n\t\t\t});\n\t\t}\n\n\t\tif (env.GEMINI_MODERATION_DANGEROUS_CONTENT_THRESHOLD) {\n\t\t\tsafetySettings.push({\n\t\t\t\tcategory: GEMINI_SAFETY_CATEGORIES.DANGEROUS_CONTENT,\n\t\t\t\tthreshold: env.GEMINI_MODERATION_DANGEROUS_CONTENT_THRESHOLD\n\t\t\t});\n\t\t}\n\n\t\treturn safetySettings;\n\t}\n\n\t/**\n\t * Validates and corrects the thinking budget for a specific model.\n\t * @param modelId - The Gemini model ID\n\t * @param thinkingBudget - The requested thinking budget\n\t * @returns The corrected thinking budget\n\t */\n\tstatic validateThinkingBudget(modelId: string, thinkingBudget: number): number {\n\t\tconst modelInfo = geminiCliModels[modelId];\n\n\t\t// For thinking models, validate the budget\n\t\tif (modelInfo?.thinking) {\n\t\t\t// Gemini 2.5 Pro and Flash don't support thinking_budget: 0\n\t\t\t// They require -1 (dynamic allocation) or positive numbers\n\t\t\tif (thinkingBudget === 0) {\n\t\t\t\tconsole.log(`[GenerationConfig] Model '${modelId}' doesn't support thinking_budget: 0, using -1 instead`);\n\t\t\t\treturn DEFAULT_THINKING_BUDGET; // -1\n\t\t\t}\n\n\t\t\t// Validate positive budget values (optional: add upper limits if needed)\n\t\t\tif (thinkingBudget < -1) {\n\t\t\t\tconsole.log(\n\t\t\t\t\t`[GenerationConfig] Invalid thinking_budget: ${thinkingBudget} for model '${modelId}', using -1 instead`\n\t\t\t\t);\n\t\t\t\treturn DEFAULT_THINKING_BUDGET; // -1\n\t\t\t}\n\t\t}\n\n\t\treturn thinkingBudget;\n\t}\n\n\t/**\n\t * Creates a validated generation config for a specific model.\n\t * @param modelId - The Gemini model ID\n\t * @param options - Generation options including thinking budget and OpenAI parameters\n\t * @param isRealThinkingEnabled - Whether real thinking is enabled\n\t * @param includeReasoning - Whether to include reasoning in response\n\t * @param env - Environment variables for safety settings\n\t * @returns Validated generation configuration\n\t */\n\tstatic createValidatedConfig(\n\t\tmodelId: string,\n\t\toptions: Partial<ChatCompletionRequest> = {},\n\t\tisRealThinkingEnabled: boolean,\n\t\tincludeReasoning: boolean,\n\t\tenv?: Env\n\t): Record<string, unknown> {\n\t\tconst generationConfig: Record<string, unknown> = {\n\t\t\ttemperature: options.temperature ?? DEFAULT_TEMPERATURE,\n\t\t\tmaxOutputTokens: options.max_tokens,\n\t\t\ttopP: options.top_p,\n\t\t\tstopSequences: typeof options.stop === \"string\" ? [options.stop] : options.stop,\n\t\t\tpresencePenalty: options.presence_penalty,\n\t\t\tfrequencyPenalty: options.frequency_penalty,\n\t\t\tseed: options.seed\n\t\t};\n\n\t\tif (options.response_format?.type === \"json_object\") {\n\t\t\tgenerationConfig.responseMimeType = \"application/json\";\n\t\t}\n\n\t\t// Add safety settings if environment variables are provided\n\t\tif (env) {\n\t\t\tconst safetySettings = this.createSafetySettings(env);\n\t\t\tif (safetySettings.length > 0) {\n\t\t\t\tgenerationConfig.safetySettings = safetySettings;\n\t\t\t}\n\t\t}\n\n\t\tconst modelInfo = geminiCliModels[modelId];\n\t\tconst isThinkingModel = modelInfo?.thinking || false;\n\n\t\tif (isThinkingModel) {\n\t\t\tlet thinkingBudget = options.thinking_budget ?? DEFAULT_THINKING_BUDGET;\n\n\t\t\t// Handle reasoning effort mapping to thinking budget\n\t\t\tconst reasoning_effort =\n\t\t\t\toptions.reasoning_effort || options.extra_body?.reasoning_effort || options.model_params?.reasoning_effort;\n\n\t\t\tif (reasoning_effort && this.isValidEffortLevel(reasoning_effort)) {\n\t\t\t\tthinkingBudget = this.mapEffortToThinkingBudget(reasoning_effort, modelId);\n\t\t\t\t// If effort is \"none\", disable reasoning\n\t\t\t\tif (reasoning_effort === \"none\") {\n\t\t\t\t\tincludeReasoning = false;\n\t\t\t\t} else {\n\t\t\t\t\tincludeReasoning = true;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst validatedBudget = this.validateThinkingBudget(modelId, thinkingBudget);\n\n\t\t\tif (isRealThinkingEnabled && includeReasoning) {\n\t\t\t\t// Enable thinking with validated budget\n\t\t\t\tgenerationConfig.thinkingConfig = {\n\t\t\t\t\tthinkingBudget: validatedBudget,\n\t\t\t\t\tincludeThoughts: true // Critical: This enables thinking content in response\n\t\t\t\t};\n\t\t\t\tconsole.log(`[GenerationConfig] Real thinking enabled for '${modelId}' with budget: ${validatedBudget}`);\n\t\t\t} else {\n\t\t\t\t// For thinking models, always use validated budget (can't use 0)\n\t\t\t\t// Control thinking visibility with includeThoughts instead\n\t\t\t\tgenerationConfig.thinkingConfig = {\n\t\t\t\t\tthinkingBudget: this.validateThinkingBudget(modelId, DEFAULT_THINKING_BUDGET),\n\t\t\t\t\tincludeThoughts: false // Disable thinking visibility in response\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\t// Remove undefined keys\n\t\tObject.keys(generationConfig).forEach((key) => generationConfig[key] === undefined && delete generationConfig[key]);\n\t\treturn generationConfig;\n\t}\n\n\tstatic createValidateTools(options: Partial<ChatCompletionRequest> = {}) {\n\t\tconst tools = [];\n\t\tlet toolConfig = {};\n\t\t// Add tools configuration if provided\n\t\tif (Array.isArray(options.tools) && options.tools.length > 0) {\n\t\t\tconst functionDeclarations = options.tools.map((tool) => {\n\t\t\t\tlet parameters = tool.function.parameters;\n\t\t\t\t// Filter parameters for Claude-style compatibility by removing keys starting with '$'\n\t\t\t\tif (parameters) {\n\t\t\t\t\tconst before = parameters;\n\t\t\t\t\tparameters = Object.keys(parameters)\n\t\t\t\t\t\t.filter((key) => !key.startsWith(\"$\"))\n\t\t\t\t\t\t.reduce(\n\t\t\t\t\t\t\t(after, key) => {\n\t\t\t\t\t\t\t\tafter[key] = before[key];\n\t\t\t\t\t\t\t\treturn after;\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{} as Record<string, unknown>\n\t\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\treturn {\n\t\t\t\t\tname: tool.function.name,\n\t\t\t\t\tdescription: tool.function.description,\n\t\t\t\t\tparameters\n\t\t\t\t};\n\t\t\t});\n\n\t\t\ttools.push({ functionDeclarations });\n\t\t\t// Handle tool choice\n\t\t\tif (options.tool_choice) {\n\t\t\t\tif (options.tool_choice === \"auto\") {\n\t\t\t\t\ttoolConfig = { functionCallingConfig: { mode: \"AUTO\" } };\n\t\t\t\t} else if (options.tool_choice === \"none\") {\n\t\t\t\t\ttoolConfig = { functionCallingConfig: { mode: \"NONE\" } };\n\t\t\t\t} else if (typeof options.tool_choice === \"object\" && options.tool_choice.function) {\n\t\t\t\t\ttoolConfig = {\n\t\t\t\t\t\tfunctionCallingConfig: {\n\t\t\t\t\t\t\tmode: \"ANY\",\n\t\t\t\t\t\t\tallowedFunctionNames: [options.tool_choice.function.name]\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn { tools, toolConfig };\n\t}\n}\n", "import { AUTO_SWITCH_MODEL_MAP, RATE_LIMIT_STATUS_CODES } from \"../constants\";\nimport { Env, ChatMessage, UsageData, StreamChunk } from \"../types\";\n\n/**\n * Helper class for handling automatic model switching on rate limit errors.\n * Provides centralized logic for detecting rate limits and managing fallback models.\n */\nexport class AutoModelSwitchingHelper {\n\tprivate env: Env;\n\n\tconstructor(env: Env) {\n\t\tthis.env = env;\n\t}\n\n\t/**\n\t * Checks if auto model switching is enabled via environment variable.\n\t */\n\tisEnabled(): boolean {\n\t\treturn this.env.ENABLE_AUTO_MODEL_SWITCHING === \"true\";\n\t}\n\n\t/**\n\t * Gets the fallback model for the given original model.\n\t * Returns null if no fallback is configured for the model.\n\t */\n\tgetFallbackModel(originalModel: string): string | null {\n\t\treturn AUTO_SWITCH_MODEL_MAP[originalModel as keyof typeof AUTO_SWITCH_MODEL_MAP] || null;\n\t}\n\n\t/**\n\t * Checks if the error message indicates a rate limit error that should trigger auto switching.\n\t */\n\tisRateLimitError(error: unknown): boolean {\n\t\treturn (\n\t\t\terror instanceof Error &&\n\t\t\t(error.message.includes(\"Stream request failed: 429\") || error.message.includes(\"Stream request failed: 503\"))\n\t\t);\n\t}\n\n\t/**\n\t * Checks if the HTTP status code indicates a rate limit error.\n\t */\n\tisRateLimitStatus(status: number): boolean {\n\t\treturn (RATE_LIMIT_STATUS_CODES as readonly number[]).includes(status);\n\t}\n\n\t/**\n\t * Determines if fallback should be attempted for the given model and conditions.\n\t */\n\tshouldAttemptFallback(originalModel: string): boolean {\n\t\treturn this.isEnabled() && this.getFallbackModel(originalModel) !== null;\n\t}\n\n\t/**\n\t * Creates a notification message for when a model switch occurs.\n\t */\n\tcreateSwitchNotification(originalModel: string, fallbackModel: string): string {\n\t\treturn `[Auto-switched from ${originalModel} to ${fallbackModel} due to rate limiting]\\n\\n`;\n\t}\n\n\t/**\n\t * Handles rate limit fallback for non-streaming requests.\n\t * This method requires a stream content function to perform the actual retry.\n\t */\n\tasync handleNonStreamingFallback(\n\t\toriginalModel: string,\n\t\tsystemPrompt: string,\n\t\tmessages: ChatMessage[],\n\t\toptions:\n\t\t\t| {\n\t\t\t\t\tincludeReasoning?: boolean;\n\t\t\t\t\tthinkingBudget?: number;\n\t\t\t  }\n\t\t\t| undefined,\n\t\tstreamContentFn: (\n\t\t\tmodelId: string,\n\t\t\tsystemPrompt: string,\n\t\t\tmessages: ChatMessage[],\n\t\t\toptions?: {\n\t\t\t\tincludeReasoning?: boolean;\n\t\t\t\tthinkingBudget?: number;\n\t\t\t}\n\t\t) => AsyncGenerator<StreamChunk>\n\t): Promise<{ content: string; usage?: UsageData } | null> {\n\t\tconst fallbackModel = this.getFallbackModel(originalModel);\n\t\tif (!fallbackModel || !this.isEnabled()) {\n\t\t\treturn null;\n\t\t}\n\n\t\tconsole.log(`Got rate limit error for model ${originalModel}, switching to fallback model: ${fallbackModel}`);\n\n\t\tlet content = \"\";\n\t\tlet usage: UsageData | undefined;\n\n\t\t// Add notification about model switch\n\t\tcontent += this.createSwitchNotification(originalModel, fallbackModel);\n\n\t\t// Collect all chunks from the stream with fallback model\n\t\tfor await (const chunk of streamContentFn(fallbackModel, systemPrompt, messages, options)) {\n\t\t\tif (chunk.type === \"text\" && typeof chunk.data === \"string\") {\n\t\t\t\tcontent += chunk.data;\n\t\t\t} else if (chunk.type === \"usage\" && typeof chunk.data === \"object\") {\n\t\t\t\tusage = chunk.data as UsageData;\n\t\t\t}\n\t\t}\n\n\t\treturn { content, usage };\n\t}\n}\n", "import {\n\tEnv,\n\t<PERSON><PERSON><PERSON>k,\n\tReasoningData,\n\tUsageData,\n\tChatMessage,\n\tMessageContent,\n\tTool,\n\tToolChoice,\n\tGeminiFunctionCall\n} from \"./types\";\nimport { AuthManager } from \"./auth\";\nimport { CODE_ASSIST_ENDPOINT, CODE_ASSIST_API_VERSION } from \"./config\";\nimport { REASONING_MESSAGES, REASONING_CHUNK_DELAY, THINKING_CONTENT_CHUNK_SIZE } from \"./constants\";\nimport { geminiCliModels } from \"./models\";\nimport { validateImageUrl } from \"./utils/image-utils\";\nimport { GenerationConfigValidator } from \"./helpers/generation-config-validator\";\nimport { AutoModelSwitchingHelper } from \"./helpers/auto-model-switching\";\n\n// Gemini API response types\ninterface GeminiCandidate {\n\tcontent?: {\n\t\tparts?: Array<{ text?: string }>;\n\t};\n}\n\ninterface GeminiUsageMetadata {\n\tpromptTokenCount?: number;\n\tcandidatesTokenCount?: number;\n}\n\ninterface GeminiResponse {\n\tresponse?: {\n\t\tcandidates?: GeminiCandidate[];\n\t\tusageMetadata?: GeminiUsageMetadata;\n\t};\n}\n\ninterface GeminiPart {\n\ttext?: string;\n\tthought?: boolean; // For real thinking chunks from Gemini\n\tfunctionCall?: {\n\t\tname: string;\n\t\targs: object;\n\t};\n\tfunctionResponse?: {\n\t\tname: string;\n\t\tresponse: {\n\t\t\tresult: string;\n\t\t};\n\t};\n\tinlineData?: {\n\t\tmimeType: string;\n\t\tdata: string;\n\t};\n\tfileData?: {\n\t\tmimeType: string;\n\t\tfileUri: string;\n\t};\n}\n\n// Message content types - keeping only the local ones needed\ninterface TextContent {\n\ttype: \"text\";\n\ttext: string;\n}\n\ninterface GeminiFormattedMessage {\n\trole: string;\n\tparts: GeminiPart[];\n}\n\ninterface ProjectDiscoveryResponse {\n\tcloudaicompanionProject?: string;\n}\n\n// Type guard functions\nfunction isTextContent(content: MessageContent): content is TextContent {\n\treturn content.type === \"text\" && typeof content.text === \"string\";\n}\n\n/**\n * Handles communication with Google's Gemini API through the Code Assist endpoint.\n * Manages project discovery, streaming, and response parsing.\n */\nexport class GeminiApiClient {\n\tprivate env: Env;\n\tprivate authManager: AuthManager;\n\tprivate projectId: string | null = null;\n\tprivate autoSwitchHelper: AutoModelSwitchingHelper;\n\n\tconstructor(env: Env, authManager: AuthManager) {\n\t\tthis.env = env;\n\t\tthis.authManager = authManager;\n\t\tthis.autoSwitchHelper = new AutoModelSwitchingHelper(env);\n\t}\n\n\t/**\n\t * Discovers the Google Cloud project ID. Uses the environment variable if provided.\n\t */\n\tpublic async discoverProjectId(): Promise<string> {\n\t\tif (this.env.GEMINI_PROJECT_ID) {\n\t\t\treturn this.env.GEMINI_PROJECT_ID;\n\t\t}\n\t\tif (this.projectId) {\n\t\t\treturn this.projectId;\n\t\t}\n\n\t\ttry {\n\t\t\tconst initialProjectId = \"default-project\";\n\t\t\tconst loadResponse = (await this.authManager.callEndpoint(\"loadCodeAssist\", {\n\t\t\t\tcloudaicompanionProject: initialProjectId,\n\t\t\t\tmetadata: { duetProject: initialProjectId }\n\t\t\t})) as ProjectDiscoveryResponse;\n\n\t\t\tif (loadResponse.cloudaicompanionProject) {\n\t\t\t\tthis.projectId = loadResponse.cloudaicompanionProject;\n\t\t\t\treturn loadResponse.cloudaicompanionProject;\n\t\t\t}\n\t\t\tthrow new Error(\"Project ID discovery failed. Please set the GEMINI_PROJECT_ID environment variable.\");\n\t\t} catch (error: unknown) {\n\t\t\tconst errorMessage = error instanceof Error ? error.message : String(error);\n\t\t\tconsole.error(\"Failed to discover project ID:\", errorMessage);\n\t\t\tthrow new Error(\n\t\t\t\t\"Could not discover project ID. Make sure you're authenticated and consider setting GEMINI_PROJECT_ID.\"\n\t\t\t);\n\t\t}\n\t}\n\n\t/**\n\t * Parses a server-sent event (SSE) stream from the Gemini API.\n\t */\n\tprivate async *parseSSEStream(stream: ReadableStream<Uint8Array>): AsyncGenerator<GeminiResponse> {\n\t\tconst reader = stream.pipeThrough(new TextDecoderStream()).getReader();\n\t\tlet buffer = \"\";\n\t\tlet objectBuffer = \"\";\n\n\t\twhile (true) {\n\t\t\tconst { done, value } = await reader.read();\n\t\t\tif (done) {\n\t\t\t\tif (objectBuffer) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tyield JSON.parse(objectBuffer);\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error(\"Error parsing final SSE JSON object:\", e);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tbuffer += value;\n\t\t\tconst lines = buffer.split(\"\\n\");\n\t\t\tbuffer = lines.pop() || \"\"; // Keep the last, possibly incomplete, line.\n\n\t\t\tfor (const line of lines) {\n\t\t\t\tif (line.trim() === \"\") {\n\t\t\t\t\tif (objectBuffer) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tyield JSON.parse(objectBuffer);\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tconsole.error(\"Error parsing SSE JSON object:\", e);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tobjectBuffer = \"\";\n\t\t\t\t\t}\n\t\t\t\t} else if (line.startsWith(\"data: \")) {\n\t\t\t\t\tobjectBuffer += line.substring(6);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Converts a message to Gemini format, handling both text and image content.\n\t */\n\tprivate messageToGeminiFormat(msg: ChatMessage): GeminiFormattedMessage {\n\t\tconst role = msg.role === \"assistant\" ? \"model\" : \"user\";\n\n\t\t// Handle tool call results (tool role in OpenAI format)\n\t\tif (msg.role === \"tool\") {\n\t\t\treturn {\n\t\t\t\trole: \"user\",\n\t\t\t\tparts: [\n\t\t\t\t\t{\n\t\t\t\t\t\tfunctionResponse: {\n\t\t\t\t\t\t\tname: msg.tool_call_id || \"unknown_function\",\n\t\t\t\t\t\t\tresponse: {\n\t\t\t\t\t\t\t\tresult: typeof msg.content === \"string\" ? msg.content : JSON.stringify(msg.content)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t};\n\t\t}\n\n\t\t// Handle assistant messages with tool calls\n\t\tif (msg.role === \"assistant\" && msg.tool_calls && msg.tool_calls.length > 0) {\n\t\t\tconst parts: GeminiPart[] = [];\n\n\t\t\t// Add text content if present\n\t\t\tif (typeof msg.content === \"string\" && msg.content.trim()) {\n\t\t\t\tparts.push({ text: msg.content });\n\t\t\t}\n\n\t\t\t// Add function calls\n\t\t\tfor (const toolCall of msg.tool_calls) {\n\t\t\t\tif (toolCall.type === \"function\") {\n\t\t\t\t\tparts.push({\n\t\t\t\t\t\tfunctionCall: {\n\t\t\t\t\t\t\tname: toolCall.function.name,\n\t\t\t\t\t\t\targs: JSON.parse(toolCall.function.arguments)\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn { role: \"model\", parts };\n\t\t}\n\n\t\tif (typeof msg.content === \"string\") {\n\t\t\t// Simple text message\n\t\t\treturn {\n\t\t\t\trole,\n\t\t\t\tparts: [{ text: msg.content }]\n\t\t\t};\n\t\t}\n\n\t\tif (Array.isArray(msg.content)) {\n\t\t\t// Multimodal message with text and/or images\n\t\t\tconst parts: GeminiPart[] = [];\n\n\t\t\tfor (const content of msg.content) {\n\t\t\t\tif (content.type === \"text\") {\n\t\t\t\t\tparts.push({ text: content.text });\n\t\t\t\t} else if (content.type === \"image_url\" && content.image_url) {\n\t\t\t\t\tconst imageUrl = content.image_url.url;\n\n\t\t\t\t\t// Validate image URL\n\t\t\t\t\tconst validation = validateImageUrl(imageUrl);\n\t\t\t\t\tif (!validation.isValid) {\n\t\t\t\t\t\tthrow new Error(`Invalid image: ${validation.error}`);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (imageUrl.startsWith(\"data:\")) {\n\t\t\t\t\t\t// Handle base64 encoded images\n\t\t\t\t\t\tconst [mimeType, base64Data] = imageUrl.split(\",\");\n\t\t\t\t\t\tconst mediaType = mimeType.split(\":\")[1].split(\";\")[0];\n\n\t\t\t\t\t\tparts.push({\n\t\t\t\t\t\t\tinlineData: {\n\t\t\t\t\t\t\t\tmimeType: mediaType,\n\t\t\t\t\t\t\t\tdata: base64Data\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// Handle URL images\n\t\t\t\t\t\t// Note: For better reliability, you might want to fetch the image\n\t\t\t\t\t\t// and convert it to base64, as Gemini API might have limitations with external URLs\n\t\t\t\t\t\tparts.push({\n\t\t\t\t\t\t\tfileData: {\n\t\t\t\t\t\t\t\tmimeType: validation.mimeType || \"image/jpeg\",\n\t\t\t\t\t\t\t\tfileUri: imageUrl\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn { role, parts };\n\t\t}\n\n\t\t// Fallback for unexpected content format\n\t\treturn {\n\t\t\trole,\n\t\t\tparts: [{ text: String(msg.content) }]\n\t\t};\n\t}\n\n\t/**\n\t * Validates if the model supports images.\n\t */\n\tprivate validateImageSupport(modelId: string): boolean {\n\t\treturn geminiCliModels[modelId]?.supportsImages || false;\n\t}\n\n\t/**\n\t * Validates image content and format using the shared validation utility.\n\t */\n\tprivate validateImageContent(imageUrl: string): boolean {\n\t\tconst validation = validateImageUrl(imageUrl);\n\t\treturn validation.isValid;\n\t}\n\n\t/**\n\t * Stream content from Gemini API.\n\t */\n\tasync *streamContent(\n\t\tmodelId: string,\n\t\tsystemPrompt: string,\n\t\tmessages: ChatMessage[],\n\t\toptions?: {\n\t\t\tincludeReasoning?: boolean;\n\t\t\tthinkingBudget?: number;\n\t\t\ttools?: Tool[];\n\t\t\ttool_choice?: ToolChoice;\n\t\t\tmax_tokens?: number;\n\t\t\ttemperature?: number;\n\t\t\ttop_p?: number;\n\t\t\tstop?: string | string[];\n\t\t\tpresence_penalty?: number;\n\t\t\tfrequency_penalty?: number;\n\t\t\tseed?: number;\n\t\t\tresponse_format?: {\n\t\t\t\ttype: \"text\" | \"json_object\";\n\t\t\t};\n\t\t}\n\t): AsyncGenerator<StreamChunk> {\n\t\tawait this.authManager.initializeAuth();\n\t\tconst projectId = await this.discoverProjectId();\n\n\t\tconst contents = messages.map((msg) => this.messageToGeminiFormat(msg));\n\n\t\tif (systemPrompt) {\n\t\t\tcontents.unshift({ role: \"user\", parts: [{ text: systemPrompt }] });\n\t\t}\n\n\t\t// Check if this is a thinking model and which thinking mode to use\n\t\tconst isThinkingModel = geminiCliModels[modelId]?.thinking || false;\n\t\tconst isRealThinkingEnabled = this.env.ENABLE_REAL_THINKING === \"true\";\n\t\tconst isFakeThinkingEnabled = this.env.ENABLE_FAKE_THINKING === \"true\";\n\t\tconst streamThinkingAsContent = this.env.STREAM_THINKING_AS_CONTENT === \"true\";\n\t\tconst includeReasoning = options?.includeReasoning || false;\n\n\t\tconst req = {\n\t\t\tthinking_budget: options?.thinkingBudget,\n\t\t\ttools: options?.tools,\n\t\t\ttool_choice: options?.tool_choice,\n\t\t\tmax_tokens: options?.max_tokens,\n\t\t\ttemperature: options?.temperature,\n\t\t\ttop_p: options?.top_p,\n\t\t\tstop: options?.stop,\n\t\t\tpresence_penalty: options?.presence_penalty,\n\t\t\tfrequency_penalty: options?.frequency_penalty,\n\t\t\tseed: options?.seed,\n\t\t\tresponse_format: options?.response_format\n\t\t};\n\n\t\t// Use the validation helper to create a proper generation config\n\t\tconst generationConfig = GenerationConfigValidator.createValidatedConfig(\n\t\t\tmodelId,\n\t\t\treq,\n\t\t\tisRealThinkingEnabled,\n\t\t\tincludeReasoning,\n\t\t\tthis.env\n\t\t);\n\n\t\tconst { tools, toolConfig } = GenerationConfigValidator.createValidateTools(req);\n\n\t\t// For thinking models with fake thinking (fallback when real thinking is not enabled or not requested)\n\t\tlet needsThinkingClose = false;\n\t\tif (isThinkingModel && isFakeThinkingEnabled && !includeReasoning) {\n\t\t\tyield* this.generateReasoningOutput(modelId, messages, streamThinkingAsContent);\n\t\t\tneedsThinkingClose = streamThinkingAsContent; // Only need to close if we streamed as content\n\t\t}\n\n\t\tconst streamRequest = {\n\t\t\tmodel: modelId,\n\t\t\tproject: projectId,\n\t\t\trequest: {\n\t\t\t\tcontents: contents,\n\t\t\t\tgenerationConfig,\n\t\t\t\ttools: tools,\n\t\t\t\ttoolConfig\n\t\t\t}\n\t\t};\n\n\t\tyield* this.performStreamRequest(\n\t\t\tstreamRequest,\n\t\t\tneedsThinkingClose,\n\t\t\tfalse,\n\t\t\tincludeReasoning && streamThinkingAsContent,\n\t\t\tmodelId\n\t\t);\n\t}\n\n\t/**\n\t * Generates reasoning output for thinking models.\n\t */\n\tprivate async *generateReasoningOutput(\n\t\tmodelId: string,\n\t\tmessages: ChatMessage[],\n\t\tstreamAsContent: boolean = false\n\t): AsyncGenerator<StreamChunk> {\n\t\t// Get the last user message to understand what the model should think about\n\t\tconst lastUserMessage = messages.filter((msg) => msg.role === \"user\").pop();\n\t\tlet userContent = \"\";\n\n\t\tif (lastUserMessage) {\n\t\t\tif (typeof lastUserMessage.content === \"string\") {\n\t\t\t\tuserContent = lastUserMessage.content;\n\t\t\t} else if (Array.isArray(lastUserMessage.content)) {\n\t\t\t\tuserContent = lastUserMessage.content\n\t\t\t\t\t.filter(isTextContent)\n\t\t\t\t\t.map((c) => c.text)\n\t\t\t\t\t.join(\" \");\n\t\t\t}\n\t\t}\n\n\t\t// Generate reasoning text based on the user's question using constants\n\t\tconst requestPreview = userContent.substring(0, 100) + (userContent.length > 100 ? \"...\" : \"\");\n\n\t\tif (streamAsContent) {\n\t\t\t// DeepSeek R1 style: stream thinking as content with <thinking> tags\n\t\t\tyield {\n\t\t\t\ttype: \"thinking_content\",\n\t\t\t\tdata: \"<thinking>\\n\"\n\t\t\t};\n\n\t\t\t// Add a small delay after opening tag\n\t\t\tawait new Promise((resolve) => setTimeout(resolve, REASONING_CHUNK_DELAY)); // Stream reasoning content in smaller chunks for more realistic streaming\n\t\t\tconst reasoningTexts = REASONING_MESSAGES.map((msg) => msg.replace(\"{requestPreview}\", requestPreview));\n\t\t\tconst fullReasoningText = reasoningTexts.join(\"\");\n\n\t\t\t// Split into smaller chunks for more realistic streaming\n\t\t\t// Try to split on word boundaries when possible for better readability\n\t\t\tconst chunks: string[] = [];\n\t\t\tlet remainingText = fullReasoningText;\n\n\t\t\twhile (remainingText.length > 0) {\n\t\t\t\tif (remainingText.length <= THINKING_CONTENT_CHUNK_SIZE) {\n\t\t\t\t\tchunks.push(remainingText);\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\t// Try to find a good break point (space, newline, punctuation)\n\t\t\t\tlet chunkEnd = THINKING_CONTENT_CHUNK_SIZE;\n\t\t\t\tconst searchSpace = remainingText.substring(0, chunkEnd + 10); // Look a bit ahead\n\t\t\t\tconst goodBreaks = [\" \", \"\\n\", \".\", \",\", \"!\", \"?\", \";\", \":\"];\n\n\t\t\t\tfor (const breakChar of goodBreaks) {\n\t\t\t\t\tconst lastBreak = searchSpace.lastIndexOf(breakChar);\n\t\t\t\t\tif (lastBreak > THINKING_CONTENT_CHUNK_SIZE * 0.7) {\n\t\t\t\t\t\t// Don't make chunks too small\n\t\t\t\t\t\tchunkEnd = lastBreak + 1;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tchunks.push(remainingText.substring(0, chunkEnd));\n\t\t\t\tremainingText = remainingText.substring(chunkEnd);\n\t\t\t}\n\n\t\t\tfor (const chunk of chunks) {\n\t\t\t\tyield {\n\t\t\t\t\ttype: \"thinking_content\",\n\t\t\t\t\tdata: chunk\n\t\t\t\t};\n\n\t\t\t\t// Add small delay between chunks\n\t\t\t\tawait new Promise((resolve) => setTimeout(resolve, 50));\n\t\t\t}\n\n\t\t\t// Note: We don't close the thinking tag here - it will be closed when real content starts\n\t\t} else {\n\t\t\t// Original mode: stream as reasoning field\n\t\t\tconst reasoningTexts = REASONING_MESSAGES.map((msg) => msg.replace(\"{requestPreview}\", requestPreview));\n\n\t\t\t// Stream the reasoning text in chunks\n\t\t\tfor (const reasoningText of reasoningTexts) {\n\t\t\t\tconst reasoningData: ReasoningData = { reasoning: reasoningText };\n\t\t\t\tyield {\n\t\t\t\t\ttype: \"reasoning\",\n\t\t\t\t\tdata: reasoningData\n\t\t\t\t};\n\n\t\t\t\t// Add a small delay to simulate thinking time\n\t\t\t\tawait new Promise((resolve) => setTimeout(resolve, REASONING_CHUNK_DELAY));\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Performs the actual stream request with retry logic for 401 errors and auto model switching for rate limits.\n\t */\n\tprivate async *performStreamRequest(\n\t\tstreamRequest: unknown,\n\t\tneedsThinkingClose: boolean = false,\n\t\tisRetry: boolean = false,\n\t\trealThinkingAsContent: boolean = false,\n\t\toriginalModel?: string\n\t): AsyncGenerator<StreamChunk> {\n\t\tconst response = await fetch(`${CODE_ASSIST_ENDPOINT}/${CODE_ASSIST_API_VERSION}:streamGenerateContent?alt=sse`, {\n\t\t\tmethod: \"POST\",\n\t\t\theaders: {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\tAuthorization: `Bearer ${this.authManager.getAccessToken()}`\n\t\t\t},\n\t\t\tbody: JSON.stringify(streamRequest)\n\t\t});\n\n\t\tif (!response.ok) {\n\t\t\tif (response.status === 401 && !isRetry) {\n\t\t\t\tconsole.log(\"Got 401 error in stream request, clearing token cache and retrying...\");\n\t\t\t\tawait this.authManager.clearTokenCache();\n\t\t\t\tawait this.authManager.initializeAuth();\n\t\t\t\tyield* this.performStreamRequest(streamRequest, needsThinkingClose, true, realThinkingAsContent, originalModel); // Retry once\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Handle rate limiting with auto model switching\n\t\t\tif (this.autoSwitchHelper.isRateLimitStatus(response.status) && !isRetry && originalModel) {\n\t\t\t\tconst fallbackModel = this.autoSwitchHelper.getFallbackModel(originalModel);\n\t\t\t\tif (fallbackModel && this.autoSwitchHelper.isEnabled()) {\n\t\t\t\t\tconsole.log(\n\t\t\t\t\t\t`Got ${response.status} error for model ${originalModel}, switching to fallback model: ${fallbackModel}`\n\t\t\t\t\t);\n\n\t\t\t\t\t// Create new request with fallback model\n\t\t\t\t\tconst fallbackRequest = {\n\t\t\t\t\t\t...(streamRequest as Record<string, unknown>),\n\t\t\t\t\t\tmodel: fallbackModel\n\t\t\t\t\t};\n\n\t\t\t\t\t// Add a notification chunk about the model switch\n\t\t\t\t\tyield {\n\t\t\t\t\t\ttype: \"text\",\n\t\t\t\t\t\tdata: this.autoSwitchHelper.createSwitchNotification(originalModel, fallbackModel)\n\t\t\t\t\t};\n\n\t\t\t\t\tyield* this.performStreamRequest(\n\t\t\t\t\t\tfallbackRequest,\n\t\t\t\t\t\tneedsThinkingClose,\n\t\t\t\t\t\ttrue,\n\t\t\t\t\t\trealThinkingAsContent,\n\t\t\t\t\t\toriginalModel\n\t\t\t\t\t);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst errorText = await response.text();\n\t\t\tconsole.error(`[GeminiAPI] Stream request failed: ${response.status}`, errorText);\n\t\t\tthrow new Error(`Stream request failed: ${response.status}`);\n\t\t}\n\n\t\tif (!response.body) {\n\t\t\tthrow new Error(\"Response has no body\");\n\t\t}\n\n\t\tlet hasClosedThinking = false;\n\t\tlet hasStartedThinking = false;\n\n\t\tfor await (const jsonData of this.parseSSEStream(response.body)) {\n\t\t\tconst candidate = jsonData.response?.candidates?.[0];\n\n\t\t\tif (candidate?.content?.parts) {\n\t\t\t\tfor (const part of candidate.content.parts as GeminiPart[]) {\n\t\t\t\t\t// Handle real thinking content from Gemini\n\t\t\t\t\tif (part.thought === true && part.text) {\n\t\t\t\t\t\tconst thinkingText = part.text;\n\n\t\t\t\t\t\tif (realThinkingAsContent) {\n\t\t\t\t\t\t\t// Stream as content with <thinking> tags (DeepSeek R1 style)\n\t\t\t\t\t\t\tif (!hasStartedThinking) {\n\t\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\t\ttype: \"thinking_content\",\n\t\t\t\t\t\t\t\t\tdata: \"<thinking>\\n\"\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\thasStartedThinking = true;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\ttype: \"thinking_content\",\n\t\t\t\t\t\t\t\tdata: thinkingText\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// Stream as separate reasoning field\n\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\ttype: \"real_thinking\",\n\t\t\t\t\t\t\t\tdata: thinkingText\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t// Check if text content contains <think> tags (based on your original example)\n\t\t\t\t\telse if (part.text && part.text.includes(\"<think>\")) {\n\t\t\t\t\t\tif (realThinkingAsContent) {\n\t\t\t\t\t\t\t// Extract thinking content and convert to our format\n\t\t\t\t\t\t\tconst thinkingMatch = part.text.match(/<think>(.*?)<\\/think>/s);\n\t\t\t\t\t\t\tif (thinkingMatch) {\n\t\t\t\t\t\t\t\tif (!hasStartedThinking) {\n\t\t\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\t\t\ttype: \"thinking_content\",\n\t\t\t\t\t\t\t\t\t\tdata: \"<thinking>\\n\"\n\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\thasStartedThinking = true;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\t\ttype: \"thinking_content\",\n\t\t\t\t\t\t\t\t\tdata: thinkingMatch[1]\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// Extract any non-thinking coRecentent\n\t\t\t\t\t\t\tconst nonThinkingContent = part.text.replace(/<think>.*?<\\/think>/gs, \"\").trim();\n\t\t\t\t\t\t\tif (nonThinkingContent) {\n\t\t\t\t\t\t\t\tif (hasStartedThinking && !hasClosedThinking) {\n\t\t\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\t\t\ttype: \"thinking_content\",\n\t\t\t\t\t\t\t\t\t\tdata: \"\\n</thinking>\\n\\n\"\n\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\thasClosedThinking = true;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tyield { type: \"text\", data: nonThinkingContent };\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// Stream thinking as separate reasoning field\n\t\t\t\t\t\t\tconst thinkingMatch = part.text.match(/<think>(.*?)<\\/think>/s);\n\t\t\t\t\t\t\tif (thinkingMatch) {\n\t\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\t\ttype: \"real_thinking\",\n\t\t\t\t\t\t\t\t\tdata: thinkingMatch[1]\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// Stream non-thinking content as regular text\n\t\t\t\t\t\t\tconst nonThinkingContent = part.text.replace(/<think>.*?<\\/think>/gs, \"\").trim();\n\t\t\t\t\t\t\tif (nonThinkingContent) {\n\t\t\t\t\t\t\t\tyield { type: \"text\", data: nonThinkingContent };\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t// Handle regular content - only if it's not a thinking part and doesn't contain <think> tags\n\t\t\t\t\telse if (part.text && !part.thought && !part.text.includes(\"<think>\")) {\n\t\t\t\t\t\t// Close thinking tag before first real content if needed\n\t\t\t\t\t\tif ((needsThinkingClose || (realThinkingAsContent && hasStartedThinking)) && !hasClosedThinking) {\n\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\ttype: \"thinking_content\",\n\t\t\t\t\t\t\t\tdata: \"\\n</thinking>\\n\\n\"\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\thasClosedThinking = true;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tyield { type: \"text\", data: part.text };\n\t\t\t\t\t}\n\t\t\t\t\t// Handle function calls from Gemini\n\t\t\t\t\telse if (part.functionCall) {\n\t\t\t\t\t\t// Close thinking tag before function call if needed\n\t\t\t\t\t\tif ((needsThinkingClose || (realThinkingAsContent && hasStartedThinking)) && !hasClosedThinking) {\n\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\ttype: \"thinking_content\",\n\t\t\t\t\t\t\t\tdata: \"\\n</thinking>\\n\\n\"\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\thasClosedThinking = true;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst functionCallData: GeminiFunctionCall = {\n\t\t\t\t\t\t\tname: part.functionCall.name,\n\t\t\t\t\t\t\targs: part.functionCall.args\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\ttype: \"tool_code\",\n\t\t\t\t\t\t\tdata: functionCallData\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\t// Note: Skipping unknown part structures\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (jsonData.response?.usageMetadata) {\n\t\t\t\tconst usage = jsonData.response.usageMetadata;\n\t\t\t\tconst usageData: UsageData = {\n\t\t\t\t\tinputTokens: usage.promptTokenCount || 0,\n\t\t\t\t\toutputTokens: usage.candidatesTokenCount || 0\n\t\t\t\t};\n\t\t\t\tyield {\n\t\t\t\t\ttype: \"usage\",\n\t\t\t\t\tdata: usageData\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Get a complete response from Gemini API (non-streaming).\n\t */\n\tasync getCompletion(\n\t\tmodelId: string,\n\t\tsystemPrompt: string,\n\t\tmessages: ChatMessage[],\n\t\toptions?: {\n\t\t\tincludeReasoning?: boolean;\n\t\t\tthinkingBudget?: number;\n\t\t\ttools?: Tool[];\n\t\t\ttool_choice?: ToolChoice;\n\t\t\tmax_tokens?: number;\n\t\t\ttemperature?: number;\n\t\t\ttop_p?: number;\n\t\t\tstop?: string | string[];\n\t\t\tpresence_penalty?: number;\n\t\t\tfrequency_penalty?: number;\n\t\t\tseed?: number;\n\t\t\tresponse_format?: {\n\t\t\t\ttype: \"text\" | \"json_object\";\n\t\t\t};\n\t\t}\n\t): Promise<{\n\t\tcontent: string;\n\t\tusage?: UsageData;\n\t\ttool_calls?: Array<{ id: string; type: \"function\"; function: { name: string; arguments: string } }>;\n\t}> {\n\t\ttry {\n\t\t\tlet content = \"\";\n\t\t\tlet usage: UsageData | undefined;\n\t\t\tconst tool_calls: Array<{ id: string; type: \"function\"; function: { name: string; arguments: string } }> = [];\n\n\t\t\t// Collect all chunks from the stream\n\t\t\tfor await (const chunk of this.streamContent(modelId, systemPrompt, messages, options)) {\n\t\t\t\tif (chunk.type === \"text\" && typeof chunk.data === \"string\") {\n\t\t\t\t\tcontent += chunk.data;\n\t\t\t\t} else if (chunk.type === \"usage\" && typeof chunk.data === \"object\") {\n\t\t\t\t\tusage = chunk.data as UsageData;\n\t\t\t\t} else if (chunk.type === \"tool_code\" && typeof chunk.data === \"object\") {\n\t\t\t\t\tconst toolData = chunk.data as GeminiFunctionCall;\n\t\t\t\t\ttool_calls.push({\n\t\t\t\t\t\tid: `call_${crypto.randomUUID()}`,\n\t\t\t\t\t\ttype: \"function\",\n\t\t\t\t\t\tfunction: {\n\t\t\t\t\t\t\tname: toolData.name,\n\t\t\t\t\t\t\targuments: JSON.stringify(toolData.args)\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t// Skip reasoning chunks for non-streaming responses\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\tcontent,\n\t\t\t\tusage,\n\t\t\t\ttool_calls: tool_calls.length > 0 ? tool_calls : undefined\n\t\t\t};\n\t\t} catch (error: unknown) {\n\t\t\t// Handle rate limiting for non-streaming requests\n\t\t\tif (this.autoSwitchHelper.isRateLimitError(error)) {\n\t\t\t\tconst fallbackResult = await this.autoSwitchHelper.handleNonStreamingFallback(\n\t\t\t\t\tmodelId,\n\t\t\t\t\tsystemPrompt,\n\t\t\t\t\tmessages,\n\t\t\t\t\toptions,\n\t\t\t\t\tthis.streamContent.bind(this)\n\t\t\t\t);\n\t\t\t\tif (fallbackResult) {\n\t\t\t\t\treturn fallbackResult;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Re-throw if not a rate limit error or fallback not available\n\t\t\tthrow error;\n\t\t}\n\t}\n}\n", "import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ing<PERSON><PERSON>, GeminiFunction<PERSON>all, UsageData } from \"./types\";\nimport { OPENAI_CHAT_COMPLETION_OBJECT } from \"./config\";\n\n// OpenAI API interfaces\ninterface OpenAIToolCall {\n\tindex: number;\n\tid: string;\n\ttype: \"function\";\n\tfunction: {\n\t\tname: string;\n\t\targuments: string;\n\t};\n}\n\ninterface OpenAIChoice {\n\tindex: number;\n\tdelta: OpenAIDelta;\n\tfinish_reason: string | null;\n\tlogprobs?: null;\n\tmatched_stop?: null;\n}\n\ninterface OpenAIDelta {\n\trole?: string;\n\tcontent?: string | null;\n\treasoning?: string;\n\treasoning_content?: string | null;\n\ttool_calls?: OpenAIToolCall[];\n}\n\ninterface OpenAIChunk {\n\tid: string;\n\tobject: string;\n\tcreated: number;\n\tmodel: string;\n\tchoices: OpenAIChoice[];\n\tusage?: null;\n}\n\ninterface OpenAIFinalChoice {\n\tindex: number;\n\tdelta: Record<string, never>;\n\tfinish_reason: string;\n}\n\ninterface OpenAIUsage {\n\tprompt_tokens: number;\n\tcompletion_tokens: number;\n\ttotal_tokens: number;\n}\n\ninterface OpenAIFinalChunk {\n\tid: string;\n\tobject: string;\n\tcreated: number;\n\tmodel: string;\n\tchoices: OpenAIFinalChoice[];\n\tusage?: OpenAIUsage;\n}\n\n// Type guard functions\nfunction isReasoningData(data: unknown): data is ReasoningData {\n\treturn typeof data === \"object\" && data !== null && (\"reasoning\" in data || \"toolCode\" in data);\n}\n\nfunction isGeminiFunctionCall(data: unknown): data is GeminiFunctionCall {\n\treturn typeof data === \"object\" && data !== null && \"name\" in data && \"args\" in data;\n}\n\nfunction isUsageData(data: unknown): data is UsageData {\n\treturn typeof data === \"object\" && data !== null && \"inputTokens\" in data && \"outputTokens\" in data;\n}\n\n/**\n * Creates a TransformStream to convert Gemini's output chunks\n * into OpenAI-compatible server-sent events.\n */\nexport function createOpenAIStreamTransformer(model: string): TransformStream<StreamChunk, Uint8Array> {\n\tconst chatID = `chatcmpl-${crypto.randomUUID()}`;\n\tconst creationTime = Math.floor(Date.now() / 1000);\n\tconst encoder = new TextEncoder();\n\tlet firstChunk = true;\n\tlet toolCallId: string | null = null;\n\tlet toolCallName: string | null = null;\n\tlet usageData: UsageData | undefined;\n\n\treturn new TransformStream({\n\t\ttransform(chunk, controller) {\n\t\t\tif (chunk.type === \"text\" && chunk.data && typeof chunk.data === \"string\") {\n\t\t\t\tconst delta: OpenAIDelta = {\n\t\t\t\t\tcontent: chunk.data\n\t\t\t\t};\n\t\t\t\tif (firstChunk) {\n\t\t\t\t\tdelta.role = \"assistant\";\n\t\t\t\t\tfirstChunk = false;\n\t\t\t\t}\n\n\t\t\t\tconst openAIChunk: OpenAIChunk = {\n\t\t\t\t\tid: chatID,\n\t\t\t\t\tobject: OPENAI_CHAT_COMPLETION_OBJECT,\n\t\t\t\t\tcreated: creationTime,\n\t\t\t\t\tmodel: model,\n\t\t\t\t\tchoices: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tindex: 0,\n\t\t\t\t\t\t\tdelta: delta,\n\t\t\t\t\t\t\tfinish_reason: null,\n\t\t\t\t\t\t\tlogprobs: null,\n\t\t\t\t\t\t\tmatched_stop: null\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t\tusage: null\n\t\t\t\t};\n\t\t\t\tcontroller.enqueue(encoder.encode(`data: ${JSON.stringify(openAIChunk)}\\n\\n`));\n\t\t\t} else if (chunk.type === \"thinking_content\" && chunk.data && typeof chunk.data === \"string\") {\n\t\t\t\t// Handle thinking content streamed as regular content (DeepSeek R1 style)\n\t\t\t\tconst delta: OpenAIDelta = {\n\t\t\t\t\tcontent: chunk.data,\n\t\t\t\t\treasoning_content: null\n\t\t\t\t};\n\t\t\t\tif (firstChunk) {\n\t\t\t\t\tdelta.role = \"assistant\";\n\t\t\t\t\tfirstChunk = false;\n\t\t\t\t}\n\n\t\t\t\tconst openAIChunk: OpenAIChunk = {\n\t\t\t\t\tid: chatID,\n\t\t\t\t\tobject: OPENAI_CHAT_COMPLETION_OBJECT,\n\t\t\t\t\tcreated: creationTime,\n\t\t\t\t\tmodel: model,\n\t\t\t\t\tchoices: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tindex: 0,\n\t\t\t\t\t\t\tdelta: delta,\n\t\t\t\t\t\t\tfinish_reason: null,\n\t\t\t\t\t\t\tlogprobs: null,\n\t\t\t\t\t\t\tmatched_stop: null\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t\tusage: null\n\t\t\t\t};\n\t\t\t\tcontroller.enqueue(encoder.encode(`data: ${JSON.stringify(openAIChunk)}\\n\\n`));\n\t\t\t} else if (chunk.type === \"real_thinking\" && chunk.data && typeof chunk.data === \"string\") {\n\t\t\t\t// Handle real thinking content from Gemini\n\t\t\t\tconst delta: OpenAIDelta = {\n\t\t\t\t\treasoning: chunk.data,\n\t\t\t\t\treasoning_content: null\n\t\t\t\t};\n\n\t\t\t\tconst openAIChunk: OpenAIChunk = {\n\t\t\t\t\tid: chatID,\n\t\t\t\t\tobject: OPENAI_CHAT_COMPLETION_OBJECT,\n\t\t\t\t\tcreated: creationTime,\n\t\t\t\t\tmodel: model,\n\t\t\t\t\tchoices: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tindex: 0,\n\t\t\t\t\t\t\tdelta: delta,\n\t\t\t\t\t\t\tfinish_reason: null,\n\t\t\t\t\t\t\tlogprobs: null,\n\t\t\t\t\t\t\tmatched_stop: null\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t\tusage: null\n\t\t\t\t};\n\t\t\t\tcontroller.enqueue(encoder.encode(`data: ${JSON.stringify(openAIChunk)}\\n\\n`));\n\t\t\t} else if (chunk.type === \"reasoning\" && isReasoningData(chunk.data)) {\n\t\t\t\t// Handle thinking/reasoning chunks (original format)\n\t\t\t\tconst delta: OpenAIDelta = {\n\t\t\t\t\treasoning: chunk.data.reasoning,\n\t\t\t\t\treasoning_content: null\n\t\t\t\t};\n\n\t\t\t\tconst openAIChunk: OpenAIChunk = {\n\t\t\t\t\tid: chatID,\n\t\t\t\t\tobject: OPENAI_CHAT_COMPLETION_OBJECT,\n\t\t\t\t\tcreated: creationTime,\n\t\t\t\t\tmodel: model,\n\t\t\t\t\tchoices: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tindex: 0,\n\t\t\t\t\t\t\tdelta: delta,\n\t\t\t\t\t\t\tfinish_reason: null,\n\t\t\t\t\t\t\tlogprobs: null,\n\t\t\t\t\t\t\tmatched_stop: null\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t\tusage: null\n\t\t\t\t};\n\t\t\t\tcontroller.enqueue(encoder.encode(`data: ${JSON.stringify(openAIChunk)}\\n\\n`));\n\t\t\t} else if (chunk.type === \"tool_code\" && isGeminiFunctionCall(chunk.data)) {\n\t\t\t\tconst toolData = chunk.data;\n\t\t\t\tconst toolCode = toolData.args;\n\t\t\t\tconst functionName = toolData.name;\n\n\t\t\t\tif (functionName) {\n\t\t\t\t\ttoolCallName = functionName;\n\t\t\t\t\ttoolCallId = `call_${crypto.randomUUID()}`;\n\t\t\t\t}\n\n\t\t\t\tconst delta: OpenAIDelta = {\n\t\t\t\t\ttool_calls: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tindex: 0,\n\t\t\t\t\t\t\tid: toolCallId || \"\",\n\t\t\t\t\t\t\ttype: \"function\",\n\t\t\t\t\t\t\tfunction: {\n\t\t\t\t\t\t\t\tname: toolCallName || \"\",\n\t\t\t\t\t\t\t\targuments: JSON.stringify(toolCode)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t};\n\n\t\t\t\tif (firstChunk) {\n\t\t\t\t\tdelta.role = \"assistant\";\n\t\t\t\t\tdelta.content = null; // Important: content must be null when tool_calls are present\n\t\t\t\t\tfirstChunk = false;\n\t\t\t\t}\n\n\t\t\t\tconst openAIChunk: OpenAIChunk = {\n\t\t\t\t\tid: chatID,\n\t\t\t\t\tobject: OPENAI_CHAT_COMPLETION_OBJECT,\n\t\t\t\t\tcreated: creationTime,\n\t\t\t\t\tmodel: model,\n\t\t\t\t\tchoices: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tindex: 0,\n\t\t\t\t\t\t\tdelta: delta,\n\t\t\t\t\t\t\tfinish_reason: null,\n\t\t\t\t\t\t\tlogprobs: null,\n\t\t\t\t\t\t\tmatched_stop: null\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t\tusage: null\n\t\t\t\t};\n\t\t\t\tcontroller.enqueue(encoder.encode(`data: ${JSON.stringify(openAIChunk)}\\n\\n`));\n\t\t\t} else if (chunk.type === \"usage\" && isUsageData(chunk.data)) {\n\t\t\t\t// Capture usage data to include in the final chunk\n\t\t\t\tusageData = chunk.data;\n\t\t\t}\n\t\t},\n\t\tflush(controller) {\n\t\t\t// Send the final chunk with the finish reason and usage data if available.\n\t\t\tconst finishReason = toolCallId ? \"tool_calls\" : \"stop\";\n\t\t\tconst finalChunk: OpenAIFinalChunk = {\n\t\t\t\tid: chatID,\n\t\t\t\tobject: OPENAI_CHAT_COMPLETION_OBJECT,\n\t\t\t\tcreated: creationTime,\n\t\t\t\tmodel: model,\n\t\t\t\tchoices: [{ index: 0, delta: {}, finish_reason: finishReason }]\n\t\t\t};\n\n\t\t\t// Include usage data if available\n\t\t\tif (usageData) {\n\t\t\t\tfinalChunk.usage = {\n\t\t\t\t\tprompt_tokens: usageData.inputTokens,\n\t\t\t\t\tcompletion_tokens: usageData.outputTokens,\n\t\t\t\t\ttotal_tokens: usageData.inputTokens + usageData.outputTokens\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tcontroller.enqueue(encoder.encode(`data: ${JSON.stringify(finalChunk)}\\n\\n`));\n\t\t\tcontroller.enqueue(encoder.encode(\"data: [DONE]\\n\\n\"));\n\t\t}\n\t});\n}\n", "import { Hono } from \"hono\";\nimport { Env, ChatCompletionRequest, ChatCompletionResponse } from \"../types\";\nimport { geminiCliModels, DEFAULT_MODEL, getAllModelIds } from \"../models\";\nimport { OPENAI_MODEL_OWNER } from \"../config\";\nimport { DEFAULT_THINKING_BUDGET } from \"../constants\";\nimport { AuthManager } from \"../auth\";\nimport { GeminiApiClient } from \"../gemini-client\";\nimport { createOpenAIStreamTransformer } from \"../stream-transformer\";\n\n/**\n * OpenAI-compatible API routes for models and chat completions.\n */\nexport const OpenAIRoute = new Hono<{ Bindings: Env }>();\n\n// List available models\nOpenAIRoute.get(\"/models\", async (c) => {\n\tconst modelData = getAllModelIds().map((modelId) => ({\n\t\tid: modelId,\n\t\tobject: \"model\",\n\t\tcreated: Math.floor(Date.now() / 1000),\n\t\towned_by: OPENAI_MODEL_OWNER\n\t}));\n\n\treturn c.json({\n\t\tobject: \"list\",\n\t\tdata: modelData\n\t});\n});\n\n// Chat completions endpoint\nOpenAIRoute.post(\"/chat/completions\", async (c) => {\n\ttry {\n\t\tconsole.log(\"Chat completions request received\");\n\t\tconst body = await c.req.json<ChatCompletionRequest>();\n\t\tconst model = body.model || DEFAULT_MODEL;\n\t\tconst messages = body.messages || [];\n\t\t// OpenAI API compatibility: stream defaults to true unless explicitly set to false\n\t\tconst stream = body.stream !== false;\n\n\t\t// Check environment settings for real thinking\n\t\tconst isRealThinkingEnabled = c.env.ENABLE_REAL_THINKING === \"true\";\n\t\tlet includeReasoning = isRealThinkingEnabled; // Automatically enable reasoning when real thinking is enabled\n\t\tlet thinkingBudget = body.thinking_budget ?? DEFAULT_THINKING_BUDGET; // Default to dynamic allocation\n\n\t\t// Newly added parameters\n\t\tconst generationOptions = {\n\t\t\tmax_tokens: body.max_tokens,\n\t\t\ttemperature: body.temperature,\n\t\t\ttop_p: body.top_p,\n\t\t\tstop: body.stop,\n\t\t\tpresence_penalty: body.presence_penalty,\n\t\t\tfrequency_penalty: body.frequency_penalty,\n\t\t\tseed: body.seed,\n\t\t\tresponse_format: body.response_format\n\t\t};\n\n\t\t// Handle effort level mapping to thinking_budget (check multiple locations for client compatibility)\n\t\tconst reasoning_effort =\n\t\t\tbody.reasoning_effort || body.extra_body?.reasoning_effort || body.model_params?.reasoning_effort;\n\t\tif (reasoning_effort) {\n\t\t\tincludeReasoning = true; // Effort implies reasoning\n\t\t\tconst isFlashModel = model.includes(\"flash\");\n\t\t\tswitch (reasoning_effort) {\n\t\t\t\tcase \"low\":\n\t\t\t\t\tthinkingBudget = 1024;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"medium\":\n\t\t\t\t\tthinkingBudget = isFlashModel ? 12288 : 16384;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"high\":\n\t\t\t\t\tthinkingBudget = isFlashModel ? 24576 : 32768;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"none\":\n\t\t\t\t\tthinkingBudget = 0;\n\t\t\t\t\tincludeReasoning = false;\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tconst tools = body.tools;\n\t\tconst tool_choice = body.tool_choice;\n\n\t\tconsole.log(\"Request body parsed:\", {\n\t\t\tmodel,\n\t\t\tmessageCount: messages.length,\n\t\t\tstream,\n\t\t\tincludeReasoning,\n\t\t\tthinkingBudget,\n\t\t\ttools,\n\t\t\ttool_choice\n\t\t});\n\n\t\tif (!messages.length) {\n\t\t\treturn c.json({ error: \"messages is a required field\" }, 400);\n\t\t}\n\n\t\t// Validate model\n\t\tif (!(model in geminiCliModels)) {\n\t\t\treturn c.json(\n\t\t\t\t{\n\t\t\t\t\terror: `Model '${model}' not found. Available models: ${getAllModelIds().join(\", \")}`\n\t\t\t\t},\n\t\t\t\t400\n\t\t\t);\n\t\t}\n\n\t\t// Check if the request contains images and validate model support\n\t\tconst hasImages = messages.some((msg) => {\n\t\t\tif (Array.isArray(msg.content)) {\n\t\t\t\treturn msg.content.some((content) => content.type === \"image_url\");\n\t\t\t}\n\t\t\treturn false;\n\t\t});\n\n\t\tif (hasImages && !geminiCliModels[model].supportsImages) {\n\t\t\treturn c.json(\n\t\t\t\t{\n\t\t\t\t\terror: `Model '${model}' does not support image inputs. Please use a vision-capable model like gemini-2.5-pro or gemini-2.5-flash.`\n\t\t\t\t},\n\t\t\t\t400\n\t\t\t);\n\t\t}\n\n\t\t// Extract system prompt and user/assistant messages\n\t\tlet systemPrompt = \"\";\n\t\tconst otherMessages = messages.filter((msg) => {\n\t\t\tif (msg.role === \"system\") {\n\t\t\t\t// Handle system messages with both string and array content\n\t\t\t\tif (typeof msg.content === \"string\") {\n\t\t\t\t\tsystemPrompt = msg.content;\n\t\t\t\t} else if (Array.isArray(msg.content)) {\n\t\t\t\t\t// For system messages, only extract text content\n\t\t\t\t\tconst textContent = msg.content\n\t\t\t\t\t\t.filter((part) => part.type === \"text\")\n\t\t\t\t\t\t.map((part) => part.text || \"\")\n\t\t\t\t\t\t.join(\" \");\n\t\t\t\t\tsystemPrompt = textContent;\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn true;\n\t\t});\n\n\t\t// Initialize services\n\t\tconst authManager = new AuthManager(c.env);\n\t\tconst geminiClient = new GeminiApiClient(c.env, authManager);\n\n\t\t// Test authentication first\n\t\ttry {\n\t\t\tawait authManager.initializeAuth();\n\t\t\tconsole.log(\"Authentication successful\");\n\t\t} catch (authError: unknown) {\n\t\t\tconst errorMessage = authError instanceof Error ? authError.message : String(authError);\n\t\t\tconsole.error(\"Authentication failed:\", errorMessage);\n\t\t\treturn c.json({ error: \"Authentication failed: \" + errorMessage }, 401);\n\t\t}\n\n\t\tif (stream) {\n\t\t\t// Streaming response\n\t\t\tconst { readable, writable } = new TransformStream();\n\t\t\tconst writer = writable.getWriter();\n\t\t\tconst openAITransformer = createOpenAIStreamTransformer(model);\n\t\t\tconst openAIStream = readable.pipeThrough(openAITransformer);\n\n\t\t\t// Asynchronously pipe data from Gemini to transformer\n\t\t\t(async () => {\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log(\"Starting stream generation\");\n\t\t\t\t\tconst geminiStream = geminiClient.streamContent(model, systemPrompt, otherMessages, {\n\t\t\t\t\t\tincludeReasoning,\n\t\t\t\t\t\tthinkingBudget,\n\t\t\t\t\t\ttools,\n\t\t\t\t\t\ttool_choice,\n\t\t\t\t\t\t...generationOptions\n\t\t\t\t\t});\n\n\t\t\t\t\tfor await (const chunk of geminiStream) {\n\t\t\t\t\t\tawait writer.write(chunk);\n\t\t\t\t\t}\n\t\t\t\t\tconsole.log(\"Stream completed successfully\");\n\t\t\t\t\tawait writer.close();\n\t\t\t\t} catch (streamError: unknown) {\n\t\t\t\t\tconst errorMessage = streamError instanceof Error ? streamError.message : String(streamError);\n\t\t\t\t\tconsole.error(\"Stream error:\", errorMessage);\n\t\t\t\t\t// Try to write an error chunk before closing\n\t\t\t\t\tawait writer.write({\n\t\t\t\t\t\ttype: \"text\",\n\t\t\t\t\t\tdata: `Error: ${errorMessage}`\n\t\t\t\t\t});\n\t\t\t\t\tawait writer.close();\n\t\t\t\t}\n\t\t\t})();\n\n\t\t\t// Return streaming response\n\t\t\tconsole.log(\"Returning streaming response\");\n\t\t\treturn new Response(openAIStream, {\n\t\t\t\theaders: {\n\t\t\t\t\t\"Content-Type\": \"text/event-stream\",\n\t\t\t\t\t\"Cache-Control\": \"no-cache\",\n\t\t\t\t\tConnection: \"keep-alive\",\n\t\t\t\t\t\"Access-Control-Allow-Origin\": \"*\",\n\t\t\t\t\t\"Access-Control-Allow-Methods\": \"GET, POST, OPTIONS\",\n\t\t\t\t\t\"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\t// Non-streaming response\n\t\t\ttry {\n\t\t\t\tconsole.log(\"Starting non-streaming completion\");\n\t\t\t\tconst completion = await geminiClient.getCompletion(model, systemPrompt, otherMessages, {\n\t\t\t\t\tincludeReasoning,\n\t\t\t\t\tthinkingBudget,\n\t\t\t\t\ttools,\n\t\t\t\t\ttool_choice,\n\t\t\t\t\t...generationOptions\n\t\t\t\t});\n\n\t\t\t\tconst response: ChatCompletionResponse = {\n\t\t\t\t\tid: `chatcmpl-${crypto.randomUUID()}`,\n\t\t\t\t\tobject: \"chat.completion\",\n\t\t\t\t\tcreated: Math.floor(Date.now() / 1000),\n\t\t\t\t\tmodel: model,\n\t\t\t\t\tchoices: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tindex: 0,\n\t\t\t\t\t\t\tmessage: {\n\t\t\t\t\t\t\t\trole: \"assistant\",\n\t\t\t\t\t\t\t\tcontent: completion.content,\n\t\t\t\t\t\t\t\ttool_calls: completion.tool_calls\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfinish_reason: completion.tool_calls && completion.tool_calls.length > 0 ? \"tool_calls\" : \"stop\"\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t};\n\n\t\t\t\t// Add usage information if available\n\t\t\t\tif (completion.usage) {\n\t\t\t\t\tresponse.usage = {\n\t\t\t\t\t\tprompt_tokens: completion.usage.inputTokens,\n\t\t\t\t\t\tcompletion_tokens: completion.usage.outputTokens,\n\t\t\t\t\t\ttotal_tokens: completion.usage.inputTokens + completion.usage.outputTokens\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\tconsole.log(\"Non-streaming completion successful\");\n\t\t\t\treturn c.json(response);\n\t\t\t} catch (completionError: unknown) {\n\t\t\t\tconst errorMessage = completionError instanceof Error ? completionError.message : String(completionError);\n\t\t\t\tconsole.error(\"Completion error:\", errorMessage);\n\t\t\t\treturn c.json({ error: errorMessage }, 500);\n\t\t\t}\n\t\t}\n\t} catch (e: unknown) {\n\t\tconst errorMessage = e instanceof Error ? e.message : String(e);\n\t\tconsole.error(\"Top-level error:\", e);\n\t\treturn c.json({ error: errorMessage }, 500);\n\t}\n});\n", "import { <PERSON>o } from \"hono\";\nimport { Env } from \"../types\";\nimport { AuthManager } from \"../auth\";\nimport { GeminiApiClient } from \"../gemini-client\";\n\n/**\n * Debug and testing routes for troubleshooting authentication and API functionality.\n */\nexport const DebugRoute = new Hono<{ Bindings: Env }>();\n\n// Check KV cache status\nDebugRoute.get(\"/cache\", async (c) => {\n\ttry {\n\t\tconst authManager = new AuthManager(c.env);\n\t\tconst cacheInfo = await authManager.getCachedTokenInfo();\n\n\t\t// Remove sensitive information from the response\n\t\tconst sanitizedInfo = {\n\t\t\tstatus: \"ok\",\n\t\t\tcached: cacheInfo.cached,\n\t\t\tcached_at: cacheInfo.cached_at,\n\t\t\texpires_at: cacheInfo.expires_at,\n\t\t\ttime_until_expiry_seconds: cacheInfo.time_until_expiry_seconds,\n\t\t\tis_expired: cacheInfo.is_expired,\n\t\t\tmessage: cacheInfo.message\n\t\t\t// Explicitly exclude token_preview and any other sensitive data\n\t\t};\n\n\t\treturn c.json(sanitizedInfo);\n\t} catch (e: unknown) {\n\t\tconst errorMessage = e instanceof Error ? e.message : String(e);\n\t\treturn c.json(\n\t\t\t{\n\t\t\t\tstatus: \"error\",\n\t\t\t\tmessage: errorMessage\n\t\t\t},\n\t\t\t500\n\t\t);\n\t}\n});\n\n// Simple token test endpoint\nDebugRoute.post(\"/token-test\", async (c) => {\n\ttry {\n\t\tconsole.log(\"Token test endpoint called\");\n\t\tconst authManager = new AuthManager(c.env);\n\n\t\t// Test authentication only\n\t\tawait authManager.initializeAuth();\n\t\tconsole.log(\"Token test passed\");\n\n\t\treturn c.json({\n\t\t\tstatus: \"ok\",\n\t\t\tmessage: \"Token authentication successful\"\n\t\t});\n\t} catch (e: unknown) {\n\t\tconst errorMessage = e instanceof Error ? e.message : String(e);\n\t\tconsole.error(\"Token test error:\", e);\n\t\treturn c.json(\n\t\t\t{\n\t\t\t\tstatus: \"error\",\n\t\t\t\tmessage: errorMessage\n\t\t\t\t// Removed stack trace for security\n\t\t\t},\n\t\t\t500\n\t\t);\n\t}\n});\n\n// Full functionality test endpoint\nDebugRoute.post(\"/test\", async (c) => {\n\ttry {\n\t\tconsole.log(\"Test endpoint called\");\n\t\tconst authManager = new AuthManager(c.env);\n\t\tconst geminiClient = new GeminiApiClient(c.env, authManager);\n\n\t\t// Test authentication\n\t\tawait authManager.initializeAuth();\n\t\tconsole.log(\"Auth test passed\");\n\n\t\t// Test project discovery\n\t\tconst projectId = await geminiClient.discoverProjectId();\n\t\tconsole.log(\"Project discovery test passed\");\n\n\t\treturn c.json({\n\t\t\tstatus: \"ok\",\n\t\t\tmessage: \"Authentication and project discovery successful\",\n\t\t\tproject_available: !!projectId\n\t\t\t// Removed actual projectId for security\n\t\t});\n\t} catch (e: unknown) {\n\t\tconst errorMessage = e instanceof Error ? e.message : String(e);\n\t\tconsole.error(\"Test endpoint error:\", e);\n\t\treturn c.json(\n\t\t\t{\n\t\t\t\tstatus: \"error\",\n\t\t\t\tmessage: errorMessage\n\t\t\t\t// Removed stack trace and detailed error message for security\n\t\t\t},\n\t\t\t500\n\t\t);\n\t}\n});\n", "import { Middleware<PERSON><PERSON><PERSON> } from \"hono\";\nimport { Env } from \"../types\";\n\n/**\n * Middleware to enforce OpenAI-style API key authentication if OPENAI_API_KEY is set in the environment.\n * Checks for 'Authorization: Bearer <key>' header on protected routes.\n */\nexport const openAIApiKeyAuth: MiddlewareHandler<{ Bindings: Env }> = async (c, next) => {\n\t// Skip authentication for public endpoints\n\tconst publicEndpoints = [\"/\", \"/health\"];\n\tif (publicEndpoints.some((endpoint) => c.req.path === endpoint)) {\n\t\tawait next();\n\t\treturn;\n\t}\n\n\t// If OPENAI_API_KEY is set in environment, require authentication\n\tif (c.env.OPENAI_API_KEY) {\n\t\tconst authHeader = c.req.header(\"Authorization\");\n\n\t\tif (!authHeader) {\n\t\t\treturn c.json(\n\t\t\t\t{\n\t\t\t\t\terror: {\n\t\t\t\t\t\tmessage: \"Missing Authorization header\",\n\t\t\t\t\t\ttype: \"authentication_error\",\n\t\t\t\t\t\tcode: \"missing_authorization\"\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t401\n\t\t\t);\n\t\t}\n\n\t\t// Check for Bearer token format\n\t\tconst match = authHeader.match(/^<PERSON><PERSON>\\s+(.+)$/);\n\t\tif (!match) {\n\t\t\treturn c.json(\n\t\t\t\t{\n\t\t\t\t\terror: {\n\t\t\t\t\t\tmessage: \"Invalid Authorization header format. Expected: Bearer <token>\",\n\t\t\t\t\t\ttype: \"authentication_error\",\n\t\t\t\t\t\tcode: \"invalid_authorization_format\"\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t401\n\t\t\t);\n\t\t}\n\n\t\tconst providedKey = match[1];\n\t\tif (providedKey !== c.env.OPENAI_API_KEY) {\n\t\t\treturn c.json(\n\t\t\t\t{\n\t\t\t\t\terror: {\n\t\t\t\t\t\tmessage: \"Invalid API key\",\n\t\t\t\t\t\ttype: \"authentication_error\",\n\t\t\t\t\t\tcode: \"invalid_api_key\"\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t401\n\t\t\t);\n\t\t}\n\n\t\t// Optionally log successful authentication\n\t\t// console.log('API key authentication successful');\n\t}\n\n\tawait next();\n};\n", "import { Context, Next } from \"hono\";\nimport { Env } from \"../types\";\n\n/**\n * Logging middleware for request/response tracking\n *\n * Logs:\n * - Request start with method, path, and body (for POST/PUT/PATCH)\n * - Request completion with status code and duration\n * - Masks sensitive data in request bodies\n */\nexport const loggingMiddleware = async (c: Context<{ Bindings: Env }>, next: Next) => {\n\tconst method = c.req.method;\n\tconst path = c.req.path;\n\tconst startTime = Date.now();\n\tconst timestamp = new Date().toISOString();\n\n\t// Log request body for POST/PUT/PATCH requests\n\tlet bodyLog = \"\";\n\tif ([\"POST\", \"PUT\", \"PATCH\"].includes(method)) {\n\t\ttry {\n\t\t\t// Clone the request to read the body without consuming it\n\t\t\tconst clonedReq = c.req.raw.clone();\n\t\t\tconst body = await clonedReq.text();\n\n\t\t\t// Truncate very long bodies and mask sensitive data\n\t\t\tconst truncatedBody = body.length > 500 ? body.substring(0, 500) + \"...\" : body;\n\t\t\t// Mask potential API keys or tokens\n\t\t\tconst maskedBody = truncatedBody.replace(/\"(api_?key|token|authorization)\":\\s*\"[^\"]*\"/gi, '\"$1\": \"***\"');\n\t\t\tbodyLog = ` - Body: ${maskedBody}`;\n\t\t} catch {\n\t\t\tbodyLog = \" - Body: [unable to parse]\";\n\t\t}\n\t}\n\n\tconsole.log(`[${timestamp}] ${method} ${path}${bodyLog} - Request started`);\n\n\tawait next();\n\n\tconst duration = Date.now() - startTime;\n\tconst status = c.res.status;\n\tconst endTimestamp = new Date().toISOString();\n\n\tconsole.log(`[${endTimestamp}] ${method} ${path} - Completed with status ${status} (${duration}ms)`);\n};\n", "import { <PERSON>o } from \"hono\";\nimport { Env } from \"./types\";\nimport { OpenAIRoute } from \"./routes/openai\";\nimport { DebugRoute } from \"./routes/debug\";\nimport { openAIApiKeyAuth } from \"./middlewares/auth\";\nimport { loggingMiddleware } from \"./middlewares/logging\";\n\n/**\n * Gemini CLI OpenAI Worker\n *\n * A Cloudflare Worker that provides OpenAI-compatible API endpoints\n * for Google's Gemini models via the Gemini CLI OAuth flow.\n *\n * Features:\n * - OpenAI-compatible chat completions and model listing\n * - OAuth2 authentication with token caching via Cloudflare KV\n * - Support for multiple Gemini models (2.5 Pro, 2.0 Flash, 1.5 Pro, etc.)\n * - Streaming responses compatible with OpenAI SDK\n * - Debug and testing endpoints for troubleshooting\n */\n\n// Create the main Hono app\nconst app = new Hono<{ Bindings: Env }>();\n\n// Add logging middleware\napp.use(\"*\", loggingMiddleware);\n\n// Add CORS headers for all requests\napp.use(\"*\", async (c, next) => {\n\t// Set CORS headers\n\tc.header(\"Access-Control-Allow-Origin\", \"*\");\n\tc.header(\"Access-Control-Allow-Methods\", \"GET, POST, OPTIONS\");\n\tc.header(\"Access-Control-Allow-Headers\", \"Content-Type, Authorization\");\n\n\t// Handle preflight requests\n\tif (c.req.method === \"OPTIONS\") {\n\t\tc.status(204);\n\t\treturn c.body(null);\n\t}\n\n\tawait next();\n});\n\n// Apply OpenAI API key authentication middleware to all /v1 routes\napp.use(\"/v1/*\", openAIApiKeyAuth);\n\n// Setup route handlers\napp.route(\"/v1\", OpenAIRoute);\napp.route(\"/v1/debug\", DebugRoute);\n\n// Add individual debug routes to main app for backward compatibility\napp.route(\"/v1\", DebugRoute);\n\n// Root endpoint - basic info about the service\napp.get(\"/\", (c) => {\n\tconst requiresAuth = !!c.env.OPENAI_API_KEY;\n\n\treturn c.json({\n\t\tname: \"Gemini CLI OpenAI Worker\",\n\t\tdescription: \"OpenAI-compatible API for Google Gemini models via OAuth\",\n\t\tversion: \"1.0.0\",\n\t\tauthentication: {\n\t\t\trequired: requiresAuth,\n\t\t\ttype: requiresAuth ? \"Bearer token in Authorization header\" : \"None\"\n\t\t},\n\t\tendpoints: {\n\t\t\tchat_completions: \"/v1/chat/completions\",\n\t\t\tmodels: \"/v1/models\",\n\t\t\tdebug: {\n\t\t\t\tcache: \"/v1/debug/cache\",\n\t\t\t\ttoken_test: \"/v1/token-test\",\n\t\t\t\tfull_test: \"/v1/test\"\n\t\t\t}\n\t\t},\n\t\tdocumentation: \"https://github.com/gewoonjaap/gemini-cli-openai\"\n\t});\n});\n\n// Health check endpoint\napp.get(\"/health\", (c) => {\n\treturn c.json({ status: \"ok\", timestamp: new Date().toISOString() });\n});\n\nexport default app;\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTHER_EXPORTS from \"I:\\\\gemini\\\\gemini-cli-openai\\\\src\\\\index.ts\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"I:\\\\gemini\\\\gemini-cli-openai\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"I:\\\\gemini\\\\gemini-cli-openai\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"I:\\\\gemini\\\\gemini-cli-openai\\\\src\\\\index.ts\";\n\t\t\t\tconst MIDDLEWARE_TEST_INJECT = \"__INJECT_FOR_TESTING_WRANGLER_MIDDLEWARE__\";\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"I:\\\\gemini\\\\gemini-cli-openai\\\\.wrangler\\\\tmp\\\\bundle-jKQFiY\\\\middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"I:\\\\gemini\\\\gemini-cli-openai\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\common.ts\";\nimport type { WorkerEntrypointConstructor } from \"I:\\\\gemini\\\\gemini-cli-openai\\\\.wrangler\\\\tmp\\\\bundle-jKQFiY\\\\middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"I:\\\\gemini\\\\gemini-cli-openai\\\\.wrangler\\\\tmp\\\\bundle-jKQFiY\\\\middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;;AAuBO,SAAS,0BAA0B,MAAM;AAC/C,SAAO,IAAI,MAAM,WAAW,IAAI,0BAA0B;AAC3D;AAFgB;AAAA;AAIT,SAAS,eAAe,MAAM;AACpC,QAAM,KAAK,6BAAM;AAChB,UAAM,0CAA0B,IAAI;AAAA,EACrC,GAFW;AAGX,SAAO,OAAO,OAAO,IAAI,EAAE,WAAW,KAAK,CAAC;AAC7C;AALgB;;AAcT,SAAS,oBAAoB,MAAM;AACzC,SAAO,MAAM;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AACb,YAAM,IAAI,MAAM,WAAW,IAAI,0BAA0B;AAAA,IAC1D;AAAA,EACD;AACD;AAPgB;;;ACxChB,IAAM,cAAc,WAAW,aAAa,cAAc,KAAK,IAAI;AACnE,IAAM,kBAAkB,WAAW,aAAa,MAAM,WAAW,YAAY,IAAI,KAAK,WAAW,WAAW,IAAI,MAAM,KAAK,IAAI,IAAI;AACnI,IAAM,aAAa;AAAA,EAClB,MAAM;AAAA,EACN,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,EACR,SAAS;AACR,WAAO;AAAA,EACR;AACD;AACO,IAAM,mBAAN,MAAuB;AAAA,EAzB9B,OAyB8B;AAAA;AAAA;AAAA,EAC7B,YAAY;AAAA,EACZ;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA,YAAY,MAAM,SAAS;AAC1B,SAAK,OAAO;AACZ,SAAK,YAAY,SAAS,aAAa,gBAAgB;AACvD,SAAK,SAAS,SAAS;AAAA,EACxB;AAAA,EACA,IAAI,WAAW;AACd,WAAO,gBAAgB,IAAI,KAAK;AAAA,EACjC;AAAA,EACA,SAAS;AACR,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,WAAW,KAAK;AAAA,MAChB,WAAW,KAAK;AAAA,MAChB,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,IACd;AAAA,EACD;AACD;AACO,IAAM,kBAAkB,MAAMA,yBAAwB,iBAAiB;AAAA,EAjD9E,OAiD8E;AAAA;AAAA;AAAA,EAC7E,YAAY;AAAA,EACZ,cAAc;AACb,UAAM,GAAG,SAAS;AAAA,EACnB;AAAA,EACA,IAAI,WAAW;AACd,WAAO;AAAA,EACR;AACD;AACO,IAAM,qBAAN,cAAiC,iBAAiB;AAAA,EA1DzD,OA0DyD;AAAA;AAAA;AAAA,EACxD,YAAY;AACb;AACO,IAAM,4BAAN,cAAwC,iBAAiB;AAAA,EA7DhE,OA6DgE;AAAA;AAAA;AAAA,EAC/D,YAAY;AAAA,EACZ,eAAe,CAAC;AAAA,EAChB,aAAa;AAAA,EACb,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,OAAO;AAAA,EACP,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,wBAAwB;AAAA,EACxB,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,cAAc;AAAA,EACd,iBAAiB;AAClB;AACO,IAAM,+BAAN,MAAmC;AAAA,EArF1C,OAqF0C;AAAA;AAAA;AAAA,EACzC,YAAY;AAAA,EACZ,aAAa;AACZ,WAAO,CAAC;AAAA,EACT;AAAA,EACA,iBAAiB,OAAO,OAAO;AAC9B,WAAO,CAAC;AAAA,EACT;AAAA,EACA,iBAAiB,MAAM;AACtB,WAAO,CAAC;AAAA,EACT;AACD;AACO,IAAM,cAAN,MAAkB;AAAA,EAjGzB,OAiGyB;AAAA;AAAA;AAAA,EACxB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc,oBAAI,IAAI;AAAA,EACtB,WAAW,CAAC;AAAA,EACZ,4BAA4B;AAAA,EAC5B,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS,KAAK,UAAU;AACvB,UAAM,0BAA0B,sBAAsB;AAAA,EACvD;AAAA,EACA,IAAI,aAAa;AAChB,WAAO;AAAA,EACR;AAAA,EACA,uBAAuB;AACtB,WAAO,CAAC;AAAA,EACT;AAAA,EACA,qBAAqB;AACpB,WAAO,IAAI,0BAA0B,EAAE;AAAA,EACxC;AAAA,EACA,6BAA6B;AAAA,EAC7B,MAAM;AACL,QAAI,KAAK,eAAe,aAAa;AACpC,aAAO,gBAAgB;AAAA,IACxB;AACA,WAAO,KAAK,IAAI,IAAI,KAAK;AAAA,EAC1B;AAAA,EACA,WAAW,UAAU;AACpB,SAAK,WAAW,WAAW,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,SAAS,QAAQ,IAAI,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,cAAc,MAAM;AAAA,EACjI;AAAA,EACA,cAAc,aAAa;AAC1B,SAAK,WAAW,cAAc,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,SAAS,WAAW,IAAI,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,cAAc,SAAS;AAAA,EAC1I;AAAA,EACA,uBAAuB;AACtB,SAAK,WAAW,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,cAAc,cAAc,EAAE,cAAc,YAAY;AAAA,EACvG;AAAA,EACA,aAAa;AACZ,WAAO,KAAK;AAAA,EACb;AAAA,EACA,iBAAiB,MAAM,MAAM;AAC5B,WAAO,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,SAAS,SAAS,CAAC,QAAQ,EAAE,cAAc,KAAK;AAAA,EACtF;AAAA,EACA,iBAAiB,MAAM;AACtB,WAAO,KAAK,SAAS,OAAO,CAAC,MAAM,EAAE,cAAc,IAAI;AAAA,EACxD;AAAA,EACA,KAAK,MAAM,SAAS;AACnB,UAAM,QAAQ,IAAI,gBAAgB,MAAM,OAAO;AAC/C,SAAK,SAAS,KAAK,KAAK;AACxB,WAAO;AAAA,EACR;AAAA,EACA,QAAQ,aAAa,uBAAuB,SAAS;AACpD,QAAI;AACJ,QAAI;AACJ,QAAI,OAAO,0BAA0B,UAAU;AAC9C,cAAQ,KAAK,iBAAiB,uBAAuB,MAAM,EAAE,CAAC,GAAG;AACjE,YAAM,KAAK,iBAAiB,SAAS,MAAM,EAAE,CAAC,GAAG;AAAA,IAClD,OAAO;AACN,cAAQ,OAAO,WAAW,uBAAuB,KAAK,KAAK,KAAK,IAAI;AACpE,YAAM,OAAO,WAAW,uBAAuB,GAAG,KAAK,KAAK,IAAI;AAAA,IACjE;AACA,UAAM,QAAQ,IAAI,mBAAmB,aAAa;AAAA,MACjD,WAAW;AAAA,MACX,QAAQ;AAAA,QACP;AAAA,QACA;AAAA,MACD;AAAA,IACD,CAAC;AACD,SAAK,SAAS,KAAK,KAAK;AACxB,WAAO;AAAA,EACR;AAAA,EACA,4BAA4B,SAAS;AACpC,SAAK,4BAA4B;AAAA,EAClC;AAAA,EACA,iBAAiB,MAAM,UAAU,SAAS;AACzC,UAAM,0BAA0B,8BAA8B;AAAA,EAC/D;AAAA,EACA,oBAAoB,MAAM,UAAU,SAAS;AAC5C,UAAM,0BAA0B,iCAAiC;AAAA,EAClE;AAAA,EACA,cAAc,OAAO;AACpB,UAAM,0BAA0B,2BAA2B;AAAA,EAC5D;AAAA,EACA,SAAS;AACR,WAAO;AAAA,EACR;AACD;AACO,IAAM,sBAAN,MAA0B;AAAA,EAvLjC,OAuLiC;AAAA;AAAA;AAAA,EAChC,YAAY;AAAA,EACZ,OAAO,sBAAsB,CAAC;AAAA,EAC9B,YAAY;AAAA,EACZ,YAAY,UAAU;AACrB,SAAK,YAAY;AAAA,EAClB;AAAA,EACA,cAAc;AACb,WAAO,CAAC;AAAA,EACT;AAAA,EACA,aAAa;AACZ,UAAM,0BAA0B,gCAAgC;AAAA,EACjE;AAAA,EACA,QAAQ,SAAS;AAChB,UAAM,0BAA0B,6BAA6B;AAAA,EAC9D;AAAA,EACA,KAAK,IAAI;AACR,WAAO;AAAA,EACR;AAAA,EACA,gBAAgB,IAAI,YAAY,MAAM;AACrC,WAAO,GAAG,KAAK,SAAS,GAAG,IAAI;AAAA,EAChC;AAAA,EACA,UAAU;AACT,WAAO;AAAA,EACR;AAAA,EACA,iBAAiB;AAChB,WAAO;AAAA,EACR;AAAA,EACA,cAAc;AACb,WAAO;AAAA,EACR;AACD;AACO,IAAM,cAAc,WAAW,eAAe,sBAAsB,WAAW,cAAc,WAAW,cAAc,IAAI,YAAY;;;AC7M7I,WAAW,cAAc;AACzB,WAAW,cAAc;AACzB,WAAW,mBAAmB;AAC9B,WAAW,kBAAkB;AAC7B,WAAW,qBAAqB;AAChC,WAAW,sBAAsB;AACjC,WAAW,+BAA+B;AAC1C,WAAW,4BAA4B;;;ACjBvC,SAAS,gBAAgB;;;ACAzB,IAAO,eAAQ,OAAO,OAAO,MAAM;AAAC,GAAG,EAAE,WAAW,KAAK,CAAC;;;ADG1D,IAAM,WAAW,WAAW;AACrB,IAAM,gBAAgB;AACtB,IAAM,UAAU,IAAI,SAAS;AAC7B,IAAM,UAAU,IAAI,SAAS;AAC7B,IAAM,MAAM,UAAU,OAAO;AAC7B,IAAM,OAAO,UAAU,QAAQ;AAC/B,IAAM,QAAQ,UAAU,SAAS;AACjC,IAAM,QAAQ,UAAU,SAAS;AACjC,IAAM,QAAQ,UAAU,SAAS;AACjC,IAAM,QAAQ,UAAU,SAAS;AACjC,IAAM,OAAO,UAAU,QAAQ;AAC/B,IAAM,aAAa,UAAU,cAA8B,+BAAe,oBAAoB;AAE9F,IAAM,QAAQ,UAAU,SAAS;AACjC,IAAM,QAAQ,UAAU,SAAS;AACjC,IAAM,aAAa,UAAU,cAAc;AAC3C,IAAM,MAAM,UAAU,OAAO;AAC7B,IAAM,SAAS,UAAU,UAAU;AACnC,IAAM,QAAQ,UAAU,SAAS;AACjC,IAAM,WAAW,UAAU,YAAY;AACvC,IAAM,iBAAiB,UAAU,kBAAkB;AACnD,IAAM,UAAU,UAAU,WAAW;AACrC,IAAM,aAAa,UAAU,cAAc;AAC3C,IAAM,OAAO,UAAU,QAAQ;AAC/B,IAAM,UAAU,UAAU,WAAW;AACrC,IAAM,UAAU,UAAU,WAAW;AACrC,IAAM,YAAY,UAAU,aAAa;AACzC,IAAM,UAAU,UAAU,WAA2B,oCAAoB,iBAAiB;AAC1F,IAAM,SAAyB,oBAAI,IAAI;AAIvC,IAAM,sBAAsB;AAC5B,IAAM,sBAAsB;;;AElBnC,IAAM,iBAAiB,WAAW,SAAS;AACpC,IAAM;AAAA,EACX;AAAA,EACA,OAAAC;AAAA;AAAA,EAEA;AAAA,EACA,OAAAC;AAAA,EACA,YAAAC;AAAA;AAAA,EAEA,YAAAC;AAAA,EACA,OAAAC;AAAA,EACA,KAAAC;AAAA,EACA,QAAAC;AAAA,EACA,OAAAC;AAAA,EACA,OAAAC;AAAA,EACA,gBAAAC;AAAA,EACA,UAAAC;AAAA,EACA,MAAAC;AAAA,EACA,KAAAC;AAAA,EACA,SAAAC;AAAA,EACA,YAAAC;AAAA,EACA,OAAAC;AAAA,EACA,MAAAC;AAAA,EACA,SAAAC;AAAA,EACA,SAAAC;AAAA,EACA,WAAAC;AAAA,EACA,OAAAC;AAAA,EACA,MAAAC;AACF,IAAI;AACJ,OAAO,OAAO,gBAAgB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAO,kBAAQ;;;ACvDf,WAAW,UAAU;;;ACDd,IAAM,SAAyB,uBAAO,OAAO,gCAASC,QAAO,WAAW;AAC9E,QAAM,MAAM,KAAK,IAAI;AACrB,QAAM,UAAU,KAAK,MAAM,MAAM,GAAG;AACpC,QAAM,QAAQ,MAAM,MAAM;AAC1B,MAAI,WAAW;AACd,QAAI,cAAc,UAAU,UAAU,CAAC;AACvC,QAAI,YAAY,QAAQ,UAAU,CAAC;AACnC,QAAI,YAAY,GAAG;AAClB,oBAAc,cAAc;AAC5B,kBAAY,MAAM;AAAA,IACnB;AACA,WAAO,CAAC,aAAa,SAAS;AAAA,EAC/B;AACA,SAAO,CAAC,SAAS,KAAK;AACvB,GAdoD,WAcjD,EAAE,QAAQ,gCAAS,SAAS;AAC9B,SAAO,OAAO,KAAK,IAAI,IAAI,GAAG;AAC/B,GAFa,UAEX,CAAC;;;AChBH,SAAS,oBAAoB;;;ACAtB,IAAM,cAAN,MAAkB;AAAA,EAAzB,OAAyB;AAAA;AAAA;AAAA,EACxB;AAAA,EACA,UAAU;AAAA,EACV,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY,IAAI;AACf,SAAK,KAAK;AAAA,EACX;AAAA,EACA,UAAUC,MAAK,UAAU;AACxB,gBAAY,SAAS;AACrB,WAAO;AAAA,EACR;AAAA,EACA,gBAAgB,UAAU;AACzB,gBAAY,SAAS;AACrB,WAAO;AAAA,EACR;AAAA,EACA,SAAS,GAAG,GAAG,UAAU;AACxB,gBAAY,OAAO,aAAa,cAAc,SAAS;AACvD,WAAO;AAAA,EACR;AAAA,EACA,WAAW,IAAI,IAAI,UAAU;AAC5B,gBAAY,SAAS;AACrB,WAAO;AAAA,EACR;AAAA,EACA,cAAcC,MAAK;AAClB,WAAO;AAAA,EACR;AAAA,EACA,UAAUC,QAAOD,MAAK;AACrB,WAAO;AAAA,EACR;AAAA,EACA,gBAAgB;AACf,WAAO,CAAC,KAAK,SAAS,KAAK,IAAI;AAAA,EAChC;AAAA,EACA,MAAM,KAAK,UAAU,IAAI;AACxB,QAAI,eAAe,YAAY;AAC9B,YAAM,IAAI,YAAY,EAAE,OAAO,GAAG;AAAA,IACnC;AACA,QAAI;AACH,cAAQ,IAAI,GAAG;AAAA,IAChB,QAAQ;AAAA,IAAC;AACT,UAAM,OAAO,OAAO,cAAc,GAAG;AACrC,WAAO;AAAA,EACR;AACD;;;AC3CO,IAAM,aAAN,MAAiB;AAAA,EAAxB,OAAwB;AAAA;AAAA;AAAA,EACvB;AAAA,EACA,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,YAAY,IAAI;AACf,SAAK,KAAK;AAAA,EACX;AAAA,EACA,WAAW,MAAM;AAChB,SAAK,QAAQ;AACb,WAAO;AAAA,EACR;AACD;;;ACXO,IAAM,eAAe;;;AHIrB,IAAM,UAAN,MAAM,iBAAgB,aAAa;AAAA,EAJ1C,OAI0C;AAAA;AAAA;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,MAAM;AACjB,UAAM;AACN,SAAK,MAAM,KAAK;AAChB,SAAK,SAAS,KAAK;AACnB,SAAK,WAAW,KAAK;AACrB,eAAW,QAAQ,CAAC,GAAG,OAAO,oBAAoB,SAAQ,SAAS,GAAG,GAAG,OAAO,oBAAoB,aAAa,SAAS,CAAC,GAAG;AAC7H,YAAM,QAAQ,KAAK,IAAI;AACvB,UAAI,OAAO,UAAU,YAAY;AAChC,aAAK,IAAI,IAAI,MAAM,KAAK,IAAI;AAAA,MAC7B;AAAA,IACD;AAAA,EACD;AAAA,EACA,YAAY,SAAS,MAAM,MAAM;AAChC,YAAQ,KAAK,GAAG,OAAO,IAAI,IAAI,OAAO,EAAE,GAAG,OAAO,GAAG,IAAI,OAAO,EAAE,GAAG,OAAO,EAAE;AAAA,EAC/E;AAAA,EACA,QAAQ,MAAM;AACb,WAAO,MAAM,KAAK,GAAG,IAAI;AAAA,EAC1B;AAAA,EACA,UAAU,WAAW;AACpB,WAAO,MAAM,UAAU,SAAS;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,QAAQ;AACX,WAAO,KAAK,WAAW,IAAI,WAAW,CAAC;AAAA,EACxC;AAAA,EACA,IAAI,SAAS;AACZ,WAAO,KAAK,YAAY,IAAI,YAAY,CAAC;AAAA,EAC1C;AAAA,EACA,IAAI,SAAS;AACZ,WAAO,KAAK,YAAY,IAAI,YAAY,CAAC;AAAA,EAC1C;AAAA,EACA,OAAO;AAAA,EACP,MAAME,MAAK;AACV,SAAK,OAAOA;AAAA,EACb;AAAA,EACA,MAAM;AACL,WAAO,KAAK;AAAA,EACb;AAAA,EACA,OAAO;AAAA,EACP,WAAW;AAAA,EACX,OAAO,CAAC;AAAA,EACR,QAAQ;AAAA,EACR,WAAW,CAAC;AAAA,EACZ,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,IAAI,UAAU;AACb,WAAO,IAAI,YAAY;AAAA,EACxB;AAAA,EACA,IAAI,WAAW;AACd,WAAO,EAAE,MAAM,aAAa;AAAA,EAC7B;AAAA,EACA,IAAI,8BAA8B;AACjC,WAAO,oBAAI,IAAI;AAAA,EAChB;AAAA,EACA,IAAI,oBAAoB;AACvB,WAAO;AAAA,EACR;AAAA,EACA,IAAI,YAAY;AACf,WAAO;AAAA,EACR;AAAA,EACA,IAAI,mBAAmB;AACtB,WAAO;AAAA,EACR;AAAA,EACA,IAAI,mBAAmB;AACtB,WAAO;AAAA,EACR;AAAA,EACA,IAAI,WAAW;AACd,WAAO,CAAC;AAAA,EACT;AAAA,EACA,IAAI,UAAU;AACb,WAAO,CAAC;AAAA,EACT;AAAA,EACA,IAAI,YAAY;AACf,WAAO;AAAA,EACR;AAAA,EACA,IAAI,SAAS;AACZ,WAAO,CAAC;AAAA,EACT;AAAA,EACA,IAAI,iBAAiB;AACpB,WAAO,CAAC;AAAA,EACT;AAAA,EACA,oBAAoB;AACnB,WAAO;AAAA,EACR;AAAA,EACA,kBAAkB;AACjB,WAAO;AAAA,EACR;AAAA,EACA,SAAS;AACR,WAAO;AAAA,EACR;AAAA,EACA,gBAAgB;AACf,WAAO,CAAC;AAAA,EACT;AAAA,EACA,MAAM;AAAA,EAAC;AAAA,EACP,QAAQ;AAAA,EAAC;AAAA,EACT,QAAQ;AACP,UAAM,0BAA0B,eAAe;AAAA,EAChD;AAAA,EACA,mBAAmB;AAClB,WAAO;AAAA,EACR;AAAA,EACA,yBAAyB;AACxB,UAAM,0BAA0B,gCAAgC;AAAA,EACjE;AAAA,EACA,OAAO;AACN,UAAM,0BAA0B,cAAc;AAAA,EAC/C;AAAA,EACA,aAAa;AACZ,UAAM,0BAA0B,oBAAoB;AAAA,EACrD;AAAA,EACA,OAAO;AACN,UAAM,0BAA0B,cAAc;AAAA,EAC/C;AAAA,EACA,QAAQ;AACP,UAAM,0BAA0B,eAAe;AAAA,EAChD;AAAA,EACA,SAAS;AACR,UAAM,0BAA0B,gBAAgB;AAAA,EACjD;AAAA,EACA,uBAAuB;AACtB,UAAM,0BAA0B,8BAA8B;AAAA,EAC/D;AAAA,EACA,cAAc;AACb,UAAM,0BAA0B,qBAAqB;AAAA,EACtD;AAAA,EACA,aAAa;AACZ,UAAM,0BAA0B,oBAAoB;AAAA,EACrD;AAAA,EACA,WAAW;AACV,UAAM,0BAA0B,kBAAkB;AAAA,EACnD;AAAA,EACA,sCAAsC;AACrC,UAAM,0BAA0B,6CAA6C;AAAA,EAC9E;AAAA,EACA,sCAAsC;AACrC,UAAM,0BAA0B,6CAA6C;AAAA,EAC9E;AAAA,EACA,aAAa;AACZ,UAAM,0BAA0B,oBAAoB;AAAA,EACrD;AAAA,EACA,YAAY;AACX,UAAM,0BAA0B,mBAAmB;AAAA,EACpD;AAAA,EACA,SAAS;AACR,UAAM,0BAA0B,gBAAgB;AAAA,EACjD;AAAA,EACA,UAAU;AACT,UAAM,0BAA0B,iBAAiB;AAAA,EAClD;AAAA,EACA,aAAa,EAAE,KAAqB,+BAAe,wBAAwB,EAAE;AAAA,EAC7E,SAAS;AAAA,IACR,WAAW;AAAA,IACX,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,2BAA2B;AAAA,IAC3B,WAA2B,+BAAe,0BAA0B;AAAA,IACpE,aAA6B,+BAAe,4BAA4B;AAAA,EACzE;AAAA,EACA,eAAe;AAAA,IACd,UAA0B,+BAAe,+BAA+B;AAAA,IACxE,YAA4B,+BAAe,iCAAiC;AAAA,IAC5E,oBAAoC,+BAAe,yCAAyC;AAAA,EAC7F;AAAA,EACA,cAAc,OAAO,OAAO,OAAO;AAAA,IAClC,cAAc;AAAA,IACd,KAAK;AAAA,IACL,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,EACX,IAAI,EAAE,KAAK,6BAAM,GAAN,OAAQ,CAAC;AAAA,EACpB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,eAAe;AAAA,EACf,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,QAAQ;AAAA,EACR,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,iBAAiB;AAClB;;;AI3NA,IAAM,gBAAgB,WAAW,SAAS;AACnC,IAAM,mBAAmB,cAAc;AACvC,IAAM,EAAE,MAAM,UAAU,SAAS,IAAI;AAAA,EAC1C;AACF;AACA,IAAM,eAAe,IAAI,QAAa;AAAA,EACpC,KAAK,cAAc;AAAA,EACnB;AAAA,EACA;AACF,CAAC;AACM,IAAM;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI;AACJ,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAAD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAO,kBAAQ;;;AClOf,WAAW,UAAU;;;ACArB,IAAI,UAAU,wBAAC,YAAY,SAAS,eAAe;AACjD,SAAO,CAACC,UAAS,SAAS;AACxB,QAAI,QAAQ;AACZ,WAAO,SAAS,CAAC;AACjB,mBAAe,SAAS,GAAG;AACzB,UAAI,KAAK,OAAO;AACd,cAAM,IAAI,MAAM,8BAA8B;AAAA,MAChD;AACA,cAAQ;AACR,UAAI;AACJ,UAAI,UAAU;AACd,UAAI;AACJ,UAAI,WAAW,CAAC,GAAG;AACjB,kBAAU,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;AAC5B,QAAAA,SAAQ,IAAI,aAAa;AAAA,MAC3B,OAAO;AACL,kBAAU,MAAM,WAAW,UAAU,QAAQ;AAAA,MAC/C;AACA,UAAI,SAAS;AACX,YAAI;AACF,gBAAM,MAAM,QAAQA,UAAS,MAAM,SAAS,IAAI,CAAC,CAAC;AAAA,QACpD,SAAS,KAAK;AACZ,cAAI,eAAe,SAAS,SAAS;AACnC,YAAAA,SAAQ,QAAQ;AAChB,kBAAM,MAAM,QAAQ,KAAKA,QAAO;AAChC,sBAAU;AAAA,UACZ,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAIA,SAAQ,cAAc,SAAS,YAAY;AAC7C,gBAAM,MAAM,WAAWA,QAAO;AAAA,QAChC;AAAA,MACF;AACA,UAAI,QAAQA,SAAQ,cAAc,SAAS,UAAU;AACnD,QAAAA,SAAQ,MAAM;AAAA,MAChB;AACA,aAAOA;AAAA,IACT;AAnCe;AAAA,EAoCjB;AACF,GAzCc;;;ACAd,IAAI,mBAAmB,OAAO;;;ACC9B,IAAI,YAAY,8BAAO,SAAS,UAA0B,uBAAO,OAAO,IAAI,MAAM;AAChF,QAAM,EAAE,MAAM,OAAO,MAAM,MAAM,IAAI;AACrC,QAAM,UAAU,mBAAmB,cAAc,QAAQ,IAAI,UAAU,QAAQ;AAC/E,QAAM,cAAc,QAAQ,IAAI,cAAc;AAC9C,MAAI,aAAa,WAAW,qBAAqB,KAAK,aAAa,WAAW,mCAAmC,GAAG;AAClH,WAAO,cAAc,SAAS,EAAE,KAAK,IAAI,CAAC;AAAA,EAC5C;AACA,SAAO,CAAC;AACV,GARgB;AAShB,eAAe,cAAc,SAAS,SAAS;AAC7C,QAAM,WAAW,MAAM,QAAQ,SAAS;AACxC,MAAI,UAAU;AACZ,WAAO,0BAA0B,UAAU,OAAO;AAAA,EACpD;AACA,SAAO,CAAC;AACV;AANe;AAOf,SAAS,0BAA0B,UAAU,SAAS;AACpD,QAAM,OAAuB,uBAAO,OAAO,IAAI;AAC/C,WAAS,QAAQ,CAAC,OAAO,QAAQ;AAC/B,UAAM,uBAAuB,QAAQ,OAAO,IAAI,SAAS,IAAI;AAC7D,QAAI,CAAC,sBAAsB;AACzB,WAAK,GAAG,IAAI;AAAA,IACd,OAAO;AACL,6BAAuB,MAAM,KAAK,KAAK;AAAA,IACzC;AAAA,EACF,CAAC;AACD,MAAI,QAAQ,KAAK;AACf,WAAO,QAAQ,IAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC7C,YAAM,uBAAuB,IAAI,SAAS,GAAG;AAC7C,UAAI,sBAAsB;AACxB,kCAA0B,MAAM,KAAK,KAAK;AAC1C,eAAO,KAAK,GAAG;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AApBS;AAqBT,IAAI,yBAAyB,wBAAC,MAAM,KAAK,UAAU;AACjD,MAAI,KAAK,GAAG,MAAM,QAAQ;AACxB,QAAI,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG;AAC5B;AACA,WAAK,GAAG,EAAE,KAAK,KAAK;AAAA,IACtB,OAAO;AACL,WAAK,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,KAAK;AAAA,IAC/B;AAAA,EACF,OAAO;AACL,QAAI,CAAC,IAAI,SAAS,IAAI,GAAG;AACvB,WAAK,GAAG,IAAI;AAAA,IACd,OAAO;AACL,WAAK,GAAG,IAAI,CAAC,KAAK;AAAA,IACpB;AAAA,EACF;AACF,GAf6B;AAgB7B,IAAI,4BAA4B,wBAAC,MAAM,KAAK,UAAU;AACpD,MAAI,aAAa;AACjB,QAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,OAAK,QAAQ,CAAC,MAAM,UAAU;AAC5B,QAAI,UAAU,KAAK,SAAS,GAAG;AAC7B,iBAAW,IAAI,IAAI;AAAA,IACrB,OAAO;AACL,UAAI,CAAC,WAAW,IAAI,KAAK,OAAO,WAAW,IAAI,MAAM,YAAY,MAAM,QAAQ,WAAW,IAAI,CAAC,KAAK,WAAW,IAAI,aAAa,MAAM;AACpI,mBAAW,IAAI,IAAoB,uBAAO,OAAO,IAAI;AAAA,MACvD;AACA,mBAAa,WAAW,IAAI;AAAA,IAC9B;AAAA,EACF,CAAC;AACH,GAbgC;;;ACtDhC,IAAI,YAAY,wBAAC,SAAS;AACxB,QAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,MAAI,MAAM,CAAC,MAAM,IAAI;AACnB,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT,GANgB;AAOhB,IAAI,mBAAmB,wBAAC,cAAc;AACpC,QAAM,EAAE,QAAQ,KAAK,IAAI,sBAAsB,SAAS;AACxD,QAAM,QAAQ,UAAU,IAAI;AAC5B,SAAO,kBAAkB,OAAO,MAAM;AACxC,GAJuB;AAKvB,IAAI,wBAAwB,wBAAC,SAAS;AACpC,QAAM,SAAS,CAAC;AAChB,SAAO,KAAK,QAAQ,cAAc,CAAC,OAAO,UAAU;AAClD,UAAM,OAAO,IAAI,KAAK;AACtB,WAAO,KAAK,CAAC,MAAM,KAAK,CAAC;AACzB,WAAO;AAAA,EACT,CAAC;AACD,SAAO,EAAE,QAAQ,KAAK;AACxB,GAR4B;AAS5B,IAAI,oBAAoB,wBAAC,OAAO,WAAW;AACzC,WAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACvB,aAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,UAAI,MAAM,CAAC,EAAE,SAAS,IAAI,GAAG;AAC3B,cAAM,CAAC,IAAI,MAAM,CAAC,EAAE,QAAQ,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC;AAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT,GAXwB;AAYxB,IAAI,eAAe,CAAC;AACpB,IAAI,aAAa,wBAAC,OAAO,SAAS;AAChC,MAAI,UAAU,KAAK;AACjB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,MAAM,MAAM,6BAA6B;AACvD,MAAI,OAAO;AACT,UAAM,WAAW,GAAG,KAAK,IAAI,IAAI;AACjC,QAAI,CAAC,aAAa,QAAQ,GAAG;AAC3B,UAAI,MAAM,CAAC,GAAG;AACZ,qBAAa,QAAQ,IAAI,QAAQ,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,MAAM,CAAC,UAAU,MAAM,CAAC,GAAG,IAAI,OAAO,IAAI,MAAM,CAAC,CAAC,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG,IAAI,OAAO,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAAA,MACpL,OAAO;AACL,qBAAa,QAAQ,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG,IAAI;AAAA,MACjD;AAAA,IACF;AACA,WAAO,aAAa,QAAQ;AAAA,EAC9B;AACA,SAAO;AACT,GAjBiB;AAkBjB,IAAI,YAAY,wBAAC,KAAK,YAAY;AAChC,MAAI;AACF,WAAO,QAAQ,GAAG;AAAA,EACpB,QAAQ;AACN,WAAO,IAAI,QAAQ,yBAAyB,CAAC,UAAU;AACrD,UAAI;AACF,eAAO,QAAQ,KAAK;AAAA,MACtB,QAAQ;AACN,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AACF,GAZgB;AAahB,IAAI,eAAe,wBAAC,QAAQ,UAAU,KAAK,SAAS,GAAjC;AACnB,IAAI,UAAU,wBAAC,YAAY;AACzB,QAAM,MAAM,QAAQ;AACpB,QAAM,QAAQ,IAAI;AAAA,IAChB;AAAA,IACA,IAAI,WAAW,CAAC,MAAM,KAAK,KAAK;AAAA,EAClC;AACA,MAAI,IAAI;AACR,SAAO,IAAI,IAAI,QAAQ,KAAK;AAC1B,UAAM,WAAW,IAAI,WAAW,CAAC;AACjC,QAAI,aAAa,IAAI;AACnB,YAAM,aAAa,IAAI,QAAQ,KAAK,CAAC;AACrC,YAAM,OAAO,IAAI,MAAM,OAAO,eAAe,KAAK,SAAS,UAAU;AACrE,aAAO,aAAa,KAAK,SAAS,KAAK,IAAI,KAAK,QAAQ,QAAQ,OAAO,IAAI,IAAI;AAAA,IACjF,WAAW,aAAa,IAAI;AAC1B;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,MAAM,OAAO,CAAC;AAC3B,GAlBc;AAuBd,IAAI,kBAAkB,wBAAC,YAAY;AACjC,QAAM,SAAS,QAAQ,OAAO;AAC9B,SAAO,OAAO,SAAS,KAAK,OAAO,GAAG,EAAE,MAAM,MAAM,OAAO,MAAM,GAAG,EAAE,IAAI;AAC5E,GAHsB;AAItB,IAAI,YAAY,wBAAC,MAAM,QAAQ,SAAS;AACtC,MAAI,KAAK,QAAQ;AACf,UAAM,UAAU,KAAK,GAAG,IAAI;AAAA,EAC9B;AACA,SAAO,GAAG,OAAO,CAAC,MAAM,MAAM,KAAK,GAAG,GAAG,IAAI,GAAG,QAAQ,MAAM,KAAK,GAAG,MAAM,GAAG,EAAE,MAAM,MAAM,KAAK,GAAG,GAAG,MAAM,CAAC,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI,GAAG,EAAE;AACjJ,GALgB;AAMhB,IAAI,yBAAyB,wBAAC,SAAS;AACrC,MAAI,KAAK,WAAW,KAAK,SAAS,CAAC,MAAM,MAAM,CAAC,KAAK,SAAS,GAAG,GAAG;AAClE,WAAO;AAAA,EACT;AACA,QAAM,WAAW,KAAK,MAAM,GAAG;AAC/B,QAAM,UAAU,CAAC;AACjB,MAAI,WAAW;AACf,WAAS,QAAQ,CAAC,YAAY;AAC5B,QAAI,YAAY,MAAM,CAAC,KAAK,KAAK,OAAO,GAAG;AACzC,kBAAY,MAAM;AAAA,IACpB,WAAW,KAAK,KAAK,OAAO,GAAG;AAC7B,UAAI,KAAK,KAAK,OAAO,GAAG;AACtB,YAAI,QAAQ,WAAW,KAAK,aAAa,IAAI;AAC3C,kBAAQ,KAAK,GAAG;AAAA,QAClB,OAAO;AACL,kBAAQ,KAAK,QAAQ;AAAA,QACvB;AACA,cAAM,kBAAkB,QAAQ,QAAQ,KAAK,EAAE;AAC/C,oBAAY,MAAM;AAClB,gBAAQ,KAAK,QAAQ;AAAA,MACvB,OAAO;AACL,oBAAY,MAAM;AAAA,MACpB;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO,QAAQ,OAAO,CAAC,GAAG,GAAG,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;AACvD,GA1B6B;AA2B7B,IAAI,aAAa,wBAAC,UAAU;AAC1B,MAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,GAAG,MAAM,IAAI;AAC7B,YAAQ,MAAM,QAAQ,OAAO,GAAG;AAAA,EAClC;AACA,SAAO,MAAM,QAAQ,GAAG,MAAM,KAAK,UAAU,OAAO,mBAAmB,IAAI;AAC7E,GARiB;AASjB,IAAI,iBAAiB,wBAAC,KAAK,KAAK,aAAa;AAC3C,MAAI;AACJ,MAAI,CAAC,YAAY,OAAO,CAAC,OAAO,KAAK,GAAG,GAAG;AACzC,QAAI,YAAY,IAAI,QAAQ,IAAI,GAAG,IAAI,CAAC;AACxC,QAAI,cAAc,IAAI;AACpB,kBAAY,IAAI,QAAQ,IAAI,GAAG,IAAI,CAAC;AAAA,IACtC;AACA,WAAO,cAAc,IAAI;AACvB,YAAM,kBAAkB,IAAI,WAAW,YAAY,IAAI,SAAS,CAAC;AACjE,UAAI,oBAAoB,IAAI;AAC1B,cAAM,aAAa,YAAY,IAAI,SAAS;AAC5C,cAAM,WAAW,IAAI,QAAQ,KAAK,UAAU;AAC5C,eAAO,WAAW,IAAI,MAAM,YAAY,aAAa,KAAK,SAAS,QAAQ,CAAC;AAAA,MAC9E,WAAW,mBAAmB,MAAM,MAAM,eAAe,GAAG;AAC1D,eAAO;AAAA,MACT;AACA,kBAAY,IAAI,QAAQ,IAAI,GAAG,IAAI,YAAY,CAAC;AAAA,IAClD;AACA,cAAU,OAAO,KAAK,GAAG;AACzB,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,UAAU,CAAC;AACjB,cAAY,OAAO,KAAK,GAAG;AAC3B,MAAI,WAAW,IAAI,QAAQ,KAAK,CAAC;AACjC,SAAO,aAAa,IAAI;AACtB,UAAM,eAAe,IAAI,QAAQ,KAAK,WAAW,CAAC;AAClD,QAAI,aAAa,IAAI,QAAQ,KAAK,QAAQ;AAC1C,QAAI,aAAa,gBAAgB,iBAAiB,IAAI;AACpD,mBAAa;AAAA,IACf;AACA,QAAI,OAAO,IAAI;AAAA,MACb,WAAW;AAAA,MACX,eAAe,KAAK,iBAAiB,KAAK,SAAS,eAAe;AAAA,IACpE;AACA,QAAI,SAAS;AACX,aAAO,WAAW,IAAI;AAAA,IACxB;AACA,eAAW;AACX,QAAI,SAAS,IAAI;AACf;AAAA,IACF;AACA,QAAI;AACJ,QAAI,eAAe,IAAI;AACrB,cAAQ;AAAA,IACV,OAAO;AACL,cAAQ,IAAI,MAAM,aAAa,GAAG,iBAAiB,KAAK,SAAS,YAAY;AAC7E,UAAI,SAAS;AACX,gBAAQ,WAAW,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,UAAU;AACZ,UAAI,EAAE,QAAQ,IAAI,KAAK,MAAM,QAAQ,QAAQ,IAAI,CAAC,IAAI;AACpD,gBAAQ,IAAI,IAAI,CAAC;AAAA,MACnB;AACA;AACA,cAAQ,IAAI,EAAE,KAAK,KAAK;AAAA,IAC1B,OAAO;AACL,cAAQ,IAAI,MAAM;AAAA,IACpB;AAAA,EACF;AACA,SAAO,MAAM,QAAQ,GAAG,IAAI;AAC9B,GA/DqB;AAgErB,IAAI,gBAAgB;AACpB,IAAI,iBAAiB,wBAAC,KAAK,QAAQ;AACjC,SAAO,eAAe,KAAK,KAAK,IAAI;AACtC,GAFqB;AAGrB,IAAI,sBAAsB;;;ACxM1B,IAAI,wBAAwB,wBAAC,QAAQ,UAAU,KAAK,mBAAmB,GAA3C;AAC5B,IAAI,cAAc,MAAM;AAAA,EALxB,OAKwB;AAAA;AAAA;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb;AAAA,EACA,YAAY,CAAC;AAAA,EACb,YAAY,SAAS,OAAO,KAAK,cAAc,CAAC,CAAC,CAAC,GAAG;AACnD,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,iBAAiB,CAAC;AAAA,EACzB;AAAA,EACA,MAAM,KAAK;AACT,WAAO,MAAM,KAAK,iBAAiB,GAAG,IAAI,KAAK,qBAAqB;AAAA,EACtE;AAAA,EACA,iBAAiB,KAAK;AACpB,UAAM,WAAW,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,EAAE,GAAG;AAC7D,UAAM,QAAQ,KAAK,eAAe,QAAQ;AAC1C,WAAO,QAAQ,KAAK,KAAK,KAAK,IAAI,sBAAsB,KAAK,IAAI,QAAQ;AAAA,EAC3E;AAAA,EACA,uBAAuB;AACrB,UAAM,UAAU,CAAC;AACjB,UAAM,OAAO,OAAO,KAAK,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,CAAC;AACjE,eAAW,OAAO,MAAM;AACtB,YAAM,QAAQ,KAAK,eAAe,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,EAAE,GAAG,CAAC;AAC/E,UAAI,SAAS,OAAO,UAAU,UAAU;AACtC,gBAAQ,GAAG,IAAI,KAAK,KAAK,KAAK,IAAI,sBAAsB,KAAK,IAAI;AAAA,MACnE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe,UAAU;AACvB,WAAO,KAAK,aAAa,CAAC,IAAI,KAAK,aAAa,CAAC,EAAE,QAAQ,IAAI;AAAA,EACjE;AAAA,EACA,MAAM,KAAK;AACT,WAAO,cAAc,KAAK,KAAK,GAAG;AAAA,EACpC;AAAA,EACA,QAAQ,KAAK;AACX,WAAO,eAAe,KAAK,KAAK,GAAG;AAAA,EACrC;AAAA,EACA,OAAO,MAAM;AACX,QAAI,MAAM;AACR,aAAO,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK;AAAA,IACvC;AACA,UAAM,aAAa,CAAC;AACpB,SAAK,IAAI,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACvC,iBAAW,GAAG,IAAI;AAAA,IACpB,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,MAAM,UAAU,SAAS;AACvB,WAAO,KAAK,UAAU,eAAe,MAAM,UAAU,MAAM,OAAO;AAAA,EACpE;AAAA,EACA,cAAc,wBAAC,QAAQ;AACrB,UAAM,EAAE,WAAW,KAAAC,KAAI,IAAI;AAC3B,UAAM,aAAa,UAAU,GAAG;AAChC,QAAI,YAAY;AACd,aAAO;AAAA,IACT;AACA,UAAM,eAAe,OAAO,KAAK,SAAS,EAAE,CAAC;AAC7C,QAAI,cAAc;AAChB,aAAO,UAAU,YAAY,EAAE,KAAK,CAAC,SAAS;AAC5C,YAAI,iBAAiB,QAAQ;AAC3B,iBAAO,KAAK,UAAU,IAAI;AAAA,QAC5B;AACA,eAAO,IAAI,SAAS,IAAI,EAAE,GAAG,EAAE;AAAA,MACjC,CAAC;AAAA,IACH;AACA,WAAO,UAAU,GAAG,IAAIA,KAAI,GAAG,EAAE;AAAA,EACnC,GAhBc;AAAA,EAiBd,OAAO;AACL,WAAO,KAAK,YAAY,MAAM,EAAE,KAAK,CAAC,SAAS,KAAK,MAAM,IAAI,CAAC;AAAA,EACjE;AAAA,EACA,OAAO;AACL,WAAO,KAAK,YAAY,MAAM;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,YAAY,aAAa;AAAA,EACvC;AAAA,EACA,OAAO;AACL,WAAO,KAAK,YAAY,MAAM;AAAA,EAChC;AAAA,EACA,WAAW;AACT,WAAO,KAAK,YAAY,UAAU;AAAA,EACpC;AAAA,EACA,iBAAiB,QAAQ,MAAM;AAC7B,SAAK,eAAe,MAAM,IAAI;AAAA,EAChC;AAAA,EACA,MAAM,QAAQ;AACZ,WAAO,KAAK,eAAe,MAAM;AAAA,EACnC;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,KAAK,gBAAgB,IAAI;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,KAAK;AAAA,EACxD;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,KAAK,EAAE,KAAK,UAAU,EAAE;AAAA,EAC3E;AACF;;;AC/GA,IAAI,2BAA2B;AAAA,EAC7B,WAAW;AAAA,EACX,cAAc;AAAA,EACd,QAAQ;AACV;AACA,IAAI,MAAM,wBAAC,OAAO,cAAc;AAC9B,QAAM,gBAAgB,IAAI,OAAO,KAAK;AACtC,gBAAc,YAAY;AAC1B,gBAAc,YAAY;AAC1B,SAAO;AACT,GALU;AAgFV,IAAI,kBAAkB,8BAAO,KAAK,OAAO,mBAAmBC,UAAS,WAAW;AAC9E,MAAI,OAAO,QAAQ,YAAY,EAAE,eAAe,SAAS;AACvD,QAAI,EAAE,eAAe,UAAU;AAC7B,YAAM,IAAI,SAAS;AAAA,IACrB;AACA,QAAI,eAAe,SAAS;AAC1B,YAAM,MAAM;AAAA,IACd;AAAA,EACF;AACA,QAAM,YAAY,IAAI;AACtB,MAAI,CAAC,WAAW,QAAQ;AACtB,WAAO,QAAQ,QAAQ,GAAG;AAAA,EAC5B;AACA,MAAI,QAAQ;AACV,WAAO,CAAC,KAAK;AAAA,EACf,OAAO;AACL,aAAS,CAAC,GAAG;AAAA,EACf;AACA,QAAM,SAAS,QAAQ,IAAI,UAAU,IAAI,CAAC,MAAM,EAAE,EAAE,OAAO,QAAQ,SAAAA,SAAQ,CAAC,CAAC,CAAC,EAAE;AAAA,IAC9E,CAAC,QAAQ,QAAQ;AAAA,MACf,IAAI,OAAO,OAAO,EAAE,IAAI,CAAC,SAAS,gBAAgB,MAAM,OAAO,OAAOA,UAAS,MAAM,CAAC;AAAA,IACxF,EAAE,KAAK,MAAM,OAAO,CAAC,CAAC;AAAA,EACxB;AACA,MAAI,mBAAmB;AACrB,WAAO,IAAI,MAAM,QAAQ,SAAS;AAAA,EACpC,OAAO;AACL,WAAO;AAAA,EACT;AACF,GA5BsB;;;ACnFtB,IAAI,aAAa;AACjB,IAAI,wBAAwB,wBAAC,aAAa,YAAY;AACpD,SAAO;AAAA,IACL,gBAAgB;AAAA,IAChB,GAAG;AAAA,EACL;AACF,GAL4B;AAM5B,IAAI,UAAU,MAAM;AAAA,EAVpB,OAUoB;AAAA;AAAA;AAAA,EAClB;AAAA,EACA;AAAA,EACA,MAAM,CAAC;AAAA,EACP;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,KAAK,SAAS;AACxB,SAAK,cAAc;AACnB,QAAI,SAAS;AACX,WAAK,gBAAgB,QAAQ;AAC7B,WAAK,MAAM,QAAQ;AACnB,WAAK,mBAAmB,QAAQ;AAChC,WAAK,QAAQ,QAAQ;AACrB,WAAK,eAAe,QAAQ;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,IAAI,MAAM;AACR,SAAK,SAAS,IAAI,YAAY,KAAK,aAAa,KAAK,OAAO,KAAK,YAAY;AAC7E,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ;AACV,QAAI,KAAK,iBAAiB,iBAAiB,KAAK,eAAe;AAC7D,aAAO,KAAK;AAAA,IACd,OAAO;AACL,YAAM,MAAM,gCAAgC;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,KAAK,eAAe;AACtB,aAAO,KAAK;AAAA,IACd,OAAO;AACL,YAAM,MAAM,sCAAsC;AAAA,IACpD;AAAA,EACF;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,SAAS,IAAI,SAAS,MAAM;AAAA,MACtC,SAAS,KAAK,qBAAqB,IAAI,QAAQ;AAAA,IACjD,CAAC;AAAA,EACH;AAAA,EACA,IAAI,IAAI,MAAM;AACZ,QAAI,KAAK,QAAQ,MAAM;AACrB,aAAO,IAAI,SAAS,KAAK,MAAM,IAAI;AACnC,iBAAW,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,QAAQ,QAAQ,GAAG;AAChD,YAAI,MAAM,gBAAgB;AACxB;AAAA,QACF;AACA,YAAI,MAAM,cAAc;AACtB,gBAAM,UAAU,KAAK,KAAK,QAAQ,aAAa;AAC/C,eAAK,QAAQ,OAAO,YAAY;AAChC,qBAAW,UAAU,SAAS;AAC5B,iBAAK,QAAQ,OAAO,cAAc,MAAM;AAAA,UAC1C;AAAA,QACF,OAAO;AACL,eAAK,QAAQ,IAAI,GAAG,CAAC;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AACA,SAAK,OAAO;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,SAAS,2BAAI,SAAS;AACpB,SAAK,cAAc,CAAC,YAAY,KAAK,KAAK,OAAO;AACjD,WAAO,KAAK,UAAU,GAAG,IAAI;AAAA,EAC/B,GAHS;AAAA,EAIT,YAAY,wBAAC,WAAW,KAAK,UAAU,QAA3B;AAAA,EACZ,YAAY,6BAAM,KAAK,SAAX;AAAA,EACZ,cAAc,wBAAC,aAAa;AAC1B,SAAK,YAAY;AAAA,EACnB,GAFc;AAAA,EAGd,SAAS,wBAAC,MAAM,OAAO,YAAY;AACjC,QAAI,KAAK,WAAW;AAClB,WAAK,OAAO,IAAI,SAAS,KAAK,KAAK,MAAM,KAAK,IAAI;AAAA,IACpD;AACA,UAAM,UAAU,KAAK,OAAO,KAAK,KAAK,UAAU,KAAK,qBAAqB,IAAI,QAAQ;AACtF,QAAI,UAAU,QAAQ;AACpB,cAAQ,OAAO,IAAI;AAAA,IACrB,WAAW,SAAS,QAAQ;AAC1B,cAAQ,OAAO,MAAM,KAAK;AAAA,IAC5B,OAAO;AACL,cAAQ,IAAI,MAAM,KAAK;AAAA,IACzB;AAAA,EACF,GAZS;AAAA,EAaT,SAAS,wBAAC,WAAW;AACnB,SAAK,UAAU;AAAA,EACjB,GAFS;AAAA,EAGT,MAAM,wBAAC,KAAK,UAAU;AACpB,SAAK,SAAyB,oBAAI,IAAI;AACtC,SAAK,KAAK,IAAI,KAAK,KAAK;AAAA,EAC1B,GAHM;AAAA,EAIN,MAAM,wBAAC,QAAQ;AACb,WAAO,KAAK,OAAO,KAAK,KAAK,IAAI,GAAG,IAAI;AAAA,EAC1C,GAFM;AAAA,EAGN,IAAI,MAAM;AACR,QAAI,CAAC,KAAK,MAAM;AACd,aAAO,CAAC;AAAA,IACV;AACA,WAAO,OAAO,YAAY,KAAK,IAAI;AAAA,EACrC;AAAA,EACA,aAAa,MAAM,KAAK,SAAS;AAC/B,UAAM,kBAAkB,KAAK,OAAO,IAAI,QAAQ,KAAK,KAAK,OAAO,IAAI,KAAK,oBAAoB,IAAI,QAAQ;AAC1G,QAAI,OAAO,QAAQ,YAAY,aAAa,KAAK;AAC/C,YAAM,aAAa,IAAI,mBAAmB,UAAU,IAAI,UAAU,IAAI,QAAQ,IAAI,OAAO;AACzF,iBAAW,CAAC,KAAK,KAAK,KAAK,YAAY;AACrC,YAAI,IAAI,YAAY,MAAM,cAAc;AACtC,0BAAgB,OAAO,KAAK,KAAK;AAAA,QACnC,OAAO;AACL,0BAAgB,IAAI,KAAK,KAAK;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS;AACX,iBAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,OAAO,GAAG;AAC5C,YAAI,OAAO,MAAM,UAAU;AACzB,0BAAgB,IAAI,GAAG,CAAC;AAAA,QAC1B,OAAO;AACL,0BAAgB,OAAO,CAAC;AACxB,qBAAW,MAAM,GAAG;AAClB,4BAAgB,OAAO,GAAG,EAAE;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,OAAO,QAAQ,WAAW,MAAM,KAAK,UAAU,KAAK;AACnE,WAAO,IAAI,SAAS,MAAM,EAAE,QAAQ,SAAS,gBAAgB,CAAC;AAAA,EAChE;AAAA,EACA,cAAc,2BAAI,SAAS,KAAK,aAAa,GAAG,IAAI,GAAtC;AAAA,EACd,OAAO,wBAAC,MAAM,KAAK,YAAY,KAAK,aAAa,MAAM,KAAK,OAAO,GAA5D;AAAA,EACP,OAAO,wBAAC,MAAM,KAAK,YAAY;AAC7B,WAAO,CAAC,KAAK,oBAAoB,CAAC,KAAK,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,YAAY,IAAI,SAAS,IAAI,IAAI,KAAK;AAAA,MAChH;AAAA,MACA;AAAA,MACA,sBAAsB,YAAY,OAAO;AAAA,IAC3C;AAAA,EACF,GANO;AAAA,EAOP,OAAO,wBAAC,QAAQ,KAAK,YAAY;AAC/B,WAAO,KAAK;AAAA,MACV,KAAK,UAAU,MAAM;AAAA,MACrB;AAAA,MACA,sBAAsB,oBAAoB,OAAO;AAAA,IACnD;AAAA,EACF,GANO;AAAA,EAOP,OAAO,wBAAC,MAAM,KAAK,YAAY;AAC7B,UAAM,MAAM,wBAAC,UAAU,KAAK,aAAa,OAAO,KAAK,sBAAsB,4BAA4B,OAAO,CAAC,GAAnG;AACZ,WAAO,OAAO,SAAS,WAAW,gBAAgB,MAAM,yBAAyB,WAAW,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,IAAI,IAAI;AAAA,EAC7H,GAHO;AAAA,EAIP,WAAW,wBAAC,UAAU,WAAW;AAC/B,SAAK,OAAO,YAAY,OAAO,QAAQ,CAAC;AACxC,WAAO,KAAK,YAAY,MAAM,UAAU,GAAG;AAAA,EAC7C,GAHW;AAAA,EAIX,WAAW,6BAAM;AACf,SAAK,qBAAqB,MAAM,IAAI,SAAS;AAC7C,WAAO,KAAK,iBAAiB,IAAI;AAAA,EACnC,GAHW;AAIb;;;AC5KA,IAAI,kBAAkB;AACtB,IAAI,4BAA4B;AAChC,IAAI,UAAU,CAAC,OAAO,QAAQ,OAAO,UAAU,WAAW,OAAO;AACjE,IAAI,mCAAmC;AACvC,IAAI,uBAAuB,cAAc,MAAM;AAAA,EAL/C,OAK+C;AAAA;AAAA;AAC/C;;;ACLA,IAAI,mBAAmB;;;ACKvB,IAAI,kBAAkB,wBAAC,MAAM;AAC3B,SAAO,EAAE,KAAK,iBAAiB,GAAG;AACpC,GAFsB;AAGtB,IAAI,eAAe,wBAAC,KAAK,MAAM;AAC7B,MAAI,iBAAiB,KAAK;AACxB,UAAM,MAAM,IAAI,YAAY;AAC5B,WAAO,EAAE,YAAY,IAAI,MAAM,GAAG;AAAA,EACpC;AACA,UAAQ,MAAM,GAAG;AACjB,SAAO,EAAE,KAAK,yBAAyB,GAAG;AAC5C,GAPmB;AAQnB,IAAI,OAAO,MAAM;AAAA,EAjBjB,OAiBiB;AAAA;AAAA;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,SAAS,CAAC;AAAA,EACV,YAAY,UAAU,CAAC,GAAG;AACxB,UAAM,aAAa,CAAC,GAAG,SAAS,yBAAyB;AACzD,eAAW,QAAQ,CAAC,WAAW;AAC7B,WAAK,MAAM,IAAI,CAAC,UAAU,SAAS;AACjC,YAAI,OAAO,UAAU,UAAU;AAC7B,eAAK,QAAQ;AAAA,QACf,OAAO;AACL,eAAK,UAAU,QAAQ,KAAK,OAAO,KAAK;AAAA,QAC1C;AACA,aAAK,QAAQ,CAAC,YAAY;AACxB,eAAK,UAAU,QAAQ,KAAK,OAAO,OAAO;AAAA,QAC5C,CAAC;AACD,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,SAAK,KAAK,CAAC,QAAQ,SAAS,aAAa;AACvC,iBAAW,KAAK,CAAC,IAAI,EAAE,KAAK,GAAG;AAC7B,aAAK,QAAQ;AACb,mBAAW,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG;AAC/B,mBAAS,IAAI,CAAC,YAAY;AACxB,iBAAK,UAAU,EAAE,YAAY,GAAG,KAAK,OAAO,OAAO;AAAA,UACrD,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,SAAK,MAAM,CAAC,SAAS,aAAa;AAChC,UAAI,OAAO,SAAS,UAAU;AAC5B,aAAK,QAAQ;AAAA,MACf,OAAO;AACL,aAAK,QAAQ;AACb,iBAAS,QAAQ,IAAI;AAAA,MACvB;AACA,eAAS,QAAQ,CAAC,YAAY;AAC5B,aAAK,UAAU,iBAAiB,KAAK,OAAO,OAAO;AAAA,MACrD,CAAC;AACD,aAAO;AAAA,IACT;AACA,UAAM,EAAE,QAAQ,GAAG,qBAAqB,IAAI;AAC5C,WAAO,OAAO,MAAM,oBAAoB;AACxC,SAAK,UAAU,UAAU,OAAO,QAAQ,WAAW,UAAU;AAAA,EAC/D;AAAA,EACA,SAAS;AACP,UAAM,QAAQ,IAAI,KAAK;AAAA,MACrB,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,UAAM,eAAe,KAAK;AAC1B,UAAM,mBAAmB,KAAK;AAC9B,UAAM,SAAS,KAAK;AACpB,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,MAAM,MAAMC,MAAK;AACf,UAAM,SAAS,KAAK,SAAS,IAAI;AACjC,IAAAA,KAAI,OAAO,IAAI,CAAC,MAAM;AACpB,UAAI;AACJ,UAAIA,KAAI,iBAAiB,cAAc;AACrC,kBAAU,EAAE;AAAA,MACd,OAAO;AACL,kBAAU,8BAAO,GAAG,UAAU,MAAM,QAAQ,CAAC,GAAGA,KAAI,YAAY,EAAE,GAAG,MAAM,EAAE,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAtF;AACV,gBAAQ,gBAAgB,IAAI,EAAE;AAAA,MAChC;AACA,aAAO,UAAU,EAAE,QAAQ,EAAE,MAAM,OAAO;AAAA,IAC5C,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,SAAS,MAAM;AACb,UAAM,SAAS,KAAK,OAAO;AAC3B,WAAO,YAAY,UAAU,KAAK,WAAW,IAAI;AACjD,WAAO;AAAA,EACT;AAAA,EACA,UAAU,wBAAC,YAAY;AACrB,SAAK,eAAe;AACpB,WAAO;AAAA,EACT,GAHU;AAAA,EAIV,WAAW,wBAAC,YAAY;AACtB,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT,GAHW;AAAA,EAIX,MAAM,MAAM,oBAAoB,SAAS;AACvC,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS;AACX,UAAI,OAAO,YAAY,YAAY;AACjC,wBAAgB;AAAA,MAClB,OAAO;AACL,wBAAgB,QAAQ;AACxB,YAAI,QAAQ,mBAAmB,OAAO;AACpC,2BAAiB,wBAAC,YAAY,SAAb;AAAA,QACnB,OAAO;AACL,2BAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,UAAM,aAAa,gBAAgB,CAAC,MAAM;AACxC,YAAM,WAAW,cAAc,CAAC;AAChC,aAAO,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AAAA,IACvD,IAAI,CAAC,MAAM;AACT,UAAI,mBAAmB;AACvB,UAAI;AACF,2BAAmB,EAAE;AAAA,MACvB,QAAQ;AAAA,MACR;AACA,aAAO,CAAC,EAAE,KAAK,gBAAgB;AAAA,IACjC;AACA,wBAAoB,MAAM;AACxB,YAAM,aAAa,UAAU,KAAK,WAAW,IAAI;AACjD,YAAM,mBAAmB,eAAe,MAAM,IAAI,WAAW;AAC7D,aAAO,CAAC,YAAY;AAClB,cAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,YAAI,WAAW,IAAI,SAAS,MAAM,gBAAgB,KAAK;AACvD,eAAO,IAAI,QAAQ,KAAK,OAAO;AAAA,MACjC;AAAA,IACF,GAAG;AACH,UAAM,UAAU,8BAAO,GAAG,SAAS;AACjC,YAAM,MAAM,MAAM,mBAAmB,eAAe,EAAE,IAAI,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC;AAChF,UAAI,KAAK;AACP,eAAO;AAAA,MACT;AACA,YAAM,KAAK;AAAA,IACb,GANgB;AAOhB,SAAK,UAAU,iBAAiB,UAAU,MAAM,GAAG,GAAG,OAAO;AAC7D,WAAO;AAAA,EACT;AAAA,EACA,UAAU,QAAQ,MAAM,SAAS;AAC/B,aAAS,OAAO,YAAY;AAC5B,WAAO,UAAU,KAAK,WAAW,IAAI;AACrC,UAAM,IAAI,EAAE,UAAU,KAAK,WAAW,MAAM,QAAQ,QAAQ;AAC5D,SAAK,OAAO,IAAI,QAAQ,MAAM,CAAC,SAAS,CAAC,CAAC;AAC1C,SAAK,OAAO,KAAK,CAAC;AAAA,EACpB;AAAA,EACA,aAAa,KAAK,GAAG;AACnB,QAAI,eAAe,OAAO;AACxB,aAAO,KAAK,aAAa,KAAK,CAAC;AAAA,IACjC;AACA,UAAM;AAAA,EACR;AAAA,EACA,UAAU,SAAS,cAAcC,MAAK,QAAQ;AAC5C,QAAI,WAAW,QAAQ;AACrB,cAAQ,YAAY,IAAI,SAAS,MAAM,MAAM,KAAK,UAAU,SAAS,cAAcA,MAAK,KAAK,CAAC,GAAG;AAAA,IACnG;AACA,UAAM,OAAO,KAAK,QAAQ,SAAS,EAAE,KAAAA,KAAI,CAAC;AAC1C,UAAM,cAAc,KAAK,OAAO,MAAM,QAAQ,IAAI;AAClD,UAAM,IAAI,IAAI,QAAQ,SAAS;AAAA,MAC7B;AAAA,MACA;AAAA,MACA,KAAAA;AAAA,MACA;AAAA,MACA,iBAAiB,KAAK;AAAA,IACxB,CAAC;AACD,QAAI,YAAY,CAAC,EAAE,WAAW,GAAG;AAC/B,UAAI;AACJ,UAAI;AACF,cAAM,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY;AAC3C,YAAE,MAAM,MAAM,KAAK,iBAAiB,CAAC;AAAA,QACvC,CAAC;AAAA,MACH,SAAS,KAAK;AACZ,eAAO,KAAK,aAAa,KAAK,CAAC;AAAA,MACjC;AACA,aAAO,eAAe,UAAU,IAAI;AAAA,QAClC,CAAC,aAAa,aAAa,EAAE,YAAY,EAAE,MAAM,KAAK,iBAAiB,CAAC;AAAA,MAC1E,EAAE,MAAM,CAAC,QAAQ,KAAK,aAAa,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,iBAAiB,CAAC;AAAA,IAC9E;AACA,UAAM,WAAW,QAAQ,YAAY,CAAC,GAAG,KAAK,cAAc,KAAK,gBAAgB;AACjF,YAAQ,YAAY;AAClB,UAAI;AACF,cAAMC,WAAU,MAAM,SAAS,CAAC;AAChC,YAAI,CAACA,SAAQ,WAAW;AACtB,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AACA,eAAOA,SAAQ;AAAA,MACjB,SAAS,KAAK;AACZ,eAAO,KAAK,aAAa,KAAK,CAAC;AAAA,MACjC;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,QAAQ,wBAAC,YAAY,SAAS;AAC5B,WAAO,KAAK,UAAU,SAAS,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,MAAM;AAAA,EACjE,GAFQ;AAAA,EAGR,UAAU,wBAAC,OAAO,aAAa,KAAK,iBAAiB;AACnD,QAAI,iBAAiB,SAAS;AAC5B,aAAO,KAAK,MAAM,cAAc,IAAI,QAAQ,OAAO,WAAW,IAAI,OAAO,KAAK,YAAY;AAAA,IAC5F;AACA,YAAQ,MAAM,SAAS;AACvB,WAAO,KAAK;AAAA,MACV,IAAI;AAAA,QACF,eAAe,KAAK,KAAK,IAAI,QAAQ,mBAAmB,UAAU,KAAK,KAAK,CAAC;AAAA,QAC7E;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAbU;AAAA,EAcV,OAAO,6BAAM;AACX,qBAAiB,SAAS,CAAC,UAAU;AACnC,YAAM,YAAY,KAAK,UAAU,MAAM,SAAS,OAAO,QAAQ,MAAM,QAAQ,MAAM,CAAC;AAAA,IACtF,CAAC;AAAA,EACH,GAJO;AAKT;;;ACzOA,IAAI,oBAAoB;AACxB,IAAI,4BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,aAAa,OAAO;AACxB,IAAI,kBAAkB,IAAI,IAAI,aAAa;AAC3C,SAAS,WAAW,GAAG,GAAG;AACxB,MAAI,EAAE,WAAW,GAAG;AAClB,WAAO,EAAE,WAAW,IAAI,IAAI,IAAI,KAAK,IAAI;AAAA,EAC3C;AACA,MAAI,EAAE,WAAW,GAAG;AAClB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,6BAA6B,MAAM,2BAA2B;AACtE,WAAO;AAAA,EACT,WAAW,MAAM,6BAA6B,MAAM,2BAA2B;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,MAAM,mBAAmB;AAC3B,WAAO;AAAA,EACT,WAAW,MAAM,mBAAmB;AAClC,WAAO;AAAA,EACT;AACA,SAAO,EAAE,WAAW,EAAE,SAAS,IAAI,IAAI,KAAK,IAAI,EAAE,SAAS,EAAE;AAC/D;AAlBS;AAmBT,IAAI,OAAO,MAAM;AAAA,EAzBjB,OAyBiB;AAAA;AAAA;AAAA,EACf;AAAA,EACA;AAAA,EACA,YAA4B,uBAAO,OAAO,IAAI;AAAA,EAC9C,OAAO,QAAQ,OAAO,UAAUC,UAAS,oBAAoB;AAC3D,QAAI,OAAO,WAAW,GAAG;AACvB,UAAI,KAAK,WAAW,QAAQ;AAC1B,cAAM;AAAA,MACR;AACA,UAAI,oBAAoB;AACtB;AAAA,MACF;AACA,WAAK,SAAS;AACd;AAAA,IACF;AACA,UAAM,CAAC,OAAO,GAAG,UAAU,IAAI;AAC/B,UAAM,UAAU,UAAU,MAAM,WAAW,WAAW,IAAI,CAAC,IAAI,IAAI,yBAAyB,IAAI,CAAC,IAAI,IAAI,iBAAiB,IAAI,UAAU,OAAO,CAAC,IAAI,IAAI,yBAAyB,IAAI,MAAM,MAAM,6BAA6B;AAC9N,QAAI;AACJ,QAAI,SAAS;AACX,YAAM,OAAO,QAAQ,CAAC;AACtB,UAAI,YAAY,QAAQ,CAAC,KAAK;AAC9B,UAAI,QAAQ,QAAQ,CAAC,GAAG;AACtB,oBAAY,UAAU,QAAQ,0BAA0B,KAAK;AAC7D,YAAI,YAAY,KAAK,SAAS,GAAG;AAC/B,gBAAM;AAAA,QACR;AAAA,MACF;AACA,aAAO,KAAK,UAAU,SAAS;AAC/B,UAAI,CAAC,MAAM;AACT,YAAI,OAAO,KAAK,KAAK,SAAS,EAAE;AAAA,UAC9B,CAAC,MAAM,MAAM,6BAA6B,MAAM;AAAA,QAClD,GAAG;AACD,gBAAM;AAAA,QACR;AACA,YAAI,oBAAoB;AACtB;AAAA,QACF;AACA,eAAO,KAAK,UAAU,SAAS,IAAI,IAAI,KAAK;AAC5C,YAAI,SAAS,IAAI;AACf,eAAK,YAAYA,SAAQ;AAAA,QAC3B;AAAA,MACF;AACA,UAAI,CAAC,sBAAsB,SAAS,IAAI;AACtC,iBAAS,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC;AAAA,MACtC;AAAA,IACF,OAAO;AACL,aAAO,KAAK,UAAU,KAAK;AAC3B,UAAI,CAAC,MAAM;AACT,YAAI,OAAO,KAAK,KAAK,SAAS,EAAE;AAAA,UAC9B,CAAC,MAAM,EAAE,SAAS,KAAK,MAAM,6BAA6B,MAAM;AAAA,QAClE,GAAG;AACD,gBAAM;AAAA,QACR;AACA,YAAI,oBAAoB;AACtB;AAAA,QACF;AACA,eAAO,KAAK,UAAU,KAAK,IAAI,IAAI,KAAK;AAAA,MAC1C;AAAA,IACF;AACA,SAAK,OAAO,YAAY,OAAO,UAAUA,UAAS,kBAAkB;AAAA,EACtE;AAAA,EACA,iBAAiB;AACf,UAAM,YAAY,OAAO,KAAK,KAAK,SAAS,EAAE,KAAK,UAAU;AAC7D,UAAM,UAAU,UAAU,IAAI,CAAC,MAAM;AACnC,YAAM,IAAI,KAAK,UAAU,CAAC;AAC1B,cAAQ,OAAO,EAAE,cAAc,WAAW,IAAI,CAAC,KAAK,EAAE,SAAS,KAAK,gBAAgB,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,EAAE,eAAe;AAAA,IAChI,CAAC;AACD,QAAI,OAAO,KAAK,WAAW,UAAU;AACnC,cAAQ,QAAQ,IAAI,KAAK,MAAM,EAAE;AAAA,IACnC;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,aAAO,QAAQ,CAAC;AAAA,IAClB;AACA,WAAO,QAAQ,QAAQ,KAAK,GAAG,IAAI;AAAA,EACrC;AACF;;;ACrGA,IAAI,OAAO,MAAM;AAAA,EAFjB,OAEiB;AAAA;AAAA;AAAA,EACf,WAAW,EAAE,UAAU,EAAE;AAAA,EACzB,QAAQ,IAAI,KAAK;AAAA,EACjB,OAAO,MAAM,OAAO,oBAAoB;AACtC,UAAM,aAAa,CAAC;AACpB,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,OAAO;AAClB,UAAI,WAAW;AACf,aAAO,KAAK,QAAQ,cAAc,CAAC,MAAM;AACvC,cAAM,OAAO,MAAM,CAAC;AACpB,eAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AACpB;AACA,mBAAW;AACX,eAAO;AAAA,MACT,CAAC;AACD,UAAI,CAAC,UAAU;AACb;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,KAAK,MAAM,0BAA0B,KAAK,CAAC;AAC1D,aAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACvB,eAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAI,OAAO,CAAC,EAAE,QAAQ,IAAI,MAAM,IAAI;AAClC,iBAAO,CAAC,IAAI,OAAO,CAAC,EAAE,QAAQ,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC;AAChD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,MAAM,OAAO,QAAQ,OAAO,YAAY,KAAK,UAAU,kBAAkB;AAC9E,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,QAAI,SAAS,KAAK,MAAM,eAAe;AACvC,QAAI,WAAW,IAAI;AACjB,aAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAAA,IACtB;AACA,QAAI,eAAe;AACnB,UAAM,sBAAsB,CAAC;AAC7B,UAAM,sBAAsB,CAAC;AAC7B,aAAS,OAAO,QAAQ,yBAAyB,CAAC,GAAG,cAAc,eAAe;AAChF,UAAI,iBAAiB,QAAQ;AAC3B,4BAAoB,EAAE,YAAY,IAAI,OAAO,YAAY;AACzD,eAAO;AAAA,MACT;AACA,UAAI,eAAe,QAAQ;AACzB,4BAAoB,OAAO,UAAU,CAAC,IAAI,EAAE;AAC5C,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AACD,WAAO,CAAC,IAAI,OAAO,IAAI,MAAM,EAAE,GAAG,qBAAqB,mBAAmB;AAAA,EAC5E;AACF;;;AC9CA,IAAI,aAAa,CAAC;AAClB,IAAI,cAAc,CAAC,MAAM,CAAC,GAAmB,uBAAO,OAAO,IAAI,CAAC;AAChE,IAAI,sBAAsC,uBAAO,OAAO,IAAI;AAC5D,SAAS,oBAAoB,MAAM;AACjC,SAAO,oBAAoB,IAAI,MAAM,IAAI;AAAA,IACvC,SAAS,MAAM,KAAK,IAAI,KAAK;AAAA,MAC3B;AAAA,MACA,CAAC,GAAG,aAAa,WAAW,KAAK,QAAQ,KAAK;AAAA,IAChD,CAAC;AAAA,EACH;AACF;AAPS;AAQT,SAAS,2BAA2B;AAClC,wBAAsC,uBAAO,OAAO,IAAI;AAC1D;AAFS;AAGT,SAAS,mCAAmC,QAAQ;AAClD,QAAM,OAAO,IAAI,KAAK;AACtB,QAAM,cAAc,CAAC;AACrB,MAAI,OAAO,WAAW,GAAG;AACvB,WAAO;AAAA,EACT;AACA,QAAM,2BAA2B,OAAO;AAAA,IACtC,CAAC,UAAU,CAAC,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,GAAG,GAAG,KAAK;AAAA,EAChD,EAAE;AAAA,IACA,CAAC,CAAC,WAAW,KAAK,GAAG,CAAC,WAAW,KAAK,MAAM,YAAY,IAAI,YAAY,KAAK,MAAM,SAAS,MAAM;AAAA,EACpG;AACA,QAAM,YAA4B,uBAAO,OAAO,IAAI;AACpD,WAAS,IAAI,GAAG,IAAI,IAAI,MAAM,yBAAyB,QAAQ,IAAI,KAAK,KAAK;AAC3E,UAAM,CAAC,oBAAoB,MAAM,QAAQ,IAAI,yBAAyB,CAAC;AACvE,QAAI,oBAAoB;AACtB,gBAAU,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAmB,uBAAO,OAAO,IAAI,CAAC,CAAC,GAAG,UAAU;AAAA,IAChG,OAAO;AACL;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AACF,mBAAa,KAAK,OAAO,MAAM,GAAG,kBAAkB;AAAA,IACtD,SAAS,GAAG;AACV,YAAM,MAAM,aAAa,IAAI,qBAAqB,IAAI,IAAI;AAAA,IAC5D;AACA,QAAI,oBAAoB;AACtB;AAAA,IACF;AACA,gBAAY,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC,GAAG,UAAU,MAAM;AACjD,YAAM,gBAAgC,uBAAO,OAAO,IAAI;AACxD,oBAAc;AACd,aAAO,cAAc,GAAG,cAAc;AACpC,cAAM,CAAC,KAAK,KAAK,IAAI,WAAW,UAAU;AAC1C,sBAAc,GAAG,IAAI;AAAA,MACvB;AACA,aAAO,CAAC,GAAG,aAAa;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,QAAM,CAAC,QAAQ,qBAAqB,mBAAmB,IAAI,KAAK,YAAY;AAC5E,WAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACtD,aAAS,IAAI,GAAG,OAAO,YAAY,CAAC,EAAE,QAAQ,IAAI,MAAM,KAAK;AAC3D,YAAM,MAAM,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC;AACjC,UAAI,CAAC,KAAK;AACR;AAAA,MACF;AACA,YAAM,OAAO,OAAO,KAAK,GAAG;AAC5B,eAAS,IAAI,GAAG,OAAO,KAAK,QAAQ,IAAI,MAAM,KAAK;AACjD,YAAI,KAAK,CAAC,CAAC,IAAI,oBAAoB,IAAI,KAAK,CAAC,CAAC,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,CAAC;AACpB,aAAW,KAAK,qBAAqB;AACnC,eAAW,CAAC,IAAI,YAAY,oBAAoB,CAAC,CAAC;AAAA,EACpD;AACA,SAAO,CAAC,QAAQ,YAAY,SAAS;AACvC;AAxDS;AAyDT,SAAS,eAAe,YAAY,MAAM;AACxC,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AACA,aAAW,KAAK,OAAO,KAAK,UAAU,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG;AAC3E,QAAI,oBAAoB,CAAC,EAAE,KAAK,IAAI,GAAG;AACrC,aAAO,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AAVS;AAWT,IAAI,eAAe,MAAM;AAAA,EA3FzB,OA2FyB;AAAA;AAAA;AAAA,EACvB,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,EAAE,CAAC,eAAe,GAAmB,uBAAO,OAAO,IAAI,EAAE;AAC5E,SAAK,UAAU,EAAE,CAAC,eAAe,GAAmB,uBAAO,OAAO,IAAI,EAAE;AAAA,EAC1E;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,UAAM,aAAa,KAAK;AACxB,UAAM,SAAS,KAAK;AACpB,QAAI,CAAC,cAAc,CAAC,QAAQ;AAC1B,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AACA,QAAI,CAAC,WAAW,MAAM,GAAG;AACvB;AACA,OAAC,YAAY,MAAM,EAAE,QAAQ,CAAC,eAAe;AAC3C,mBAAW,MAAM,IAAoB,uBAAO,OAAO,IAAI;AACvD,eAAO,KAAK,WAAW,eAAe,CAAC,EAAE,QAAQ,CAAC,MAAM;AACtD,qBAAW,MAAM,EAAE,CAAC,IAAI,CAAC,GAAG,WAAW,eAAe,EAAE,CAAC,CAAC;AAAA,QAC5D,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,SAAS,MAAM;AACjB,aAAO;AAAA,IACT;AACA,UAAM,cAAc,KAAK,MAAM,MAAM,KAAK,CAAC,GAAG;AAC9C,QAAI,MAAM,KAAK,IAAI,GAAG;AACpB,YAAM,KAAK,oBAAoB,IAAI;AACnC,UAAI,WAAW,iBAAiB;AAC9B,eAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,qBAAW,CAAC,EAAE,IAAI,MAAM,eAAe,WAAW,CAAC,GAAG,IAAI,KAAK,eAAe,WAAW,eAAe,GAAG,IAAI,KAAK,CAAC;AAAA,QACvH,CAAC;AAAA,MACH,OAAO;AACL,mBAAW,MAAM,EAAE,IAAI,MAAM,eAAe,WAAW,MAAM,GAAG,IAAI,KAAK,eAAe,WAAW,eAAe,GAAG,IAAI,KAAK,CAAC;AAAA,MACjI;AACA,aAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,KAAK,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM;AACxC,eAAG,KAAK,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,UAAU,CAAC;AAAA,UAC3D,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,MAAM;AACjC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,KAAK,OAAO,CAAC,CAAC,EAAE;AAAA,YACrB,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,UAAU,CAAC;AAAA,UAC9D;AAAA,QACF;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACA,UAAM,QAAQ,uBAAuB,IAAI,KAAK,CAAC,IAAI;AACnD,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,QAAQ,MAAM,CAAC;AACrB,aAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,MAAM;AACjC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,CAAC,EAAE,KAAK,MAAM;AAAA,YACnB,GAAG,eAAe,WAAW,CAAC,GAAG,KAAK,KAAK,eAAe,WAAW,eAAe,GAAG,KAAK,KAAK,CAAC;AAAA,UACpG;AACA,iBAAO,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,aAAa,MAAM,IAAI,CAAC,CAAC;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,6BAAyB;AACzB,UAAM,WAAW,KAAK,kBAAkB;AACxC,SAAK,QAAQ,CAAC,SAAS,UAAU;AAC/B,YAAM,UAAU,SAAS,OAAO,KAAK,SAAS,eAAe;AAC7D,YAAM,cAAc,QAAQ,CAAC,EAAE,KAAK;AACpC,UAAI,aAAa;AACf,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,MAAM,MAAM,QAAQ,CAAC,CAAC;AACpC,UAAI,CAAC,OAAO;AACV,eAAO,CAAC,CAAC,GAAG,UAAU;AAAA,MACxB;AACA,YAAM,QAAQ,MAAM,QAAQ,IAAI,CAAC;AACjC,aAAO,CAAC,QAAQ,CAAC,EAAE,KAAK,GAAG,KAAK;AAAA,IAClC;AACA,WAAO,KAAK,MAAM,QAAQ,IAAI;AAAA,EAChC;AAAA,EACA,oBAAoB;AAClB,UAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,WAAO,KAAK,KAAK,OAAO,EAAE,OAAO,OAAO,KAAK,KAAK,WAAW,CAAC,EAAE,QAAQ,CAAC,WAAW;AAClF,eAAS,MAAM,MAAM,KAAK,cAAc,MAAM;AAAA,IAChD,CAAC;AACD,SAAK,cAAc,KAAK,UAAU;AAClC,WAAO;AAAA,EACT;AAAA,EACA,cAAc,QAAQ;AACpB,UAAM,SAAS,CAAC;AAChB,QAAI,cAAc,WAAW;AAC7B,KAAC,KAAK,aAAa,KAAK,OAAO,EAAE,QAAQ,CAAC,MAAM;AAC9C,YAAM,WAAW,EAAE,MAAM,IAAI,OAAO,KAAK,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC;AAC9F,UAAI,SAAS,WAAW,GAAG;AACzB,wBAAgB;AAChB,eAAO,KAAK,GAAG,QAAQ;AAAA,MACzB,WAAW,WAAW,iBAAiB;AACrC,eAAO;AAAA,UACL,GAAG,OAAO,KAAK,EAAE,eAAe,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;AAAA,QACnF;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,mCAAmC,MAAM;AAAA,IAClD;AAAA,EACF;AACF;;;ACxMA,IAAI,cAAc,MAAM;AAAA,EAFxB,OAEwB;AAAA;AAAA;AAAA,EACtB,OAAO;AAAA,EACP,WAAW,CAAC;AAAA,EACZ,UAAU,CAAC;AAAA,EACX,YAAY,MAAM;AAChB,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,QAAI,CAAC,KAAK,SAAS;AACjB,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AACA,SAAK,QAAQ,KAAK,CAAC,QAAQ,MAAM,OAAO,CAAC;AAAA,EAC3C;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,KAAK,SAAS;AACjB,YAAM,IAAI,MAAM,aAAa;AAAA,IAC/B;AACA,UAAM,UAAU,KAAK;AACrB,UAAM,SAAS,KAAK;AACpB,UAAM,MAAM,QAAQ;AACpB,QAAI,IAAI;AACR,QAAI;AACJ,WAAO,IAAI,KAAK,KAAK;AACnB,YAAM,SAAS,QAAQ,CAAC;AACxB,UAAI;AACF,iBAAS,KAAK,GAAG,OAAO,OAAO,QAAQ,KAAK,MAAM,MAAM;AACtD,iBAAO,IAAI,GAAG,OAAO,EAAE,CAAC;AAAA,QAC1B;AACA,cAAM,OAAO,MAAM,QAAQ,IAAI;AAAA,MACjC,SAAS,GAAG;AACV,YAAI,aAAa,sBAAsB;AACrC;AAAA,QACF;AACA,cAAM;AAAA,MACR;AACA,WAAK,QAAQ,OAAO,MAAM,KAAK,MAAM;AACrC,WAAK,WAAW,CAAC,MAAM;AACvB,WAAK,UAAU;AACf;AAAA,IACF;AACA,QAAI,MAAM,KAAK;AACb,YAAM,IAAI,MAAM,aAAa;AAAA,IAC/B;AACA,SAAK,OAAO,iBAAiB,KAAK,aAAa,IAAI;AACnD,WAAO;AAAA,EACT;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,KAAK,WAAW,KAAK,SAAS,WAAW,GAAG;AAC9C,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC7D;AACA,WAAO,KAAK,SAAS,CAAC;AAAA,EACxB;AACF;;;ACnDA,IAAI,cAA8B,uBAAO,OAAO,IAAI;AACpD,IAAIC,QAAO,MAAM;AAAA,EAJjB,OAIiB;AAAA;AAAA;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY,QAAQ,SAAS,UAAU;AACrC,SAAK,YAAY,YAA4B,uBAAO,OAAO,IAAI;AAC/D,SAAK,WAAW,CAAC;AACjB,QAAI,UAAU,SAAS;AACrB,YAAM,IAAoB,uBAAO,OAAO,IAAI;AAC5C,QAAE,MAAM,IAAI,EAAE,SAAS,cAAc,CAAC,GAAG,OAAO,EAAE;AAClD,WAAK,WAAW,CAAC,CAAC;AAAA,IACpB;AACA,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA,EACA,OAAO,QAAQ,MAAM,SAAS;AAC5B,SAAK,SAAS,EAAE,KAAK;AACrB,QAAI,UAAU;AACd,UAAM,QAAQ,iBAAiB,IAAI;AACnC,UAAM,eAAe,CAAC;AACtB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,IAAI,MAAM,CAAC;AACjB,YAAM,QAAQ,MAAM,IAAI,CAAC;AACzB,YAAM,UAAU,WAAW,GAAG,KAAK;AACnC,YAAM,MAAM,MAAM,QAAQ,OAAO,IAAI,QAAQ,CAAC,IAAI;AAClD,UAAI,OAAO,QAAQ,WAAW;AAC5B,kBAAU,QAAQ,UAAU,GAAG;AAC/B,YAAI,SAAS;AACX,uBAAa,KAAK,QAAQ,CAAC,CAAC;AAAA,QAC9B;AACA;AAAA,MACF;AACA,cAAQ,UAAU,GAAG,IAAI,IAAIA,MAAK;AAClC,UAAI,SAAS;AACX,gBAAQ,UAAU,KAAK,OAAO;AAC9B,qBAAa,KAAK,QAAQ,CAAC,CAAC;AAAA,MAC9B;AACA,gBAAU,QAAQ,UAAU,GAAG;AAAA,IACjC;AACA,YAAQ,SAAS,KAAK;AAAA,MACpB,CAAC,MAAM,GAAG;AAAA,QACR;AAAA,QACA,cAAc,aAAa,OAAO,CAAC,GAAG,GAAG,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;AAAA,QACjE,OAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,MAAM,QAAQ,YAAY,QAAQ;AAChD,UAAM,cAAc,CAAC;AACrB,aAAS,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,KAAK,KAAK;AACxD,YAAM,IAAI,KAAK,SAAS,CAAC;AACzB,YAAM,aAAa,EAAE,MAAM,KAAK,EAAE,eAAe;AACjD,YAAM,eAAe,CAAC;AACtB,UAAI,eAAe,QAAQ;AACzB,mBAAW,SAAyB,uBAAO,OAAO,IAAI;AACtD,oBAAY,KAAK,UAAU;AAC3B,YAAI,eAAe,eAAe,UAAU,WAAW,aAAa;AAClE,mBAAS,KAAK,GAAG,OAAO,WAAW,aAAa,QAAQ,KAAK,MAAM,MAAM;AACvE,kBAAM,MAAM,WAAW,aAAa,EAAE;AACtC,kBAAM,YAAY,aAAa,WAAW,KAAK;AAC/C,uBAAW,OAAO,GAAG,IAAI,SAAS,GAAG,KAAK,CAAC,YAAY,OAAO,GAAG,IAAI,WAAW,GAAG,KAAK,SAAS,GAAG;AACpG,yBAAa,WAAW,KAAK,IAAI;AAAA,UACnC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,QAAQ,MAAM;AACnB,UAAM,cAAc,CAAC;AACrB,SAAK,UAAU;AACf,UAAM,UAAU;AAChB,QAAI,WAAW,CAAC,OAAO;AACvB,UAAM,QAAQ,UAAU,IAAI;AAC5B,UAAM,gBAAgB,CAAC;AACvB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,OAAO,MAAM,CAAC;AACpB,YAAM,SAAS,MAAM,MAAM;AAC3B,YAAM,YAAY,CAAC;AACnB,eAAS,IAAI,GAAG,OAAO,SAAS,QAAQ,IAAI,MAAM,KAAK;AACrD,cAAM,OAAO,SAAS,CAAC;AACvB,cAAM,WAAW,KAAK,UAAU,IAAI;AACpC,YAAI,UAAU;AACZ,mBAAS,UAAU,KAAK;AACxB,cAAI,QAAQ;AACV,gBAAI,SAAS,UAAU,GAAG,GAAG;AAC3B,0BAAY;AAAA,gBACV,GAAG,KAAK,gBAAgB,SAAS,UAAU,GAAG,GAAG,QAAQ,KAAK,OAAO;AAAA,cACvE;AAAA,YACF;AACA,wBAAY,KAAK,GAAG,KAAK,gBAAgB,UAAU,QAAQ,KAAK,OAAO,CAAC;AAAA,UAC1E,OAAO;AACL,sBAAU,KAAK,QAAQ;AAAA,UACzB;AAAA,QACF;AACA,iBAAS,IAAI,GAAG,OAAO,KAAK,UAAU,QAAQ,IAAI,MAAM,KAAK;AAC3D,gBAAM,UAAU,KAAK,UAAU,CAAC;AAChC,gBAAM,SAAS,KAAK,YAAY,cAAc,CAAC,IAAI,EAAE,GAAG,KAAK,QAAQ;AACrE,cAAI,YAAY,KAAK;AACnB,kBAAM,UAAU,KAAK,UAAU,GAAG;AAClC,gBAAI,SAAS;AACX,0BAAY,KAAK,GAAG,KAAK,gBAAgB,SAAS,QAAQ,KAAK,OAAO,CAAC;AACvE,sBAAQ,UAAU;AAClB,wBAAU,KAAK,OAAO;AAAA,YACxB;AACA;AAAA,UACF;AACA,cAAI,CAAC,MAAM;AACT;AAAA,UACF;AACA,gBAAM,CAAC,KAAK,MAAM,OAAO,IAAI;AAC7B,gBAAM,QAAQ,KAAK,UAAU,GAAG;AAChC,gBAAM,iBAAiB,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AAC9C,cAAI,mBAAmB,QAAQ;AAC7B,kBAAM,IAAI,QAAQ,KAAK,cAAc;AACrC,gBAAI,GAAG;AACL,qBAAO,IAAI,IAAI,EAAE,CAAC;AAClB,0BAAY,KAAK,GAAG,KAAK,gBAAgB,OAAO,QAAQ,KAAK,SAAS,MAAM,CAAC;AAC7E,kBAAI,OAAO,KAAK,MAAM,SAAS,EAAE,QAAQ;AACvC,sBAAM,UAAU;AAChB,sBAAM,iBAAiB,EAAE,CAAC,EAAE,MAAM,IAAI,GAAG,UAAU;AACnD,sBAAM,iBAAiB,cAAc,cAAc,MAAM,CAAC;AAC1D,+BAAe,KAAK,KAAK;AAAA,cAC3B;AACA;AAAA,YACF;AAAA,UACF;AACA,cAAI,YAAY,QAAQ,QAAQ,KAAK,IAAI,GAAG;AAC1C,mBAAO,IAAI,IAAI;AACf,gBAAI,QAAQ;AACV,0BAAY,KAAK,GAAG,KAAK,gBAAgB,OAAO,QAAQ,QAAQ,KAAK,OAAO,CAAC;AAC7E,kBAAI,MAAM,UAAU,GAAG,GAAG;AACxB,4BAAY;AAAA,kBACV,GAAG,KAAK,gBAAgB,MAAM,UAAU,GAAG,GAAG,QAAQ,QAAQ,KAAK,OAAO;AAAA,gBAC5E;AAAA,cACF;AAAA,YACF,OAAO;AACL,oBAAM,UAAU;AAChB,wBAAU,KAAK,KAAK;AAAA,YACtB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,iBAAW,UAAU,OAAO,cAAc,MAAM,KAAK,CAAC,CAAC;AAAA,IACzD;AACA,QAAI,YAAY,SAAS,GAAG;AAC1B,kBAAY,KAAK,CAAC,GAAG,MAAM;AACzB,eAAO,EAAE,QAAQ,EAAE;AAAA,MACrB,CAAC;AAAA,IACH;AACA,WAAO,CAAC,YAAY,IAAI,CAAC,EAAE,SAAS,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC,CAAC;AAAA,EACrE;AACF;;;AC3JA,IAAI,aAAa,MAAM;AAAA,EAHvB,OAGuB;AAAA;AAAA;AAAA,EACrB,OAAO;AAAA,EACP;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ,IAAIC,MAAK;AAAA,EACxB;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,UAAM,UAAU,uBAAuB,IAAI;AAC3C,QAAI,SAAS;AACX,eAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,aAAK,MAAM,OAAO,QAAQ,QAAQ,CAAC,GAAG,OAAO;AAAA,MAC/C;AACA;AAAA,IACF;AACA,SAAK,MAAM,OAAO,QAAQ,MAAM,OAAO;AAAA,EACzC;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,WAAO,KAAK,MAAM,OAAO,QAAQ,IAAI;AAAA,EACvC;AACF;;;ACjBA,IAAIC,QAAO,cAAc,KAAS;AAAA,EALlC,OAKkC;AAAA;AAAA;AAAA,EAChC,YAAY,UAAU,CAAC,GAAG;AACxB,UAAM,OAAO;AACb,SAAK,SAAS,QAAQ,UAAU,IAAI,YAAY;AAAA,MAC9C,SAAS,CAAC,IAAI,aAAa,GAAG,IAAI,WAAW,CAAC;AAAA,IAChD,CAAC;AAAA,EACH;AACF;;;ACTO,IAAM,kBAA6C;AAAA,EACzD,kBAAkB;AAAA,IACjB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,UAAU;AAAA,EACX;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,UAAU;AAAA,EACX;AACD;AAGO,IAAM,gBAAgB;AAOtB,SAAS,iBAA2B;AAC1C,SAAO,OAAO,KAAK,eAAe;AACnC;AAFgB;;;ACjCT,IAAM,uBAAuB;AAC7B,IAAM,0BAA0B;AAGhC,IAAM,kBAAkB;AACxB,IAAM,sBAAsB;AAC5B,IAAM,oBAAoB;AAG1B,IAAM,oBAAoB,IAAI,KAAK;AACnC,IAAM,eAAe;AAGrB,IAAM,gCAAgC;AACtC,IAAM,qBAAqB;;;ACV3B,IAAM,qBAAqB;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAGO,IAAM,wBAAwB;AAK9B,IAAM,8BAA8B;AAGpC,IAAM,0BAA0B;AAIhC,IAAM,sBAAsB;AAG5B,IAAM,wBAAwB;AAAA,EACpC,kBAAkB;AACnB;AAGO,IAAM,0BAA0B,CAAC,KAAK,GAAG;AAGzC,IAAM,2BAA2B;AAAA,EACvC,MAAM;AAAA,EACN,KAAK;AAAA,EACL,QAAQ;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,EACV;AAAA,EACA,MAAM;AAAA,IACL,OAAO;AAAA,IACP,SAAS;AAAA,EACV;AACD;AAGO,IAAM,2BAA2B;AAAA,EACvC,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,mBAAmB;AACpB;;;ACnBO,IAAM,cAAN,MAAkB;AAAA,EArCzB,OAqCyB;AAAA;AAAA;AAAA,EAChB;AAAA,EACA,cAA6B;AAAA,EAErC,YAAYC,MAAU;AACrB,SAAK,MAAMA;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAKA,MAAa,iBAAgC;AAC5C,QAAI,CAAC,KAAK,IAAI,qBAAqB;AAClC,YAAM,IAAI,MAAM,6FAA6F;AAAA,IAC9G;AAEA,QAAI;AAEH,UAAI,kBAAkB;AAEtB,UAAI;AACH,cAAM,cAAc,MAAM,KAAK,IAAI,cAAc,IAAI,cAAc,MAAM;AACzE,YAAI,aAAa;AAChB,4BAAkB;AAClB,kBAAQ,IAAI,kCAAkC;AAAA,QAC/C;AAAA,MACD,SAAS,SAAS;AACjB,gBAAQ,IAAI,oDAAoD,OAAO;AAAA,MACxE;AAGA,UAAI,iBAAiB;AACpB,cAAMC,mBAAkB,gBAAgB,cAAc,KAAK,IAAI;AAC/D,YAAIA,mBAAkB,mBAAmB;AACxC,eAAK,cAAc,gBAAgB;AACnC,kBAAQ,IAAI,iCAAiC,KAAK,MAAMA,mBAAkB,GAAI,CAAC,eAAe;AAC9F;AAAA,QACD;AACA,gBAAQ,IAAI,uCAAuC;AAAA,MACpD;AAGA,YAAM,cAAiC,KAAK,MAAM,KAAK,IAAI,mBAAmB;AAG9E,YAAM,kBAAkB,YAAY,cAAc,KAAK,IAAI;AAC3D,UAAI,kBAAkB,mBAAmB;AAExC,aAAK,cAAc,YAAY;AAC/B,gBAAQ,IAAI,+BAA+B,KAAK,MAAM,kBAAkB,GAAI,CAAC,eAAe;AAG5F,cAAM,KAAK,eAAe,YAAY,cAAc,YAAY,WAAW;AAC3E;AAAA,MACD;AAGA,cAAQ,IAAI,mCAAmC;AAC/C,YAAM,KAAK,qBAAqB,YAAY,aAAa;AAAA,IAC1D,SAAS,GAAY;AACpB,YAAM,eAAe,aAAa,QAAQ,EAAE,UAAU,OAAO,CAAC;AAC9D,cAAQ,MAAM,wCAAwC,CAAC;AACvD,YAAM,IAAI,MAAM,4BAA4B,YAAY;AAAA,IACzD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,qBAAqB,cAAqC;AACvE,YAAQ,IAAI,2BAA2B;AAEvC,UAAM,kBAAkB,MAAM,MAAM,mBAAmB;AAAA,MACtD,QAAQ;AAAA,MACR,SAAS;AAAA,QACR,gBAAgB;AAAA,MACjB;AAAA,MACA,MAAM,IAAI,gBAAgB;AAAA,QACzB,WAAW;AAAA,QACX,eAAe;AAAA,QACf,eAAe;AAAA,QACf,YAAY;AAAA,MACb,CAAC;AAAA,IACF,CAAC;AAED,QAAI,CAAC,gBAAgB,IAAI;AACxB,YAAM,YAAY,MAAM,gBAAgB,KAAK;AAC7C,cAAQ,MAAM,yBAAyB,SAAS;AAChD,YAAM,IAAI,MAAM,yBAAyB,SAAS,EAAE;AAAA,IACrD;AAEA,UAAM,cAAe,MAAM,gBAAgB,KAAK;AAChD,SAAK,cAAc,YAAY;AAG/B,UAAM,aAAa,KAAK,IAAI,IAAI,YAAY,aAAa;AAEzD,YAAQ,IAAI,8BAA8B;AAC1C,YAAQ,IAAI,wBAAwB,YAAY,UAAU,UAAU;AAGpE,UAAM,KAAK,eAAe,YAAY,cAAc,UAAU;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,eAAe,aAAqB,YAAmC;AACpF,QAAI;AACH,YAAM,YAAY;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,WAAW,KAAK,IAAI;AAAA,MACrB;AAGA,YAAM,aAAa,KAAK,OAAO,aAAa,KAAK,IAAI,KAAK,GAAI,IAAI;AAElE,UAAI,aAAa,GAAG;AACnB,cAAM,KAAK,IAAI,cAAc,IAAI,cAAc,KAAK,UAAU,SAAS,GAAG;AAAA,UACzE,eAAe;AAAA,QAChB,CAAC;AACD,gBAAQ,IAAI,0CAA0C,UAAU,UAAU;AAAA,MAC3E,OAAO;AACN,gBAAQ,IAAI,2CAA2C;AAAA,MACxD;AAAA,IACD,SAAS,SAAS;AACjB,cAAQ,MAAM,wCAAwC,OAAO;AAAA,IAE9D;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAa,kBAAiC;AAC7C,QAAI;AACH,YAAM,KAAK,IAAI,cAAc,OAAO,YAAY;AAChD,cAAQ,IAAI,sCAAsC;AAAA,IACnD,SAAS,SAAS;AACjB,cAAQ,IAAI,4BAA4B,OAAO;AAAA,IAChD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAa,qBAA8C;AAC1D,QAAI;AACH,YAAM,cAAc,MAAM,KAAK,IAAI,cAAc,IAAI,cAAc,MAAM;AACzE,UAAI,aAAa;AAChB,cAAM,YAAY;AAClB,cAAM,kBAAkB,UAAU,cAAc,KAAK,IAAI;AAEzD,eAAO;AAAA,UACN,QAAQ;AAAA,UACR,WAAW,IAAI,KAAK,UAAU,SAAS,EAAE,YAAY;AAAA,UACrD,YAAY,IAAI,KAAK,UAAU,WAAW,EAAE,YAAY;AAAA,UACxD,2BAA2B,KAAK,MAAM,kBAAkB,GAAI;AAAA,UAC5D,YAAY,kBAAkB;AAAA;AAAA,QAE/B;AAAA,MACD;AACA,aAAO,EAAE,QAAQ,OAAO,SAAS,0BAA0B;AAAA,IAC5D,SAAS,GAAY;AACpB,YAAM,eAAe,aAAa,QAAQ,EAAE,UAAU,OAAO,CAAC;AAC9D,aAAO,EAAE,QAAQ,OAAO,OAAO,aAAa;AAAA,IAC7C;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAa,aAAa,QAAgB,MAA+B,UAAmB,OAAyB;AACpH,UAAM,KAAK,eAAe;AAE1B,UAAM,WAAW,MAAM,MAAM,GAAG,oBAAoB,IAAI,uBAAuB,IAAI,MAAM,IAAI;AAAA,MAC5F,QAAQ;AAAA,MACR,SAAS;AAAA,QACR,gBAAgB;AAAA,QAChB,eAAe,UAAU,KAAK,WAAW;AAAA,MAC1C;AAAA,MACA,MAAM,KAAK,UAAU,IAAI;AAAA,IAC1B,CAAC;AAED,QAAI,CAAC,SAAS,IAAI;AACjB,UAAI,SAAS,WAAW,OAAO,CAAC,SAAS;AACxC,gBAAQ,IAAI,qDAAqD;AACjE,aAAK,cAAc;AACnB,cAAM,KAAK,gBAAgB;AAC3B,cAAM,KAAK,eAAe;AAC1B,eAAO,KAAK,aAAa,QAAQ,MAAM,IAAI;AAAA,MAC5C;AACA,YAAM,YAAY,MAAM,SAAS,KAAK;AACtC,YAAM,IAAI,MAAM,+BAA+B,SAAS,MAAM,KAAK,SAAS,EAAE;AAAA,IAC/E;AAEA,WAAO,SAAS,KAAK;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKO,iBAAgC;AACtC,WAAO,KAAK;AAAA,EACb;AACD;;;AC1NO,SAAS,iBAAiB,UAAyC;AACzE,MAAI,CAAC,UAAU;AACd,WAAO,EAAE,SAAS,OAAO,OAAO,wBAAwB;AAAA,EACzD;AAEA,MAAI,SAAS,WAAW,aAAa,GAAG;AAEvC,UAAM,CAAC,cAAc,UAAU,IAAI,SAAS,MAAM,GAAG;AAErD,QAAI,CAAC,YAAY;AAChB,aAAO,EAAE,SAAS,OAAO,OAAO,8BAA8B;AAAA,IAC/D;AAEA,UAAM,WAAW,aAAa,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AACxD,UAAM,SAAS,SAAS,MAAM,GAAG,EAAE,CAAC;AAEpC,UAAM,mBAAmB,CAAC,QAAQ,OAAO,OAAO,OAAO,MAAM;AAC7D,QAAI,CAAC,iBAAiB,SAAS,OAAO,YAAY,CAAC,GAAG;AACrD,aAAO;AAAA,QACN,SAAS;AAAA,QACT,OAAO,6BAA6B,MAAM,wBAAwB,iBAAiB,KAAK,IAAI,CAAC;AAAA,MAC9F;AAAA,IACD;AAGA,QAAI;AACH,WAAK,WAAW,UAAU,GAAG,GAAG,CAAC;AAAA,IAClC,QAAQ;AACP,aAAO,EAAE,SAAS,OAAO,OAAO,0BAA0B;AAAA,IAC3D;AAEA,WAAO,EAAE,SAAS,MAAM,UAAU,OAAO;AAAA,EAC1C;AAEA,MAAI,SAAS,WAAW,SAAS,KAAK,SAAS,WAAW,UAAU,GAAG;AAEtE,QAAI;AACH,UAAI,IAAI,QAAQ;AAChB,aAAO,EAAE,SAAS,MAAM,UAAU,aAAa;AAAA,IAChD,QAAQ;AACP,aAAO,EAAE,SAAS,OAAO,OAAO,qBAAqB;AAAA,IACtD;AAAA,EACD;AAEA,SAAO,EAAE,SAAS,OAAO,OAAO,wDAAwD;AACzF;AA7CgB;;;ACZT,IAAM,4BAAN,MAAgC;AAAA,EAbvC,OAauC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOtC,OAAO,0BAA0B,QAAqB,SAAyB;AAC9E,UAAM,eAAe,QAAQ,SAAS,OAAO;AAE7C,YAAQ,QAAQ;AAAA,MACf,KAAK;AACJ,eAAO,yBAAyB;AAAA,MACjC,KAAK;AACJ,eAAO,yBAAyB;AAAA,MACjC,KAAK;AACJ,eAAO,eAAe,yBAAyB,OAAO,QAAQ,yBAAyB,OAAO;AAAA,MAC/F,KAAK;AACJ,eAAO,eAAe,yBAAyB,KAAK,QAAQ,yBAAyB,KAAK;AAAA,MAC3F;AACC,eAAO;AAAA,IACT;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,mBAAmB,OAAsC;AAC/D,WAAO,OAAO,UAAU,YAAY,CAAC,QAAQ,OAAO,UAAU,MAAM,EAAE,SAAS,KAAK;AAAA,EACrF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,qBAAqBC,MAAmE;AAC9F,UAAM,iBAA0E,CAAC;AAEjF,QAAIA,KAAI,wCAAwC;AAC/C,qBAAe,KAAK;AAAA,QACnB,UAAU,yBAAyB;AAAA,QACnC,WAAWA,KAAI;AAAA,MAChB,CAAC;AAAA,IACF;AAEA,QAAIA,KAAI,yCAAyC;AAChD,qBAAe,KAAK;AAAA,QACnB,UAAU,yBAAyB;AAAA,QACnC,WAAWA,KAAI;AAAA,MAChB,CAAC;AAAA,IACF;AAEA,QAAIA,KAAI,+CAA+C;AACtD,qBAAe,KAAK;AAAA,QACnB,UAAU,yBAAyB;AAAA,QACnC,WAAWA,KAAI;AAAA,MAChB,CAAC;AAAA,IACF;AAEA,QAAIA,KAAI,+CAA+C;AACtD,qBAAe,KAAK;AAAA,QACnB,UAAU,yBAAyB;AAAA,QACnC,WAAWA,KAAI;AAAA,MAChB,CAAC;AAAA,IACF;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,uBAAuB,SAAiB,gBAAgC;AAC9E,UAAM,YAAY,gBAAgB,OAAO;AAGzC,QAAI,WAAW,UAAU;AAGxB,UAAI,mBAAmB,GAAG;AACzB,gBAAQ,IAAI,6BAA6B,OAAO,wDAAwD;AACxG,eAAO;AAAA,MACR;AAGA,UAAI,iBAAiB,IAAI;AACxB,gBAAQ;AAAA,UACP,+CAA+C,cAAc,eAAe,OAAO;AAAA,QACpF;AACA,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,sBACN,SACA,UAA0C,CAAC,GAC3C,uBACA,kBACAA,MAC0B;AAC1B,UAAM,mBAA4C;AAAA,MACjD,aAAa,QAAQ,eAAe;AAAA,MACpC,iBAAiB,QAAQ;AAAA,MACzB,MAAM,QAAQ;AAAA,MACd,eAAe,OAAO,QAAQ,SAAS,WAAW,CAAC,QAAQ,IAAI,IAAI,QAAQ;AAAA,MAC3E,iBAAiB,QAAQ;AAAA,MACzB,kBAAkB,QAAQ;AAAA,MAC1B,MAAM,QAAQ;AAAA,IACf;AAEA,QAAI,QAAQ,iBAAiB,SAAS,eAAe;AACpD,uBAAiB,mBAAmB;AAAA,IACrC;AAGA,QAAIA,MAAK;AACR,YAAM,iBAAiB,KAAK,qBAAqBA,IAAG;AACpD,UAAI,eAAe,SAAS,GAAG;AAC9B,yBAAiB,iBAAiB;AAAA,MACnC;AAAA,IACD;AAEA,UAAM,YAAY,gBAAgB,OAAO;AACzC,UAAM,kBAAkB,WAAW,YAAY;AAE/C,QAAI,iBAAiB;AACpB,UAAI,iBAAiB,QAAQ,mBAAmB;AAGhD,YAAM,mBACL,QAAQ,oBAAoB,QAAQ,YAAY,oBAAoB,QAAQ,cAAc;AAE3F,UAAI,oBAAoB,KAAK,mBAAmB,gBAAgB,GAAG;AAClE,yBAAiB,KAAK,0BAA0B,kBAAkB,OAAO;AAEzE,YAAI,qBAAqB,QAAQ;AAChC,6BAAmB;AAAA,QACpB,OAAO;AACN,6BAAmB;AAAA,QACpB;AAAA,MACD;AAEA,YAAM,kBAAkB,KAAK,uBAAuB,SAAS,cAAc;AAE3E,UAAI,yBAAyB,kBAAkB;AAE9C,yBAAiB,iBAAiB;AAAA,UACjC,gBAAgB;AAAA,UAChB,iBAAiB;AAAA;AAAA,QAClB;AACA,gBAAQ,IAAI,iDAAiD,OAAO,kBAAkB,eAAe,EAAE;AAAA,MACxG,OAAO;AAGN,yBAAiB,iBAAiB;AAAA,UACjC,gBAAgB,KAAK,uBAAuB,SAAS,uBAAuB;AAAA,UAC5E,iBAAiB;AAAA;AAAA,QAClB;AAAA,MACD;AAAA,IACD;AAGA,WAAO,KAAK,gBAAgB,EAAE,QAAQ,CAAC,QAAQ,iBAAiB,GAAG,MAAM,UAAa,OAAO,iBAAiB,GAAG,CAAC;AAClH,WAAO;AAAA,EACR;AAAA,EAEA,OAAO,oBAAoB,UAA0C,CAAC,GAAG;AACxE,UAAM,QAAQ,CAAC;AACf,QAAI,aAAa,CAAC;AAElB,QAAI,MAAM,QAAQ,QAAQ,KAAK,KAAK,QAAQ,MAAM,SAAS,GAAG;AAC7D,YAAM,uBAAuB,QAAQ,MAAM,IAAI,CAAC,SAAS;AACxD,YAAI,aAAa,KAAK,SAAS;AAE/B,YAAI,YAAY;AACf,gBAAM,SAAS;AACf,uBAAa,OAAO,KAAK,UAAU,EACjC,OAAO,CAAC,QAAQ,CAAC,IAAI,WAAW,GAAG,CAAC,EACpC;AAAA,YACA,CAAC,OAAO,QAAQ;AACf,oBAAM,GAAG,IAAI,OAAO,GAAG;AACvB,qBAAO;AAAA,YACR;AAAA,YACA,CAAC;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,UACN,MAAM,KAAK,SAAS;AAAA,UACpB,aAAa,KAAK,SAAS;AAAA,UAC3B;AAAA,QACD;AAAA,MACD,CAAC;AAED,YAAM,KAAK,EAAE,qBAAqB,CAAC;AAEnC,UAAI,QAAQ,aAAa;AACxB,YAAI,QAAQ,gBAAgB,QAAQ;AACnC,uBAAa,EAAE,uBAAuB,EAAE,MAAM,OAAO,EAAE;AAAA,QACxD,WAAW,QAAQ,gBAAgB,QAAQ;AAC1C,uBAAa,EAAE,uBAAuB,EAAE,MAAM,OAAO,EAAE;AAAA,QACxD,WAAW,OAAO,QAAQ,gBAAgB,YAAY,QAAQ,YAAY,UAAU;AACnF,uBAAa;AAAA,YACZ,uBAAuB;AAAA,cACtB,MAAM;AAAA,cACN,sBAAsB,CAAC,QAAQ,YAAY,SAAS,IAAI;AAAA,YACzD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAO,EAAE,OAAO,WAAW;AAAA,EAC5B;AACD;;;AC7OO,IAAM,2BAAN,MAA+B;AAAA,EAPtC,OAOsC;AAAA;AAAA;AAAA,EAC7B;AAAA,EAER,YAAYC,MAAU;AACrB,SAAK,MAAMA;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAKA,YAAqB;AACpB,WAAO,KAAK,IAAI,gCAAgC;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,eAAsC;AACtD,WAAO,sBAAsB,aAAmD,KAAK;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiBC,QAAyB;AACzC,WACCA,kBAAiB,UAChBA,OAAM,QAAQ,SAAS,4BAA4B,KAAKA,OAAM,QAAQ,SAAS,4BAA4B;AAAA,EAE9G;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,QAAyB;AAC1C,WAAQ,wBAA8C,SAAS,MAAM;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,eAAgC;AACrD,WAAO,KAAK,UAAU,KAAK,KAAK,iBAAiB,aAAa,MAAM;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB,eAAuB,eAA+B;AAC9E,WAAO,uBAAuB,aAAa,OAAO,aAAa;AAAA;AAAA;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,2BACL,eACA,cACA,UACA,SAMA,iBASyD;AACzD,UAAM,gBAAgB,KAAK,iBAAiB,aAAa;AACzD,QAAI,CAAC,iBAAiB,CAAC,KAAK,UAAU,GAAG;AACxC,aAAO;AAAA,IACR;AAEA,YAAQ,IAAI,kCAAkC,aAAa,kCAAkC,aAAa,EAAE;AAE5G,QAAI,UAAU;AACd,QAAI;AAGJ,eAAW,KAAK,yBAAyB,eAAe,aAAa;AAGrE,qBAAiB,SAAS,gBAAgB,eAAe,cAAc,UAAU,OAAO,GAAG;AAC1F,UAAI,MAAM,SAAS,UAAU,OAAO,MAAM,SAAS,UAAU;AAC5D,mBAAW,MAAM;AAAA,MAClB,WAAW,MAAM,SAAS,WAAW,OAAO,MAAM,SAAS,UAAU;AACpE,gBAAQ,MAAM;AAAA,MACf;AAAA,IACD;AAEA,WAAO,EAAE,SAAS,MAAM;AAAA,EACzB;AACD;;;AC/BA,SAAS,cAAc,SAAiD;AACvE,SAAO,QAAQ,SAAS,UAAU,OAAO,QAAQ,SAAS;AAC3D;AAFS;AAQF,IAAM,kBAAN,MAAsB;AAAA,EArF7B,OAqF6B;AAAA;AAAA;AAAA,EACpB;AAAA,EACA;AAAA,EACA,YAA2B;AAAA,EAC3B;AAAA,EAER,YAAYC,MAAU,aAA0B;AAC/C,SAAK,MAAMA;AACX,SAAK,cAAc;AACnB,SAAK,mBAAmB,IAAI,yBAAyBA,IAAG;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAa,oBAAqC;AACjD,QAAI,KAAK,IAAI,mBAAmB;AAC/B,aAAO,KAAK,IAAI;AAAA,IACjB;AACA,QAAI,KAAK,WAAW;AACnB,aAAO,KAAK;AAAA,IACb;AAEA,QAAI;AACH,YAAM,mBAAmB;AACzB,YAAM,eAAgB,MAAM,KAAK,YAAY,aAAa,kBAAkB;AAAA,QAC3E,yBAAyB;AAAA,QACzB,UAAU,EAAE,aAAa,iBAAiB;AAAA,MAC3C,CAAC;AAED,UAAI,aAAa,yBAAyB;AACzC,aAAK,YAAY,aAAa;AAC9B,eAAO,aAAa;AAAA,MACrB;AACA,YAAM,IAAI,MAAM,qFAAqF;AAAA,IACtG,SAASC,QAAgB;AACxB,YAAM,eAAeA,kBAAiB,QAAQA,OAAM,UAAU,OAAOA,MAAK;AAC1E,cAAQ,MAAM,kCAAkC,YAAY;AAC5D,YAAM,IAAI;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,OAAe,eAAe,QAAoE;AACjG,UAAM,SAAS,OAAO,YAAY,IAAI,kBAAkB,CAAC,EAAE,UAAU;AACrE,QAAI,SAAS;AACb,QAAI,eAAe;AAEnB,WAAO,MAAM;AACZ,YAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,UAAI,MAAM;AACT,YAAI,cAAc;AACjB,cAAI;AACH,kBAAM,KAAK,MAAM,YAAY;AAAA,UAC9B,SAAS,GAAG;AACX,oBAAQ,MAAM,wCAAwC,CAAC;AAAA,UACxD;AAAA,QACD;AACA;AAAA,MACD;AAEA,gBAAU;AACV,YAAM,QAAQ,OAAO,MAAM,IAAI;AAC/B,eAAS,MAAM,IAAI,KAAK;AAExB,iBAAW,QAAQ,OAAO;AACzB,YAAI,KAAK,KAAK,MAAM,IAAI;AACvB,cAAI,cAAc;AACjB,gBAAI;AACH,oBAAM,KAAK,MAAM,YAAY;AAAA,YAC9B,SAAS,GAAG;AACX,sBAAQ,MAAM,kCAAkC,CAAC;AAAA,YAClD;AACA,2BAAe;AAAA,UAChB;AAAA,QACD,WAAW,KAAK,WAAW,QAAQ,GAAG;AACrC,0BAAgB,KAAK,UAAU,CAAC;AAAA,QACjC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKQ,sBAAsB,KAA0C;AACvE,UAAM,OAAO,IAAI,SAAS,cAAc,UAAU;AAGlD,QAAI,IAAI,SAAS,QAAQ;AACxB,aAAO;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,UACN;AAAA,YACC,kBAAkB;AAAA,cACjB,MAAM,IAAI,gBAAgB;AAAA,cAC1B,UAAU;AAAA,gBACT,QAAQ,OAAO,IAAI,YAAY,WAAW,IAAI,UAAU,KAAK,UAAU,IAAI,OAAO;AAAA,cACnF;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAGA,QAAI,IAAI,SAAS,eAAe,IAAI,cAAc,IAAI,WAAW,SAAS,GAAG;AAC5E,YAAM,QAAsB,CAAC;AAG7B,UAAI,OAAO,IAAI,YAAY,YAAY,IAAI,QAAQ,KAAK,GAAG;AAC1D,cAAM,KAAK,EAAE,MAAM,IAAI,QAAQ,CAAC;AAAA,MACjC;AAGA,iBAAW,YAAY,IAAI,YAAY;AACtC,YAAI,SAAS,SAAS,YAAY;AACjC,gBAAM,KAAK;AAAA,YACV,cAAc;AAAA,cACb,MAAM,SAAS,SAAS;AAAA,cACxB,MAAM,KAAK,MAAM,SAAS,SAAS,SAAS;AAAA,YAC7C;AAAA,UACD,CAAC;AAAA,QACF;AAAA,MACD;AAEA,aAAO,EAAE,MAAM,SAAS,MAAM;AAAA,IAC/B;AAEA,QAAI,OAAO,IAAI,YAAY,UAAU;AAEpC,aAAO;AAAA,QACN;AAAA,QACA,OAAO,CAAC,EAAE,MAAM,IAAI,QAAQ,CAAC;AAAA,MAC9B;AAAA,IACD;AAEA,QAAI,MAAM,QAAQ,IAAI,OAAO,GAAG;AAE/B,YAAM,QAAsB,CAAC;AAE7B,iBAAW,WAAW,IAAI,SAAS;AAClC,YAAI,QAAQ,SAAS,QAAQ;AAC5B,gBAAM,KAAK,EAAE,MAAM,QAAQ,KAAK,CAAC;AAAA,QAClC,WAAW,QAAQ,SAAS,eAAe,QAAQ,WAAW;AAC7D,gBAAM,WAAW,QAAQ,UAAU;AAGnC,gBAAM,aAAa,iBAAiB,QAAQ;AAC5C,cAAI,CAAC,WAAW,SAAS;AACxB,kBAAM,IAAI,MAAM,kBAAkB,WAAW,KAAK,EAAE;AAAA,UACrD;AAEA,cAAI,SAAS,WAAW,OAAO,GAAG;AAEjC,kBAAM,CAAC,UAAU,UAAU,IAAI,SAAS,MAAM,GAAG;AACjD,kBAAM,YAAY,SAAS,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AAErD,kBAAM,KAAK;AAAA,cACV,YAAY;AAAA,gBACX,UAAU;AAAA,gBACV,MAAM;AAAA,cACP;AAAA,YACD,CAAC;AAAA,UACF,OAAO;AAIN,kBAAM,KAAK;AAAA,cACV,UAAU;AAAA,gBACT,UAAU,WAAW,YAAY;AAAA,gBACjC,SAAS;AAAA,cACV;AAAA,YACD,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD;AAEA,aAAO,EAAE,MAAM,MAAM;AAAA,IACtB;AAGA,WAAO;AAAA,MACN;AAAA,MACA,OAAO,CAAC,EAAE,MAAM,OAAO,IAAI,OAAO,EAAE,CAAC;AAAA,IACtC;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKQ,qBAAqB,SAA0B;AACtD,WAAO,gBAAgB,OAAO,GAAG,kBAAkB;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAKQ,qBAAqB,UAA2B;AACvD,UAAM,aAAa,iBAAiB,QAAQ;AAC5C,WAAO,WAAW;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,cACN,SACA,cACA,UACA,SAgB8B;AAC9B,UAAM,KAAK,YAAY,eAAe;AACtC,UAAM,YAAY,MAAM,KAAK,kBAAkB;AAE/C,UAAM,WAAW,SAAS,IAAI,CAAC,QAAQ,KAAK,sBAAsB,GAAG,CAAC;AAEtE,QAAI,cAAc;AACjB,eAAS,QAAQ,EAAE,MAAM,QAAQ,OAAO,CAAC,EAAE,MAAM,aAAa,CAAC,EAAE,CAAC;AAAA,IACnE;AAGA,UAAM,kBAAkB,gBAAgB,OAAO,GAAG,YAAY;AAC9D,UAAM,wBAAwB,KAAK,IAAI,yBAAyB;AAChE,UAAM,wBAAwB,KAAK,IAAI,yBAAyB;AAChE,UAAM,0BAA0B,KAAK,IAAI,+BAA+B;AACxE,UAAM,mBAAmB,SAAS,oBAAoB;AAEtD,UAAM,MAAM;AAAA,MACX,iBAAiB,SAAS;AAAA,MAC1B,OAAO,SAAS;AAAA,MAChB,aAAa,SAAS;AAAA,MACtB,YAAY,SAAS;AAAA,MACrB,aAAa,SAAS;AAAA,MACtB,OAAO,SAAS;AAAA,MAChB,MAAM,SAAS;AAAA,MACf,kBAAkB,SAAS;AAAA,MAC3B,mBAAmB,SAAS;AAAA,MAC5B,MAAM,SAAS;AAAA,MACf,iBAAiB,SAAS;AAAA,IAC3B;AAGA,UAAM,mBAAmB,0BAA0B;AAAA,MAClD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACN;AAEA,UAAM,EAAE,OAAO,WAAW,IAAI,0BAA0B,oBAAoB,GAAG;AAG/E,QAAI,qBAAqB;AACzB,QAAI,mBAAmB,yBAAyB,CAAC,kBAAkB;AAClE,aAAO,KAAK,wBAAwB,SAAS,UAAU,uBAAuB;AAC9E,2BAAqB;AAAA,IACtB;AAEA,UAAM,gBAAgB;AAAA,MACrB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAEA,WAAO,KAAK;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,oBAAoB;AAAA,MACpB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,OAAe,wBACd,SACA,UACA,kBAA2B,OACG;AAE9B,UAAM,kBAAkB,SAAS,OAAO,CAAC,QAAQ,IAAI,SAAS,MAAM,EAAE,IAAI;AAC1E,QAAI,cAAc;AAElB,QAAI,iBAAiB;AACpB,UAAI,OAAO,gBAAgB,YAAY,UAAU;AAChD,sBAAc,gBAAgB;AAAA,MAC/B,WAAW,MAAM,QAAQ,gBAAgB,OAAO,GAAG;AAClD,sBAAc,gBAAgB,QAC5B,OAAO,aAAa,EACpB,IAAI,CAAC,MAAM,EAAE,IAAI,EACjB,KAAK,GAAG;AAAA,MACX;AAAA,IACD;AAGA,UAAM,iBAAiB,YAAY,UAAU,GAAG,GAAG,KAAK,YAAY,SAAS,MAAM,QAAQ;AAE3F,QAAI,iBAAiB;AAEpB,YAAM;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAGA,YAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,qBAAqB,CAAC;AACzE,YAAM,iBAAiB,mBAAmB,IAAI,CAAC,QAAQ,IAAI,QAAQ,oBAAoB,cAAc,CAAC;AACtG,YAAM,oBAAoB,eAAe,KAAK,EAAE;AAIhD,YAAM,SAAmB,CAAC;AAC1B,UAAI,gBAAgB;AAEpB,aAAO,cAAc,SAAS,GAAG;AAChC,YAAI,cAAc,UAAU,6BAA6B;AACxD,iBAAO,KAAK,aAAa;AACzB;AAAA,QACD;AAGA,YAAI,WAAW;AACf,cAAM,cAAc,cAAc,UAAU,GAAG,WAAW,EAAE;AAC5D,cAAM,aAAa,CAAC,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAE3D,mBAAW,aAAa,YAAY;AACnC,gBAAM,YAAY,YAAY,YAAY,SAAS;AACnD,cAAI,YAAY,8BAA8B,KAAK;AAElD,uBAAW,YAAY;AACvB;AAAA,UACD;AAAA,QACD;AAEA,eAAO,KAAK,cAAc,UAAU,GAAG,QAAQ,CAAC;AAChD,wBAAgB,cAAc,UAAU,QAAQ;AAAA,MACjD;AAEA,iBAAW,SAAS,QAAQ;AAC3B,cAAM;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAGA,cAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AAAA,MACvD;AAAA,IAGD,OAAO;AAEN,YAAM,iBAAiB,mBAAmB,IAAI,CAAC,QAAQ,IAAI,QAAQ,oBAAoB,cAAc,CAAC;AAGtG,iBAAW,iBAAiB,gBAAgB;AAC3C,cAAM,gBAA+B,EAAE,WAAW,cAAc;AAChE,cAAM;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAGA,cAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,qBAAqB,CAAC;AAAA,MAC1E;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,OAAe,qBACd,eACA,qBAA8B,OAC9B,UAAmB,OACnB,wBAAiC,OACjC,eAC8B;AAC9B,UAAM,WAAW,MAAM,MAAM,GAAG,oBAAoB,IAAI,uBAAuB,kCAAkC;AAAA,MAChH,QAAQ;AAAA,MACR,SAAS;AAAA,QACR,gBAAgB;AAAA,QAChB,eAAe,UAAU,KAAK,YAAY,eAAe,CAAC;AAAA,MAC3D;AAAA,MACA,MAAM,KAAK,UAAU,aAAa;AAAA,IACnC,CAAC;AAED,QAAI,CAAC,SAAS,IAAI;AACjB,UAAI,SAAS,WAAW,OAAO,CAAC,SAAS;AACxC,gBAAQ,IAAI,uEAAuE;AACnF,cAAM,KAAK,YAAY,gBAAgB;AACvC,cAAM,KAAK,YAAY,eAAe;AACtC,eAAO,KAAK,qBAAqB,eAAe,oBAAoB,MAAM,uBAAuB,aAAa;AAC9G;AAAA,MACD;AAGA,UAAI,KAAK,iBAAiB,kBAAkB,SAAS,MAAM,KAAK,CAAC,WAAW,eAAe;AAC1F,cAAM,gBAAgB,KAAK,iBAAiB,iBAAiB,aAAa;AAC1E,YAAI,iBAAiB,KAAK,iBAAiB,UAAU,GAAG;AACvD,kBAAQ;AAAA,YACP,OAAO,SAAS,MAAM,oBAAoB,aAAa,kCAAkC,aAAa;AAAA,UACvG;AAGA,gBAAM,kBAAkB;AAAA,YACvB,GAAI;AAAA,YACJ,OAAO;AAAA,UACR;AAGA,gBAAM;AAAA,YACL,MAAM;AAAA,YACN,MAAM,KAAK,iBAAiB,yBAAyB,eAAe,aAAa;AAAA,UAClF;AAEA,iBAAO,KAAK;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AACA;AAAA,QACD;AAAA,MACD;AAEA,YAAM,YAAY,MAAM,SAAS,KAAK;AACtC,cAAQ,MAAM,sCAAsC,SAAS,MAAM,IAAI,SAAS;AAChF,YAAM,IAAI,MAAM,0BAA0B,SAAS,MAAM,EAAE;AAAA,IAC5D;AAEA,QAAI,CAAC,SAAS,MAAM;AACnB,YAAM,IAAI,MAAM,sBAAsB;AAAA,IACvC;AAEA,QAAI,oBAAoB;AACxB,QAAI,qBAAqB;AAEzB,qBAAiB,YAAY,KAAK,eAAe,SAAS,IAAI,GAAG;AAChE,YAAM,YAAY,SAAS,UAAU,aAAa,CAAC;AAEnD,UAAI,WAAW,SAAS,OAAO;AAC9B,mBAAW,QAAQ,UAAU,QAAQ,OAAuB;AAE3D,cAAI,KAAK,YAAY,QAAQ,KAAK,MAAM;AACvC,kBAAM,eAAe,KAAK;AAE1B,gBAAI,uBAAuB;AAE1B,kBAAI,CAAC,oBAAoB;AACxB,sBAAM;AAAA,kBACL,MAAM;AAAA,kBACN,MAAM;AAAA,gBACP;AACA,qCAAqB;AAAA,cACtB;AAEA,oBAAM;AAAA,gBACL,MAAM;AAAA,gBACN,MAAM;AAAA,cACP;AAAA,YACD,OAAO;AAEN,oBAAM;AAAA,gBACL,MAAM;AAAA,gBACN,MAAM;AAAA,cACP;AAAA,YACD;AAAA,UACD,WAES,KAAK,QAAQ,KAAK,KAAK,SAAS,SAAS,GAAG;AACpD,gBAAI,uBAAuB;AAE1B,oBAAM,gBAAgB,KAAK,KAAK,MAAM,wBAAwB;AAC9D,kBAAI,eAAe;AAClB,oBAAI,CAAC,oBAAoB;AACxB,wBAAM;AAAA,oBACL,MAAM;AAAA,oBACN,MAAM;AAAA,kBACP;AACA,uCAAqB;AAAA,gBACtB;AAEA,sBAAM;AAAA,kBACL,MAAM;AAAA,kBACN,MAAM,cAAc,CAAC;AAAA,gBACtB;AAAA,cACD;AAGA,oBAAM,qBAAqB,KAAK,KAAK,QAAQ,yBAAyB,EAAE,EAAE,KAAK;AAC/E,kBAAI,oBAAoB;AACvB,oBAAI,sBAAsB,CAAC,mBAAmB;AAC7C,wBAAM;AAAA,oBACL,MAAM;AAAA,oBACN,MAAM;AAAA,kBACP;AACA,sCAAoB;AAAA,gBACrB;AACA,sBAAM,EAAE,MAAM,QAAQ,MAAM,mBAAmB;AAAA,cAChD;AAAA,YACD,OAAO;AAEN,oBAAM,gBAAgB,KAAK,KAAK,MAAM,wBAAwB;AAC9D,kBAAI,eAAe;AAClB,sBAAM;AAAA,kBACL,MAAM;AAAA,kBACN,MAAM,cAAc,CAAC;AAAA,gBACtB;AAAA,cACD;AAGA,oBAAM,qBAAqB,KAAK,KAAK,QAAQ,yBAAyB,EAAE,EAAE,KAAK;AAC/E,kBAAI,oBAAoB;AACvB,sBAAM,EAAE,MAAM,QAAQ,MAAM,mBAAmB;AAAA,cAChD;AAAA,YACD;AAAA,UACD,WAES,KAAK,QAAQ,CAAC,KAAK,WAAW,CAAC,KAAK,KAAK,SAAS,SAAS,GAAG;AAEtE,iBAAK,sBAAuB,yBAAyB,uBAAwB,CAAC,mBAAmB;AAChG,oBAAM;AAAA,gBACL,MAAM;AAAA,gBACN,MAAM;AAAA,cACP;AACA,kCAAoB;AAAA,YACrB;AAEA,kBAAM,EAAE,MAAM,QAAQ,MAAM,KAAK,KAAK;AAAA,UACvC,WAES,KAAK,cAAc;AAE3B,iBAAK,sBAAuB,yBAAyB,uBAAwB,CAAC,mBAAmB;AAChG,oBAAM;AAAA,gBACL,MAAM;AAAA,gBACN,MAAM;AAAA,cACP;AACA,kCAAoB;AAAA,YACrB;AAEA,kBAAM,mBAAuC;AAAA,cAC5C,MAAM,KAAK,aAAa;AAAA,cACxB,MAAM,KAAK,aAAa;AAAA,YACzB;AAEA,kBAAM;AAAA,cACL,MAAM;AAAA,cACN,MAAM;AAAA,YACP;AAAA,UACD;AAAA,QAED;AAAA,MACD;AAEA,UAAI,SAAS,UAAU,eAAe;AACrC,cAAM,QAAQ,SAAS,SAAS;AAChC,cAAM,YAAuB;AAAA,UAC5B,aAAa,MAAM,oBAAoB;AAAA,UACvC,cAAc,MAAM,wBAAwB;AAAA,QAC7C;AACA,cAAM;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cACL,SACA,cACA,UACA,SAoBE;AACF,QAAI;AACH,UAAI,UAAU;AACd,UAAI;AACJ,YAAM,aAAqG,CAAC;AAG5G,uBAAiB,SAAS,KAAK,cAAc,SAAS,cAAc,UAAU,OAAO,GAAG;AACvF,YAAI,MAAM,SAAS,UAAU,OAAO,MAAM,SAAS,UAAU;AAC5D,qBAAW,MAAM;AAAA,QAClB,WAAW,MAAM,SAAS,WAAW,OAAO,MAAM,SAAS,UAAU;AACpE,kBAAQ,MAAM;AAAA,QACf,WAAW,MAAM,SAAS,eAAe,OAAO,MAAM,SAAS,UAAU;AACxE,gBAAM,WAAW,MAAM;AACvB,qBAAW,KAAK;AAAA,YACf,IAAI,QAAQ,OAAO,WAAW,CAAC;AAAA,YAC/B,MAAM;AAAA,YACN,UAAU;AAAA,cACT,MAAM,SAAS;AAAA,cACf,WAAW,KAAK,UAAU,SAAS,IAAI;AAAA,YACxC;AAAA,UACD,CAAC;AAAA,QACF;AAAA,MAED;AAEA,aAAO;AAAA,QACN;AAAA,QACA;AAAA,QACA,YAAY,WAAW,SAAS,IAAI,aAAa;AAAA,MAClD;AAAA,IACD,SAASA,QAAgB;AAExB,UAAI,KAAK,iBAAiB,iBAAiBA,MAAK,GAAG;AAClD,cAAM,iBAAiB,MAAM,KAAK,iBAAiB;AAAA,UAClD;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK,cAAc,KAAK,IAAI;AAAA,QAC7B;AACA,YAAI,gBAAgB;AACnB,iBAAO;AAAA,QACR;AAAA,MACD;AAGA,YAAMA;AAAA,IACP;AAAA,EACD;AACD;;;AC3rBA,SAAS,gBAAgB,MAAsC;AAC9D,SAAO,OAAO,SAAS,YAAY,SAAS,SAAS,eAAe,QAAQ,cAAc;AAC3F;AAFS;AAIT,SAAS,qBAAqB,MAA2C;AACxE,SAAO,OAAO,SAAS,YAAY,SAAS,QAAQ,UAAU,QAAQ,UAAU;AACjF;AAFS;AAIT,SAAS,YAAY,MAAkC;AACtD,SAAO,OAAO,SAAS,YAAY,SAAS,QAAQ,iBAAiB,QAAQ,kBAAkB;AAChG;AAFS;AAQF,SAAS,8BAA8B,OAAyD;AACtG,QAAM,SAAS,YAAY,OAAO,WAAW,CAAC;AAC9C,QAAM,eAAe,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AACjD,QAAM,UAAU,IAAI,YAAY;AAChC,MAAI,aAAa;AACjB,MAAI,aAA4B;AAChC,MAAI,eAA8B;AAClC,MAAI;AAEJ,SAAO,IAAI,gBAAgB;AAAA,IAC1B,UAAU,OAAO,YAAY;AAC5B,UAAI,MAAM,SAAS,UAAU,MAAM,QAAQ,OAAO,MAAM,SAAS,UAAU;AAC1E,cAAM,QAAqB;AAAA,UAC1B,SAAS,MAAM;AAAA,QAChB;AACA,YAAI,YAAY;AACf,gBAAM,OAAO;AACb,uBAAa;AAAA,QACd;AAEA,cAAM,cAA2B;AAAA,UAChC,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,SAAS;AAAA,UACT;AAAA,UACA,SAAS;AAAA,YACR;AAAA,cACC,OAAO;AAAA,cACP;AAAA,cACA,eAAe;AAAA,cACf,UAAU;AAAA,cACV,cAAc;AAAA,YACf;AAAA,UACD;AAAA,UACA,OAAO;AAAA,QACR;AACA,mBAAW,QAAQ,QAAQ,OAAO,SAAS,KAAK,UAAU,WAAW,CAAC;AAAA;AAAA,CAAM,CAAC;AAAA,MAC9E,WAAW,MAAM,SAAS,sBAAsB,MAAM,QAAQ,OAAO,MAAM,SAAS,UAAU;AAE7F,cAAM,QAAqB;AAAA,UAC1B,SAAS,MAAM;AAAA,UACf,mBAAmB;AAAA,QACpB;AACA,YAAI,YAAY;AACf,gBAAM,OAAO;AACb,uBAAa;AAAA,QACd;AAEA,cAAM,cAA2B;AAAA,UAChC,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,SAAS;AAAA,UACT;AAAA,UACA,SAAS;AAAA,YACR;AAAA,cACC,OAAO;AAAA,cACP;AAAA,cACA,eAAe;AAAA,cACf,UAAU;AAAA,cACV,cAAc;AAAA,YACf;AAAA,UACD;AAAA,UACA,OAAO;AAAA,QACR;AACA,mBAAW,QAAQ,QAAQ,OAAO,SAAS,KAAK,UAAU,WAAW,CAAC;AAAA;AAAA,CAAM,CAAC;AAAA,MAC9E,WAAW,MAAM,SAAS,mBAAmB,MAAM,QAAQ,OAAO,MAAM,SAAS,UAAU;AAE1F,cAAM,QAAqB;AAAA,UAC1B,WAAW,MAAM;AAAA,UACjB,mBAAmB;AAAA,QACpB;AAEA,cAAM,cAA2B;AAAA,UAChC,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,SAAS;AAAA,UACT;AAAA,UACA,SAAS;AAAA,YACR;AAAA,cACC,OAAO;AAAA,cACP;AAAA,cACA,eAAe;AAAA,cACf,UAAU;AAAA,cACV,cAAc;AAAA,YACf;AAAA,UACD;AAAA,UACA,OAAO;AAAA,QACR;AACA,mBAAW,QAAQ,QAAQ,OAAO,SAAS,KAAK,UAAU,WAAW,CAAC;AAAA;AAAA,CAAM,CAAC;AAAA,MAC9E,WAAW,MAAM,SAAS,eAAe,gBAAgB,MAAM,IAAI,GAAG;AAErE,cAAM,QAAqB;AAAA,UAC1B,WAAW,MAAM,KAAK;AAAA,UACtB,mBAAmB;AAAA,QACpB;AAEA,cAAM,cAA2B;AAAA,UAChC,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,SAAS;AAAA,UACT;AAAA,UACA,SAAS;AAAA,YACR;AAAA,cACC,OAAO;AAAA,cACP;AAAA,cACA,eAAe;AAAA,cACf,UAAU;AAAA,cACV,cAAc;AAAA,YACf;AAAA,UACD;AAAA,UACA,OAAO;AAAA,QACR;AACA,mBAAW,QAAQ,QAAQ,OAAO,SAAS,KAAK,UAAU,WAAW,CAAC;AAAA;AAAA,CAAM,CAAC;AAAA,MAC9E,WAAW,MAAM,SAAS,eAAe,qBAAqB,MAAM,IAAI,GAAG;AAC1E,cAAM,WAAW,MAAM;AACvB,cAAM,WAAW,SAAS;AAC1B,cAAM,eAAe,SAAS;AAE9B,YAAI,cAAc;AACjB,yBAAe;AACf,uBAAa,QAAQ,OAAO,WAAW,CAAC;AAAA,QACzC;AAEA,cAAM,QAAqB;AAAA,UAC1B,YAAY;AAAA,YACX;AAAA,cACC,OAAO;AAAA,cACP,IAAI,cAAc;AAAA,cAClB,MAAM;AAAA,cACN,UAAU;AAAA,gBACT,MAAM,gBAAgB;AAAA,gBACtB,WAAW,KAAK,UAAU,QAAQ;AAAA,cACnC;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAEA,YAAI,YAAY;AACf,gBAAM,OAAO;AACb,gBAAM,UAAU;AAChB,uBAAa;AAAA,QACd;AAEA,cAAM,cAA2B;AAAA,UAChC,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,SAAS;AAAA,UACT;AAAA,UACA,SAAS;AAAA,YACR;AAAA,cACC,OAAO;AAAA,cACP;AAAA,cACA,eAAe;AAAA,cACf,UAAU;AAAA,cACV,cAAc;AAAA,YACf;AAAA,UACD;AAAA,UACA,OAAO;AAAA,QACR;AACA,mBAAW,QAAQ,QAAQ,OAAO,SAAS,KAAK,UAAU,WAAW,CAAC;AAAA;AAAA,CAAM,CAAC;AAAA,MAC9E,WAAW,MAAM,SAAS,WAAW,YAAY,MAAM,IAAI,GAAG;AAE7D,oBAAY,MAAM;AAAA,MACnB;AAAA,IACD;AAAA,IACA,MAAM,YAAY;AAEjB,YAAM,eAAe,aAAa,eAAe;AACjD,YAAM,aAA+B;AAAA,QACpC,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA,QACT;AAAA,QACA,SAAS,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,GAAG,eAAe,aAAa,CAAC;AAAA,MAC/D;AAGA,UAAI,WAAW;AACd,mBAAW,QAAQ;AAAA,UAClB,eAAe,UAAU;AAAA,UACzB,mBAAmB,UAAU;AAAA,UAC7B,cAAc,UAAU,cAAc,UAAU;AAAA,QACjD;AAAA,MACD;AAEA,iBAAW,QAAQ,QAAQ,OAAO,SAAS,KAAK,UAAU,UAAU,CAAC;AAAA;AAAA,CAAM,CAAC;AAC5E,iBAAW,QAAQ,QAAQ,OAAO,kBAAkB,CAAC;AAAA,IACtD;AAAA,EACD,CAAC;AACF;AA7LgB;;;ACjET,IAAM,cAAc,IAAIC,MAAwB;AAGvD,YAAY,IAAI,WAAW,OAAO,MAAM;AACvC,QAAM,YAAY,eAAe,EAAE,IAAI,CAAC,aAAa;AAAA,IACpD,IAAI;AAAA,IACJ,QAAQ;AAAA,IACR,SAAS,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AAAA,IACrC,UAAU;AAAA,EACX,EAAE;AAEF,SAAO,EAAE,KAAK;AAAA,IACb,QAAQ;AAAA,IACR,MAAM;AAAA,EACP,CAAC;AACF,CAAC;AAGD,YAAY,KAAK,qBAAqB,OAAO,MAAM;AAClD,MAAI;AACH,YAAQ,IAAI,mCAAmC;AAC/C,UAAM,OAAO,MAAM,EAAE,IAAI,KAA4B;AACrD,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,WAAW,KAAK,YAAY,CAAC;AAEnC,UAAM,SAAS,KAAK,WAAW;AAG/B,UAAM,wBAAwB,EAAE,IAAI,yBAAyB;AAC7D,QAAI,mBAAmB;AACvB,QAAI,iBAAiB,KAAK,mBAAmB;AAG7C,UAAM,oBAAoB;AAAA,MACzB,YAAY,KAAK;AAAA,MACjB,aAAa,KAAK;AAAA,MAClB,OAAO,KAAK;AAAA,MACZ,MAAM,KAAK;AAAA,MACX,kBAAkB,KAAK;AAAA,MACvB,mBAAmB,KAAK;AAAA,MACxB,MAAM,KAAK;AAAA,MACX,iBAAiB,KAAK;AAAA,IACvB;AAGA,UAAM,mBACL,KAAK,oBAAoB,KAAK,YAAY,oBAAoB,KAAK,cAAc;AAClF,QAAI,kBAAkB;AACrB,yBAAmB;AACnB,YAAM,eAAe,MAAM,SAAS,OAAO;AAC3C,cAAQ,kBAAkB;AAAA,QACzB,KAAK;AACJ,2BAAiB;AACjB;AAAA,QACD,KAAK;AACJ,2BAAiB,eAAe,QAAQ;AACxC;AAAA,QACD,KAAK;AACJ,2BAAiB,eAAe,QAAQ;AACxC;AAAA,QACD,KAAK;AACJ,2BAAiB;AACjB,6BAAmB;AACnB;AAAA,MACF;AAAA,IACD;AAEA,UAAM,QAAQ,KAAK;AACnB,UAAM,cAAc,KAAK;AAEzB,YAAQ,IAAI,wBAAwB;AAAA,MACnC;AAAA,MACA,cAAc,SAAS;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAC;AAED,QAAI,CAAC,SAAS,QAAQ;AACrB,aAAO,EAAE,KAAK,EAAE,OAAO,+BAA+B,GAAG,GAAG;AAAA,IAC7D;AAGA,QAAI,EAAE,SAAS,kBAAkB;AAChC,aAAO,EAAE;AAAA,QACR;AAAA,UACC,OAAO,UAAU,KAAK,kCAAkC,eAAe,EAAE,KAAK,IAAI,CAAC;AAAA,QACpF;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAGA,UAAM,YAAY,SAAS,KAAK,CAAC,QAAQ;AACxC,UAAI,MAAM,QAAQ,IAAI,OAAO,GAAG;AAC/B,eAAO,IAAI,QAAQ,KAAK,CAAC,YAAY,QAAQ,SAAS,WAAW;AAAA,MAClE;AACA,aAAO;AAAA,IACR,CAAC;AAED,QAAI,aAAa,CAAC,gBAAgB,KAAK,EAAE,gBAAgB;AACxD,aAAO,EAAE;AAAA,QACR;AAAA,UACC,OAAO,UAAU,KAAK;AAAA,QACvB;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAGA,QAAI,eAAe;AACnB,UAAM,gBAAgB,SAAS,OAAO,CAAC,QAAQ;AAC9C,UAAI,IAAI,SAAS,UAAU;AAE1B,YAAI,OAAO,IAAI,YAAY,UAAU;AACpC,yBAAe,IAAI;AAAA,QACpB,WAAW,MAAM,QAAQ,IAAI,OAAO,GAAG;AAEtC,gBAAM,cAAc,IAAI,QACtB,OAAO,CAAC,SAAS,KAAK,SAAS,MAAM,EACrC,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE,EAC7B,KAAK,GAAG;AACV,yBAAe;AAAA,QAChB;AACA,eAAO;AAAA,MACR;AACA,aAAO;AAAA,IACR,CAAC;AAGD,UAAM,cAAc,IAAI,YAAY,EAAE,GAAG;AACzC,UAAM,eAAe,IAAI,gBAAgB,EAAE,KAAK,WAAW;AAG3D,QAAI;AACH,YAAM,YAAY,eAAe;AACjC,cAAQ,IAAI,2BAA2B;AAAA,IACxC,SAAS,WAAoB;AAC5B,YAAM,eAAe,qBAAqB,QAAQ,UAAU,UAAU,OAAO,SAAS;AACtF,cAAQ,MAAM,0BAA0B,YAAY;AACpD,aAAO,EAAE,KAAK,EAAE,OAAO,4BAA4B,aAAa,GAAG,GAAG;AAAA,IACvE;AAEA,QAAI,QAAQ;AAEX,YAAM,EAAE,UAAU,SAAS,IAAI,IAAI,gBAAgB;AACnD,YAAM,SAAS,SAAS,UAAU;AAClC,YAAM,oBAAoB,8BAA8B,KAAK;AAC7D,YAAM,eAAe,SAAS,YAAY,iBAAiB;AAG3D,OAAC,YAAY;AACZ,YAAI;AACH,kBAAQ,IAAI,4BAA4B;AACxC,gBAAM,eAAe,aAAa,cAAc,OAAO,cAAc,eAAe;AAAA,YACnF;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,GAAG;AAAA,UACJ,CAAC;AAED,2BAAiB,SAAS,cAAc;AACvC,kBAAM,OAAO,MAAM,KAAK;AAAA,UACzB;AACA,kBAAQ,IAAI,+BAA+B;AAC3C,gBAAM,OAAO,MAAM;AAAA,QACpB,SAAS,aAAsB;AAC9B,gBAAM,eAAe,uBAAuB,QAAQ,YAAY,UAAU,OAAO,WAAW;AAC5F,kBAAQ,MAAM,iBAAiB,YAAY;AAE3C,gBAAM,OAAO,MAAM;AAAA,YAClB,MAAM;AAAA,YACN,MAAM,UAAU,YAAY;AAAA,UAC7B,CAAC;AACD,gBAAM,OAAO,MAAM;AAAA,QACpB;AAAA,MACD,GAAG;AAGH,cAAQ,IAAI,8BAA8B;AAC1C,aAAO,IAAI,SAAS,cAAc;AAAA,QACjC,SAAS;AAAA,UACR,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,UACjB,YAAY;AAAA,UACZ,+BAA+B;AAAA,UAC/B,gCAAgC;AAAA,UAChC,gCAAgC;AAAA,QACjC;AAAA,MACD,CAAC;AAAA,IACF,OAAO;AAEN,UAAI;AACH,gBAAQ,IAAI,mCAAmC;AAC/C,cAAM,aAAa,MAAM,aAAa,cAAc,OAAO,cAAc,eAAe;AAAA,UACvF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,GAAG;AAAA,QACJ,CAAC;AAED,cAAM,WAAmC;AAAA,UACxC,IAAI,YAAY,OAAO,WAAW,CAAC;AAAA,UACnC,QAAQ;AAAA,UACR,SAAS,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AAAA,UACrC;AAAA,UACA,SAAS;AAAA,YACR;AAAA,cACC,OAAO;AAAA,cACP,SAAS;AAAA,gBACR,MAAM;AAAA,gBACN,SAAS,WAAW;AAAA,gBACpB,YAAY,WAAW;AAAA,cACxB;AAAA,cACA,eAAe,WAAW,cAAc,WAAW,WAAW,SAAS,IAAI,eAAe;AAAA,YAC3F;AAAA,UACD;AAAA,QACD;AAGA,YAAI,WAAW,OAAO;AACrB,mBAAS,QAAQ;AAAA,YAChB,eAAe,WAAW,MAAM;AAAA,YAChC,mBAAmB,WAAW,MAAM;AAAA,YACpC,cAAc,WAAW,MAAM,cAAc,WAAW,MAAM;AAAA,UAC/D;AAAA,QACD;AAEA,gBAAQ,IAAI,qCAAqC;AACjD,eAAO,EAAE,KAAK,QAAQ;AAAA,MACvB,SAAS,iBAA0B;AAClC,cAAM,eAAe,2BAA2B,QAAQ,gBAAgB,UAAU,OAAO,eAAe;AACxG,gBAAQ,MAAM,qBAAqB,YAAY;AAC/C,eAAO,EAAE,KAAK,EAAE,OAAO,aAAa,GAAG,GAAG;AAAA,MAC3C;AAAA,IACD;AAAA,EACD,SAAS,GAAY;AACpB,UAAM,eAAe,aAAa,QAAQ,EAAE,UAAU,OAAO,CAAC;AAC9D,YAAQ,MAAM,oBAAoB,CAAC;AACnC,WAAO,EAAE,KAAK,EAAE,OAAO,aAAa,GAAG,GAAG;AAAA,EAC3C;AACD,CAAC;;;ACzPM,IAAM,aAAa,IAAIC,MAAwB;AAGtD,WAAW,IAAI,UAAU,OAAO,MAAM;AACrC,MAAI;AACH,UAAM,cAAc,IAAI,YAAY,EAAE,GAAG;AACzC,UAAM,YAAY,MAAM,YAAY,mBAAmB;AAGvD,UAAM,gBAAgB;AAAA,MACrB,QAAQ;AAAA,MACR,QAAQ,UAAU;AAAA,MAClB,WAAW,UAAU;AAAA,MACrB,YAAY,UAAU;AAAA,MACtB,2BAA2B,UAAU;AAAA,MACrC,YAAY,UAAU;AAAA,MACtB,SAAS,UAAU;AAAA;AAAA,IAEpB;AAEA,WAAO,EAAE,KAAK,aAAa;AAAA,EAC5B,SAAS,GAAY;AACpB,UAAM,eAAe,aAAa,QAAQ,EAAE,UAAU,OAAO,CAAC;AAC9D,WAAO,EAAE;AAAA,MACR;AAAA,QACC,QAAQ;AAAA,QACR,SAAS;AAAA,MACV;AAAA,MACA;AAAA,IACD;AAAA,EACD;AACD,CAAC;AAGD,WAAW,KAAK,eAAe,OAAO,MAAM;AAC3C,MAAI;AACH,YAAQ,IAAI,4BAA4B;AACxC,UAAM,cAAc,IAAI,YAAY,EAAE,GAAG;AAGzC,UAAM,YAAY,eAAe;AACjC,YAAQ,IAAI,mBAAmB;AAE/B,WAAO,EAAE,KAAK;AAAA,MACb,QAAQ;AAAA,MACR,SAAS;AAAA,IACV,CAAC;AAAA,EACF,SAAS,GAAY;AACpB,UAAM,eAAe,aAAa,QAAQ,EAAE,UAAU,OAAO,CAAC;AAC9D,YAAQ,MAAM,qBAAqB,CAAC;AACpC,WAAO,EAAE;AAAA,MACR;AAAA,QACC,QAAQ;AAAA,QACR,SAAS;AAAA;AAAA,MAEV;AAAA,MACA;AAAA,IACD;AAAA,EACD;AACD,CAAC;AAGD,WAAW,KAAK,SAAS,OAAO,MAAM;AACrC,MAAI;AACH,YAAQ,IAAI,sBAAsB;AAClC,UAAM,cAAc,IAAI,YAAY,EAAE,GAAG;AACzC,UAAM,eAAe,IAAI,gBAAgB,EAAE,KAAK,WAAW;AAG3D,UAAM,YAAY,eAAe;AACjC,YAAQ,IAAI,kBAAkB;AAG9B,UAAM,YAAY,MAAM,aAAa,kBAAkB;AACvD,YAAQ,IAAI,+BAA+B;AAE3C,WAAO,EAAE,KAAK;AAAA,MACb,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,mBAAmB,CAAC,CAAC;AAAA;AAAA,IAEtB,CAAC;AAAA,EACF,SAAS,GAAY;AACpB,UAAM,eAAe,aAAa,QAAQ,EAAE,UAAU,OAAO,CAAC;AAC9D,YAAQ,MAAM,wBAAwB,CAAC;AACvC,WAAO,EAAE;AAAA,MACR;AAAA,QACC,QAAQ;AAAA,QACR,SAAS;AAAA;AAAA,MAEV;AAAA,MACA;AAAA,IACD;AAAA,EACD;AACD,CAAC;;;AC/FM,IAAM,mBAAyD,8BAAO,GAAG,SAAS;AAExF,QAAM,kBAAkB,CAAC,KAAK,SAAS;AACvC,MAAI,gBAAgB,KAAK,CAAC,aAAa,EAAE,IAAI,SAAS,QAAQ,GAAG;AAChE,UAAM,KAAK;AACX;AAAA,EACD;AAGA,MAAI,EAAE,IAAI,gBAAgB;AACzB,UAAM,aAAa,EAAE,IAAI,OAAO,eAAe;AAE/C,QAAI,CAAC,YAAY;AAChB,aAAO,EAAE;AAAA,QACR;AAAA,UACC,OAAO;AAAA,YACN,SAAS;AAAA,YACT,MAAM;AAAA,YACN,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAGA,UAAM,QAAQ,WAAW,MAAM,iBAAiB;AAChD,QAAI,CAAC,OAAO;AACX,aAAO,EAAE;AAAA,QACR;AAAA,UACC,OAAO;AAAA,YACN,SAAS;AAAA,YACT,MAAM;AAAA,YACN,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAEA,UAAM,cAAc,MAAM,CAAC;AAC3B,QAAI,gBAAgB,EAAE,IAAI,gBAAgB;AACzC,aAAO,EAAE;AAAA,QACR;AAAA,UACC,OAAO;AAAA,YACN,SAAS;AAAA,YACT,MAAM;AAAA,YACN,MAAM;AAAA,UACP;AAAA,QACD;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAAA,EAID;AAEA,QAAM,KAAK;AACZ,GA3DsE;;;ACI/D,IAAM,oBAAoB,8BAAO,GAA+B,SAAe;AACrF,QAAM,SAAS,EAAE,IAAI;AACrB,QAAM,OAAO,EAAE,IAAI;AACnB,QAAM,YAAY,KAAK,IAAI;AAC3B,QAAM,aAAY,oBAAI,KAAK,GAAE,YAAY;AAGzC,MAAI,UAAU;AACd,MAAI,CAAC,QAAQ,OAAO,OAAO,EAAE,SAAS,MAAM,GAAG;AAC9C,QAAI;AAEH,YAAM,YAAY,EAAE,IAAI,IAAI,MAAM;AAClC,YAAM,OAAO,MAAM,UAAU,KAAK;AAGlC,YAAM,gBAAgB,KAAK,SAAS,MAAM,KAAK,UAAU,GAAG,GAAG,IAAI,QAAQ;AAE3E,YAAM,aAAa,cAAc,QAAQ,iDAAiD,aAAa;AACvG,gBAAU,YAAY,UAAU;AAAA,IACjC,QAAQ;AACP,gBAAU;AAAA,IACX;AAAA,EACD;AAEA,UAAQ,IAAI,IAAI,SAAS,KAAK,MAAM,IAAI,IAAI,GAAG,OAAO,oBAAoB;AAE1E,QAAM,KAAK;AAEX,QAAM,WAAW,KAAK,IAAI,IAAI;AAC9B,QAAM,SAAS,EAAE,IAAI;AACrB,QAAM,gBAAe,oBAAI,KAAK,GAAE,YAAY;AAE5C,UAAQ,IAAI,IAAI,YAAY,KAAK,MAAM,IAAI,IAAI,4BAA4B,MAAM,KAAK,QAAQ,KAAK;AACpG,GAjCiC;;;ACWjC,IAAM,MAAM,IAAIC,MAAwB;AAGxC,IAAI,IAAI,KAAK,iBAAiB;AAG9B,IAAI,IAAI,KAAK,OAAO,GAAG,SAAS;AAE/B,IAAE,OAAO,+BAA+B,GAAG;AAC3C,IAAE,OAAO,gCAAgC,oBAAoB;AAC7D,IAAE,OAAO,gCAAgC,6BAA6B;AAGtE,MAAI,EAAE,IAAI,WAAW,WAAW;AAC/B,MAAE,OAAO,GAAG;AACZ,WAAO,EAAE,KAAK,IAAI;AAAA,EACnB;AAEA,QAAM,KAAK;AACZ,CAAC;AAGD,IAAI,IAAI,SAAS,gBAAgB;AAGjC,IAAI,MAAM,OAAO,WAAW;AAC5B,IAAI,MAAM,aAAa,UAAU;AAGjC,IAAI,MAAM,OAAO,UAAU;AAG3B,IAAI,IAAI,KAAK,CAAC,MAAM;AACnB,QAAM,eAAe,CAAC,CAAC,EAAE,IAAI;AAE7B,SAAO,EAAE,KAAK;AAAA,IACb,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,gBAAgB;AAAA,MACf,UAAU;AAAA,MACV,MAAM,eAAe,yCAAyC;AAAA,IAC/D;AAAA,IACA,WAAW;AAAA,MACV,kBAAkB;AAAA,MAClB,QAAQ;AAAA,MACR,OAAO;AAAA,QACN,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,WAAW;AAAA,MACZ;AAAA,IACD;AAAA,IACA,eAAe;AAAA,EAChB,CAAC;AACF,CAAC;AAGD,IAAI,IAAI,WAAW,CAAC,MAAM;AACzB,SAAO,EAAE,KAAK,EAAE,QAAQ,MAAM,YAAW,oBAAI,KAAK,GAAE,YAAY,EAAE,CAAC;AACpE,CAAC;AAED,IAAO,cAAQ;;;ACjFf,IAAM,YAAwB,8BAAO,SAASC,MAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAASA,IAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAG;AACX,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAASC,MAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAASA,IAAG;AAAA,EAC7C,SAAS,GAAQ;AAChB,UAAMC,SAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAKA,QAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACAC,MACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAASA,MAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACAA,MACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAASA,MAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAM,gCAA8D;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EArBD,OAYoE;AAAA;AAAA;AAAA,EAC1D;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,kCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAEA,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACAC,MACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAASA,MAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAASA,MAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAYA,MAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAASA,MAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,wBACxE,SACAA,MACA,QACI;AACJ,WAAK,MAAMA;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B,GAXyE;AAAA,IAazE,cAA0B,wBAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD,GAT0B;AAAA,IAW1B,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": ["PerformanceMark", "clear", "count", "<PERSON><PERSON><PERSON><PERSON>", "createTask", "debug", "dir", "dirxml", "error", "group", "groupCollapsed", "groupEnd", "info", "log", "profile", "profileEnd", "table", "time", "timeEnd", "timeLog", "timeStamp", "trace", "warn", "hrtime", "dir", "env", "count", "cwd", "hrtime", "assert", "context", "raw", "context", "app", "env", "context", "context", "Node", "Node", "<PERSON><PERSON>", "env", "timeUntilExpiry", "env", "env", "error", "env", "error", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "env", "env", "error", "env", "env"]}