# Roo/Cline 配置指南

## 🚀 一键生成API密钥

使用PowerShell脚本自动生成和配置API密钥：

```powershell
# 生成新的API密钥
.\setup-key.ps1

# 显示当前配置
.\setup-key.ps1 -Show

# 使用自定义密钥
.\setup-key.ps1 -CustomKey sk-your-custom-key-here

# 显示帮助
.\setup-key.ps1 -Help
```

## 📱 Cline (VS Code扩展) 配置

### 1. 安装Cline扩展
- 打开VS Code
- 搜索并安装"Cline"扩展

### 2. 配置Cline
打开Cline设置，配置以下参数：

- **API Provider**: `OpenAI`
- **Base URL**: `http://127.0.0.1:8787/v1`
- **API Key**: `sk-LzABlqDB7y8z83YUkBJMSBrNqdzZe87IYQncw52KXMCStC7o` (使用脚本生成的密钥)
- **Model**: `gemini-2.5-flash`

### 3. 测试连接
在Cline中发送"你好"测试连接是否正常。

## 🤖 Roo 配置

### 环境变量设置
```bash
# Windows (PowerShell)
$env:OPENAI_API_BASE="http://127.0.0.1:8787/v1"
$env:OPENAI_API_KEY="sk-LzABlqDB7y8z83YUkBJMSBrNqdzZe87IYQncw52KXMCStC7o"
$env:MODEL="gemini-2.5-flash"

# Linux/macOS
export OPENAI_API_BASE="http://127.0.0.1:8787/v1"
export OPENAI_API_KEY="sk-LzABlqDB7y8z83YUkBJMSBrNqdzZe87IYQncw52KXMCStC7o"
export MODEL="gemini-2.5-flash"
```

### 配置文件设置
如果Roo支持配置文件，创建`.roo.config`：

```json
{
  "api_base": "http://127.0.0.1:8787/v1",
  "api_key": "sk-LzABlqDB7y8z83YUkBJMSBrNqdzZe87IYQncw52KXMCStC7o",
  "model": "gemini-2.5-flash"
}
```

## 🌐 通用OpenAI客户端配置

任何支持OpenAI API的客户端都可以使用以下配置：

- **Base URL**: `http://127.0.0.1:8787/v1`
- **API Key**: `sk-LzABlqDB7y8z83YUkBJMSBrNqdzZe87IYQncw52KXMCStC7o`
- **Model**: `gemini-2.5-flash`

## 🧪 测试连接

### 使用curl测试
```bash
curl -X POST http://127.0.0.1:8787/v1/chat/completions \
  -H "Authorization: Bearer sk-LzABlqDB7y8z83YUkBJMSBrNqdzZe87IYQncw52KXMCStC7o" \
  -H "Content-Type: application/json" \
  -d '{"model":"gemini-2.5-flash","messages":[{"role":"user","content":"你好"}]}'
```

### 使用Node.js测试
```bash
node simple-test.js
```

## 📋 启动步骤

1. **启动Gemini服务器**
   ```bash
   npm run dev
   ```

2. **配置客户端**
   - 使用上述配置参数
   - 确保Base URL指向本地服务器

3. **测试连接**
   - 发送简单消息测试
   - 确认响应正常

## ⚠️ 注意事项

### 模型选择
- ✅ **推荐使用**: `gemini-2.5-flash` (快速、稳定)
- ❌ **暂不推荐**: `gemini-2.5-pro` (存在响应问题)

### 服务器要求
- 确保Gemini CLI OpenAI服务器正在运行
- 服务器地址：`http://127.0.0.1:8787`
- 需要有效的OAuth凭据

### API密钥安全
- API密钥仅用于本地认证
- 不要在公共场所分享密钥
- 可以随时重新生成新密钥

## 🔧 故障排除

### 连接失败
1. 检查服务器是否运行：`npm run dev`
2. 验证API密钥是否正确
3. 确认Base URL格式正确

### 认证错误
1. 重新生成API密钥：`.\setup-key.ps1`
2. 检查OAuth凭据是否有效
3. 运行OAuth更新：`npm run update-oauth`

### 模型错误
1. 确认使用`gemini-2.5-flash`模型
2. 避免使用`gemini-2.5-pro`（存在问题）
3. 检查模型名称拼写

## 📞 获取帮助

如果遇到问题：
1. 查看服务器日志
2. 运行测试脚本验证配置
3. 检查OAuth凭据状态

---

*配置完成后，你就可以在Roo或Cline中使用Gemini模型了！* 🎉
