# 🚀 Gemini CLI OpenAI 终极启动指南

## 📋 概述

`start.js` 是一个智能启动脚本，能够：
- 🔍 自动检测OAuth Token过期状态
- 🔄 过期时自动启动Gemini CLI重新认证
- 📝 自动更新项目OAuth凭据
- 🔑 自动生成API密钥
- 🌐 启动开发服务器
- 📊 后台监控OAuth状态

## 🎯 快速开始

### 基本启动
```bash
# 使用npm脚本（推荐）
npm start

# 或直接运行
node start.js
```

### 高级选项
```bash
# 强制重新认证（即使Token未过期）
npm run start:force
node start.js --force

# 跳过OAuth检查，直接启动
npm run start:skip-oauth
node start.js --skip-oauth

# 不启动OAuth监控
npm run start:no-monitor
node start.js --no-monitor

# 显示详细日志
node start.js --verbose

# 显示帮助
node start.js --help
```

## 🔄 自动化流程

### 1. OAuth状态检测
脚本会自动检查 `C:\Users\<USER>\.gemini\oauth_creds.json` 中的Token状态：

- ✅ **有效** (>30分钟): 直接启动服务器
- ⚠️ **警告** (5-30分钟): 显示警告但继续启动
- 🚨 **危险** (<5分钟): 自动重新认证
- ❌ **过期** (<=0分钟): 自动重新认证
- 📁 **缺失**: 自动重新认证

### 2. 自动认证流程
当需要重新认证时：
1. 启动 `gemini` 命令
2. 等待用户在浏览器中完成Google登录
3. 自动更新项目OAuth凭据
4. 清理Unicode字符避免ByteString错误

### 3. API密钥管理
- 自动运行 `setup-key.ps1` 生成OpenAI格式API密钥
- 更新 `.dev.vars` 配置文件
- 提供Roo/Cline配置信息

### 4. 服务器启动
- 检查并终止占用8787端口的进程
- 启动 `npm run dev`
- 验证服务器响应
- 显示访问地址

### 5. 状态监控
- 每5分钟检查OAuth Token状态
- Token即将过期时显示警告
- 后台运行，不影响服务器性能

## 📊 状态指示器

脚本使用彩色日志显示不同状态：

- ✅ **绿色 [OK]**: 操作成功
- ⚠️ **黄色 [WARN]**: 警告信息
- ❌ **红色 [ERROR]**: 错误信息
- ℹ️ **青色 [INFO]**: 一般信息
- 🔍 **灰色 [DEBUG]**: 调试信息

## 🛠️ 故障排除

### OAuth认证失败
```bash
# 检查Gemini CLI是否安装
npm list -g @google/gemini-cli

# 如果未安装，执行安装
npm install -g @google/gemini-cli

# 手动运行认证
gemini
```

### 服务器启动失败
```bash
# 检查端口占用
netstat -ano | findstr :8787

# 手动终止进程
taskkill /F /PID [进程ID]

# 重新启动
npm start
```

### API密钥问题
```bash
# 重新生成API密钥
powershell -ExecutionPolicy Bypass -File setup-key.ps1

# 查看当前配置
powershell -ExecutionPolicy Bypass -File setup-key.ps1 -Show
```

## 📱 Roo/Cline配置

启动成功后，使用以下配置：

### Cline (VS Code扩展)
- **API Provider**: OpenAI
- **Base URL**: `http://127.0.0.1:8787/v1`
- **API Key**: 运行 `powershell -ExecutionPolicy Bypass -File setup-key.ps1 -Show` 查看
- **Model**: `gemini-2.5-flash`

### Roo
```bash
# 环境变量
export OPENAI_API_BASE="http://127.0.0.1:8787/v1"
export OPENAI_API_KEY="sk-your-generated-key"
export MODEL="gemini-2.5-flash"
```

## 🧪 测试验证

### 快速测试
```bash
# 测试API响应
node simple-test.js

# 测试两个模型
node test-models.js

# 测试流式响应
node test-stream.js
```

### 手动测试
```bash
curl -X POST http://127.0.0.1:8787/v1/chat/completions \
  -H "Authorization: Bearer [your-api-key]" \
  -H "Content-Type: application/json" \
  -d '{"model":"gemini-2.5-flash","messages":[{"role":"user","content":"你好"}]}'
```

## ⚙️ 配置文件

### 主要文件
- `start.js` - 终极启动脚本
- `setup-key.ps1` - API密钥生成脚本
- `update_oauth_credentials.js` - OAuth凭据更新脚本
- `.dev.vars` - 环境变量配置

### 凭据位置
- **Windows标准路径**: `C:\Users\<USER>\.gemini\oauth_creds.json`
- **项目配置**: `./.dev.vars`
- **备份目录**: `./oauth_backups/`

## 🔧 高级功能

### 自定义监控间隔
修改 `start.js` 中的监控间隔：
```javascript
// 默认5分钟 (300000ms)
setInterval(async () => {
    // 监控逻辑
}, 300000);
```

### 自定义端口检查
修改端口检查逻辑以支持其他端口：
```javascript
// 检查其他端口
const portInUse = await checkPort(8788);
```

## 📞 获取帮助

### 常用命令
```bash
# 显示帮助
node start.js --help

# 查看当前OAuth状态
node start.js --skip-oauth --no-monitor

# 强制重新认证
node start.js --force

# 详细日志模式
node start.js --verbose
```

### 日志文件
脚本输出包含时间戳，便于问题追踪：
```
✅ [09:07:06] 服务器启动成功！
ℹ️  [09:07:06] 本地地址: http://127.0.0.1:8787
```

## 🎉 完成

使用 `npm start` 即可享受全自动的Gemini CLI OpenAI服务启动体验！

脚本会智能处理所有复杂的认证和配置流程，让你专注于使用AI功能。

---

*如有问题，请检查日志输出或运行 `node start.js --help` 获取更多信息。*
