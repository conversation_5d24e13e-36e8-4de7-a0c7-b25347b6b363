#!/usr/bin/env node

/**
 * API密钥检查和测试工具
 */

const http = require('http');
const fs = require('fs');

class ApiChecker {
    constructor() {
        this.baseUrl = 'http://127.0.0.1:8787';
        this.devVarsPath = './.dev.vars';
    }

    // 获取当前API密钥
    getCurrentApiKey() {
        try {
            if (fs.existsSync(this.devVarsPath)) {
                const content = fs.readFileSync(this.devVarsPath, 'utf8');
                const lines = content.split('\n');
                const apiKeyLine = lines.find(line => line.startsWith('OPENAI_API_KEY='));
                
                if (apiKeyLine) {
                    return apiKeyLine.split('=')[1].trim();
                }
            }
            return null;
        } catch (error) {
            return null;
        }
    }

    // 测试API密钥
    async testApiKey(apiKey) {
        return new Promise((resolve) => {
            const payload = JSON.stringify({
                model: 'gemini-2.5-flash',
                messages: [{ role: 'user', content: 'Hello' }],
                max_tokens: 10
            });

            const options = {
                hostname: '127.0.0.1',
                port: 8787,
                path: '/v1/chat/completions',
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(payload)
                }
            };

            const req = http.request(options, (res) => {
                let data = '';
                
                res.on('data', (chunk) => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const response = JSON.parse(data);
                        
                        if (res.statusCode === 200) {
                            resolve({
                                success: true,
                                statusCode: res.statusCode,
                                response: response
                            });
                        } else {
                            resolve({
                                success: false,
                                statusCode: res.statusCode,
                                error: response.error || response
                            });
                        }
                    } catch (error) {
                        resolve({
                            success: false,
                            statusCode: res.statusCode,
                            error: { message: 'Failed to parse response', raw: data }
                        });
                    }
                });
            });

            req.on('error', (error) => {
                resolve({
                    success: false,
                    error: { message: `Connection failed: ${error.message}` }
                });
            });

            req.setTimeout(10000, () => {
                req.destroy();
                resolve({
                    success: false,
                    error: { message: 'Request timeout' }
                });
            });

            req.write(payload);
            req.end();
        });
    }

    // 检查服务器状态
    async checkServerStatus() {
        return new Promise((resolve) => {
            const options = {
                hostname: '127.0.0.1',
                port: 8787,
                path: '/v1/models',
                method: 'GET',
                timeout: 5000
            };

            const req = http.request(options, (res) => {
                resolve({
                    running: true,
                    statusCode: res.statusCode
                });
            });

            req.on('error', () => {
                resolve({
                    running: false,
                    statusCode: null
                });
            });

            req.setTimeout(5000, () => {
                req.destroy();
                resolve({
                    running: false,
                    statusCode: null
                });
            });

            req.end();
        });
    }

    // 主检查流程
    async run() {
        console.log('🔍 API密钥检查和测试工具');
        console.log('=' .repeat(40));

        // 1. 检查服务器状态
        console.log('\n1. 检查服务器状态...');
        const serverStatus = await this.checkServerStatus();
        
        if (!serverStatus.running) {
            console.log('❌ 服务器未运行');
            console.log('💡 请先运行: npm start 或 node start.js');
            return;
        }
        
        console.log('✅ 服务器正在运行');

        // 2. 获取当前API密钥
        console.log('\n2. 获取当前API密钥...');
        const currentApiKey = this.getCurrentApiKey();
        
        if (!currentApiKey) {
            console.log('❌ 未找到API密钥');
            console.log('💡 请运行: powershell -ExecutionPolicy Bypass -File setup-key.ps1');
            return;
        }
        
        console.log(`✅ 找到API密钥: ${currentApiKey}`);

        // 3. 测试API密钥
        console.log('\n3. 测试API密钥...');
        const testResult = await this.testApiKey(currentApiKey);
        
        if (testResult.success) {
            console.log('✅ API密钥有效！');
            console.log(`📊 响应状态: ${testResult.statusCode}`);
            
            if (testResult.response.choices && testResult.response.choices[0]) {
                const content = testResult.response.choices[0].message.content;
                console.log(`💬 测试响应: ${content || '(空响应)'}`);
            }
            
            if (testResult.response.usage) {
                console.log(`🔢 Token使用: ${testResult.response.usage.total_tokens}`);
            }
        } else {
            console.log('❌ API密钥无效！');
            console.log(`📊 状态码: ${testResult.statusCode || 'N/A'}`);
            
            if (testResult.error) {
                console.log(`📄 错误信息: ${testResult.error.message || JSON.stringify(testResult.error)}`);
            }
        }

        // 4. 显示配置信息
        console.log('\n4. 当前配置信息:');
        console.log('=' .repeat(40));
        console.log(`🌐 服务器地址: ${this.baseUrl}`);
        console.log(`🔑 API密钥: ${currentApiKey}`);
        console.log(`🤖 推荐模型: gemini-2.5-flash`);
        
        console.log('\n📱 Roo配置:');
        console.log(`   OPENAI_API_BASE=${this.baseUrl}/v1`);
        console.log(`   OPENAI_API_KEY=${currentApiKey}`);
        console.log(`   MODEL=gemini-2.5-flash`);
        
        console.log('\n📱 Cline配置:');
        console.log(`   Base URL: ${this.baseUrl}/v1`);
        console.log(`   API Key: ${currentApiKey}`);
        console.log(`   Model: gemini-2.5-flash`);

        // 5. 故障排除建议
        if (!testResult.success) {
            console.log('\n🛠️  故障排除建议:');
            console.log('   1. 重新生成API密钥: powershell -ExecutionPolicy Bypass -File setup-key.ps1');
            console.log('   2. 重启服务器: npm start');
            console.log('   3. 检查OAuth凭据: npm run update-oauth');
            console.log('   4. 查看服务器日志确认错误信息');
        }
    }
}

// 执行检查
if (require.main === module) {
    const checker = new ApiChecker();
    checker.run().catch(error => {
        console.error('检查过程中发生错误:', error.message);
        process.exit(1);
    });
}

module.exports = ApiChecker;
