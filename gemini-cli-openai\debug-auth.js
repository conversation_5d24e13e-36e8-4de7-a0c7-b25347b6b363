#!/usr/bin/env node

/**
 * 调试API密钥认证问题的工具
 */

const fs = require('fs');
const http = require('http');

// 获取.dev.vars中的API密钥
function getApiKeyFromDevVars() {
    try {
        const content = fs.readFileSync('./.dev.vars', 'utf8');
        const lines = content.split('\n');
        const apiKeyLine = lines.find(line => line.startsWith('OPENAI_API_KEY='));
        
        if (apiKeyLine) {
            const key = apiKeyLine.split('=')[1].trim();
            return key;
        }
        return null;
    } catch (error) {
        console.error('读取.dev.vars失败:', error.message);
        return null;
    }
}

// 调试API密钥字符
function debugApiKey(key) {
    console.log('🔍 API密钥调试信息:');
    console.log(`   长度: ${key.length}`);
    console.log(`   内容: "${key}"`);
    console.log(`   十六进制: ${Buffer.from(key, 'utf8').toString('hex')}`);
    console.log(`   字符码: [${key.split('').map(c => c.charCodeAt(0)).join(', ')}]`);
    
    // 检查是否有不可见字符
    const hasInvisible = /[\x00-\x1F\x7F-\x9F]/.test(key);
    console.log(`   包含不可见字符: ${hasInvisible ? '是' : '否'}`);
    
    if (hasInvisible) {
        console.log('   不可见字符位置:');
        for (let i = 0; i < key.length; i++) {
            const code = key.charCodeAt(i);
            if (code < 32 || (code >= 127 && code <= 159)) {
                console.log(`     位置 ${i}: 字符码 ${code} (${code === 13 ? 'CR' : code === 10 ? 'LF' : code === 9 ? 'TAB' : 'OTHER'})`);
            }
        }
    }
}

// 测试debug端点
async function testDebugEndpoint() {
    return new Promise((resolve) => {
        const options = {
            hostname: '127.0.0.1',
            port: 8787,
            path: '/v1/debug/api-key-check',
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${getApiKeyFromDevVars()}`,
                'Content-Type': 'application/json'
            }
        };

        console.log('🌐 调用debug端点: http://127.0.0.1:8787/v1/debug/api-key-check');

        const req = http.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                console.log(`📊 Debug端点响应:`);
                console.log(`   状态码: ${res.statusCode}`);

                try {
                    const response = JSON.parse(data);
                    console.log(`   密钥匹配: ${response.keys_match ? '✅ 是' : '❌ 否'}`);
                    console.log(`   服务器密钥存在: ${response.server_key_present ? '✅ 是' : '❌ 否'}`);
                    console.log(`   客户端密钥存在: ${response.client_key_present ? '✅ 是' : '❌ 否'}`);
                    console.log(`   服务器密钥长度: ${response.server_key_length}`);
                    console.log(`   客户端密钥长度: ${response.client_key_length}`);
                    console.log(`   服务器密钥前缀: ${response.server_key_prefix}`);
                    console.log(`   客户端密钥前缀: ${response.client_key_prefix}`);
                } catch (error) {
                    console.log(`   原始响应: ${data}`);
                }

                resolve({ statusCode: res.statusCode, body: data });
            });
        });

        req.on('error', (error) => {
            console.log(`❌ Debug端点请求错误: ${error.message}`);
            resolve({ error: error.message });
        });

        req.setTimeout(10000, () => {
            req.destroy();
            console.log('❌ Debug端点请求超时');
            resolve({ error: 'timeout' });
        });

        req.end();
    });
}

// 测试API请求
async function testApiRequest(apiKey) {
    return new Promise((resolve) => {
        const payload = JSON.stringify({
            model: 'gemini-2.5-flash',
            messages: [{ role: 'user', content: 'test' }],
            max_tokens: 5
        });

        const options = {
            hostname: '127.0.0.1',
            port: 8787,
            path: '/v1/chat/completions',
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(payload)
            }
        };

        console.log('\n🌐 发送API请求:');
        console.log(`   URL: http://127.0.0.1:8787/v1/chat/completions`);
        console.log(`   Authorization: Bearer ${apiKey}`);

        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`\n📊 响应结果:`);
                console.log(`   状态码: ${res.statusCode}`);
                console.log(`   响应头: ${JSON.stringify(res.headers, null, 2)}`);
                
                try {
                    const response = JSON.parse(data);
                    console.log(`   响应体: ${JSON.stringify(response, null, 2)}`);
                } catch (error) {
                    console.log(`   原始响应: ${data}`);
                }
                
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: data
                });
            });
        });

        req.on('error', (error) => {
            console.log(`❌ 请求错误: ${error.message}`);
            resolve({ error: error.message });
        });

        req.setTimeout(10000, () => {
            req.destroy();
            console.log('❌ 请求超时');
            resolve({ error: 'timeout' });
        });

        req.write(payload);
        req.end();
    });
}

// 主函数
async function main() {
    console.log('🔧 API密钥认证调试工具');
    console.log('=' .repeat(50));

    // 1. 获取API密钥
    const apiKey = getApiKeyFromDevVars();
    if (!apiKey) {
        console.log('❌ 无法获取API密钥');
        return;
    }

    // 2. 调试API密钥
    debugApiKey(apiKey);

    // 3. 测试API请求
    await testApiRequest(apiKey);

    // 4. 测试不同的API密钥变体
    console.log('\n🧪 测试API密钥变体:');
    
    // 测试去除首尾空格
    const trimmedKey = apiKey.trim();
    if (trimmedKey !== apiKey) {
        console.log('   测试去除空格的密钥...');
        await testApiRequest(trimmedKey);
    }
    
    // 测试默认密钥
    console.log('   测试默认密钥...');
    await testApiRequest('sk-your-secret-api-key-here');

    // 测试新的debug端点
    console.log('\n🔍 测试新的API密钥检查端点:');
    await testDebugEndpoint();
}

if (require.main === module) {
    main().catch(error => {
        console.error('调试过程中发生错误:', error.message);
        process.exit(1);
    });
}

module.exports = { getApiKeyFromDevVars, debugApiKey, testApiRequest };
