#!/usr/bin/env node

/**
 * 调试Roo错误：Cannot read properties of undefined (reading '0')
 */

const http = require('http');

async function testChatRequest() {
    console.log('🔍 测试Chat API请求...');
    
    const payload = {
        model: 'gemini-2.5-flash',
        messages: [
            {
                role: 'user',
                content: 'Hello, just say "Hi" back'
            }
        ],
        max_tokens: 100,
        stream: false
    };

    const postData = JSON.stringify(payload);
    
    const options = {
        hostname: '127.0.0.1',
        port: 8787,
        path: '/v1/chat/completions',
        method: 'POST',
        headers: {
            'Authorization': 'Bearer sk-eTXRUZYLykgnNVz1NkTIJpkXXXKPd3Wh27Kibl2vsmHr9H3w',
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
        }
    };

    return new Promise((resolve, reject) => {
        console.log('📤 发送请求...');
        console.log('URL:', `http://127.0.0.1:8787/v1/chat/completions`);
        console.log('Payload:', JSON.stringify(payload, null, 2));

        const req = http.request(options, (res) => {
            let data = '';
            
            console.log(`📊 响应状态码: ${res.statusCode}`);
            console.log(`📋 响应头:`, res.headers);
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log('📥 原始响应数据:');
                console.log(data);
                console.log('📏 响应长度:', data.length);
                
                try {
                    const response = JSON.parse(data);
                    console.log('✅ 解析后的JSON:');
                    console.log(JSON.stringify(response, null, 2));
                    
                    // 检查响应结构
                    console.log('\n🔍 响应结构分析:');
                    console.log('- response.choices 存在:', !!response.choices);
                    console.log('- response.choices 类型:', typeof response.choices);
                    console.log('- response.choices 长度:', response.choices ? response.choices.length : 'N/A');
                    
                    if (response.choices && response.choices.length > 0) {
                        console.log('- choices[0] 存在:', !!response.choices[0]);
                        console.log('- choices[0].message 存在:', !!response.choices[0]?.message);
                        console.log('- choices[0].message.content:', response.choices[0]?.message?.content);
                    } else {
                        console.log('❌ choices数组为空或不存在！');
                    }
                    
                    resolve(response);
                } catch (error) {
                    console.error('❌ JSON解析失败:', error.message);
                    console.log('原始数据:', data);
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ 请求错误:', error.message);
            reject(error);
        });

        req.setTimeout(15000, () => {
            req.destroy();
            console.log('❌ 请求超时');
            reject(new Error('Request timeout'));
        });

        req.write(postData);
        req.end();
    });
}

async function main() {
    try {
        await testChatRequest();
    } catch (error) {
        console.error('测试失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}
