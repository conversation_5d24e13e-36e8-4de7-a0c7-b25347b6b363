# Gemini CLI OpenAI API密钥生成和配置脚本
# 用于Roo/Cline等OpenAI兼容客户端

param(
    [switch]$Help,
    [switch]$Show,
    [string]$CustomKey = ""
)

# 生成随机API密钥
function New-ApiKey {
    $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    $keyLength = 48
    $randomKey = ""
    for ($i = 0; $i -lt $keyLength; $i++) {
        $randomKey += $chars[(Get-Random -Maximum $chars.Length)]
    }
    return "sk-$randomKey"
}

# 更新.dev.vars文件
function Update-DevVars {
    param([string]$ApiKey)
    
    $devVarsPath = ".\.dev.vars"
    
    if (-not (Test-Path $devVarsPath)) {
        Write-Host "错误: .dev.vars文件不存在" -ForegroundColor Red
        return $false
    }
    
    try {
        # 备份原文件
        $backupPath = ".\.dev.vars.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        Copy-Item $devVarsPath $backupPath
        Write-Host "已备份原文件到: $backupPath" -ForegroundColor Yellow
        
        # 读取并替换内容
        $content = Get-Content $devVarsPath
        $newContent = @()
        
        foreach ($line in $content) {
            if ($line -match '^OPENAI_API_KEY=') {
                $newContent += "OPENAI_API_KEY=$ApiKey"
            } else {
                $newContent += $line
            }
        }
        
        # 写入新内容
        $newContent | Set-Content $devVarsPath
        
        Write-Host "API密钥已更新到 .dev.vars" -ForegroundColor Green
        return $true
        
    } catch {
        Write-Host "更新.dev.vars失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 显示当前配置
function Show-Configuration {
    $devVarsPath = ".\.dev.vars"
    
    if (-not (Test-Path $devVarsPath)) {
        Write-Host ".dev.vars文件不存在" -ForegroundColor Red
        return
    }
    
    $content = Get-Content $devVarsPath
    $apiKeyLine = $content | Where-Object { $_ -match '^OPENAI_API_KEY=' } | Select-Object -First 1
    
    if ($apiKeyLine) {
        $currentKey = ($apiKeyLine -split '=', 2)[1]
        Write-Host "`n当前配置信息:" -ForegroundColor Cyan
        Write-Host "   API密钥: $currentKey" -ForegroundColor White
        Write-Host "   服务地址: http://127.0.0.1:8787" -ForegroundColor White
        Write-Host "   推荐模型: gemini-2.5-flash" -ForegroundColor Green
    } else {
        Write-Host "未找到API密钥配置" -ForegroundColor Red
    }
}

# 显示客户端配置说明
function Show-ClientConfiguration {
    param([string]$ApiKey)
    
    Write-Host "`n=== Roo/Cline配置说明 ===" -ForegroundColor Cyan
    
    Write-Host "`nCline (VS Code扩展) 配置:" -ForegroundColor Yellow
    Write-Host "   1. 打开VS Code，安装Cline扩展"
    Write-Host "   2. 打开Cline设置"
    Write-Host "   3. 配置以下参数:"
    Write-Host "      • API Provider: OpenAI" -ForegroundColor Green
    Write-Host "      • Base URL: http://127.0.0.1:8787/v1" -ForegroundColor Green
    Write-Host "      • API Key: $ApiKey" -ForegroundColor Green
    Write-Host "      • Model: gemini-2.5-flash" -ForegroundColor Green
    
    Write-Host "`nRoo 配置:" -ForegroundColor Yellow
    Write-Host "   设置环境变量:"
    Write-Host "      • OPENAI_API_BASE=http://127.0.0.1:8787/v1" -ForegroundColor Green
    Write-Host "      • OPENAI_API_KEY=$ApiKey" -ForegroundColor Green
    Write-Host "      • MODEL=gemini-2.5-flash" -ForegroundColor Green
    
    Write-Host "`n通用OpenAI客户端配置:" -ForegroundColor Yellow
    Write-Host "   • Base URL: http://127.0.0.1:8787/v1" -ForegroundColor Green
    Write-Host "   • API Key: $ApiKey" -ForegroundColor Green
    Write-Host "   • Model: gemini-2.5-flash" -ForegroundColor Green
    
    Write-Host "`n测试命令:" -ForegroundColor Yellow
    Write-Host "curl -X POST http://127.0.0.1:8787/v1/chat/completions \\" -ForegroundColor White
    Write-Host "  -H `"Authorization: Bearer $ApiKey`" \\" -ForegroundColor White
    Write-Host "  -H `"Content-Type: application/json`" \\" -ForegroundColor White
    Write-Host "  -d '{`"model`":`"gemini-2.5-flash`",`"messages`":[{`"role`":`"user`",`"content`":`"你好`"}]}'" -ForegroundColor White
}

# 显示帮助信息
function Show-Help {
    Write-Host "`nGemini CLI OpenAI API密钥配置工具" -ForegroundColor Cyan
    Write-Host "=================================" -ForegroundColor Blue
    
    Write-Host "`n用法:" -ForegroundColor Yellow
    Write-Host "   .\generate-api-key.ps1 [选项]"
    
    Write-Host "`n选项:" -ForegroundColor Yellow
    Write-Host "   -Show              显示当前配置信息" -ForegroundColor Green
    Write-Host "   -CustomKey [key]   使用自定义API密钥" -ForegroundColor Green
    Write-Host "   -Help              显示帮助信息" -ForegroundColor Green
    
    Write-Host "`n示例:" -ForegroundColor Yellow
    Write-Host "   .\generate-api-key.ps1                           # 生成新密钥"
    Write-Host "   .\generate-api-key.ps1 -Show                     # 显示配置"
    Write-Host "   .\generate-api-key.ps1 -CustomKey sk-abc123...   # 使用自定义密钥"
    
    Write-Host "`n功能:" -ForegroundColor Yellow
    Write-Host "   ✅ 生成符合OpenAI格式的API密钥 (sk-...)" -ForegroundColor Green
    Write-Host "   ✅ 自动更新.dev.vars配置文件" -ForegroundColor Green
    Write-Host "   ✅ 提供Roo/Cline详细配置说明" -ForegroundColor Green
    Write-Host "   ✅ 自动备份原配置文件" -ForegroundColor Green
}

# 主程序
Write-Host "Gemini CLI OpenAI API密钥配置工具" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Blue

# 检查是否在正确目录
if (-not (Test-Path ".\.dev.vars")) {
    Write-Host "错误: 请在gemini-cli-openai项目目录中运行此脚本" -ForegroundColor Red
    Write-Host "当前目录: $(Get-Location)" -ForegroundColor Yellow
    exit 1
}

if ($Help) {
    Show-Help
    exit 0
}

if ($Show) {
    Show-Configuration
    exit 0
}

# 确定使用的API密钥
$apiKey = ""
if ($CustomKey -ne "") {
    if ($CustomKey -match '^sk-[a-zA-Z0-9]{48,}$') {
        $apiKey = $CustomKey
        Write-Host "使用自定义API密钥: $apiKey" -ForegroundColor Green
    } else {
        Write-Host "错误: 自定义密钥格式不正确" -ForegroundColor Red
        Write-Host "正确格式: sk-[48位以上字母数字]" -ForegroundColor Yellow
        exit 1
    }
} else {
    $apiKey = New-ApiKey
    Write-Host "生成新API密钥: $apiKey" -ForegroundColor Green
}

# 更新配置文件
if (Update-DevVars -ApiKey $apiKey) {
    Write-Host "`n配置更新成功！" -ForegroundColor Green
    
    # 显示配置信息
    Show-Configuration
    
    # 显示客户端配置说明
    Show-ClientConfiguration -ApiKey $apiKey
    
    Write-Host "`n后续步骤:" -ForegroundColor Cyan
    Write-Host "   1. 启动服务器: npm run dev"
    Write-Host "   2. 配置Roo/Cline使用上述参数"
    Write-Host "   3. 测试连接是否正常"
    
} else {
    Write-Host "`n配置更新失败！" -ForegroundColor Red
    exit 1
}
