#!/usr/bin/env node

/**
 * 快速OAuth检查脚本
 * 用于package.json脚本和CI/CD流水线
 */

const fs = require('fs');

function checkOAuth(credsPath = 'I:/gemini/oauth_creds.json', silent = false) {
  try {
    if (!fs.existsSync(credsPath)) {
      if (!silent) console.log('❌ OAuth凭据文件不存在');
      return { valid: false, status: 'missing' };
    }

    const creds = JSON.parse(fs.readFileSync(credsPath, 'utf8'));
    const now = Date.now();
    const timeLeft = creds.expiry_date - now;

    if (timeLeft <= 0) {
      if (!silent) console.log('🚨 OAuth Token已过期');
      return { valid: false, status: 'expired' };
    }

    const minutes = Math.floor(timeLeft / 60000);
    const hours = Math.floor(minutes / 60);

    let status, message;
    if (minutes < 5) {
      status = 'critical';
      message = `🚨 Token将在 ${minutes} 分钟后过期`;
    } else if (minutes < 30) {
      status = 'warning';
      message = `⚠️  Token将在 ${minutes} 分钟后过期`;
    } else if (hours < 2) {
      status = 'caution';
      message = `⏰ Token将在 ${hours}h${minutes % 60}m后过期`;
    } else {
      status = 'good';
      message = `✅ Token正常 (${hours}h${minutes % 60}m)`;
    }

    if (!silent) console.log(message);
    return { valid: true, status, timeLeft, minutes, hours };

  } catch (error) {
    if (!silent) console.log('❌ 检查失败:', error.message);
    return { valid: false, status: 'error', error: error.message };
  }
}

// 命令行使用
if (require.main === module) {
  const args = process.argv.slice(2);
  const silent = args.includes('--silent') || args.includes('-s');
  const credsPath = args.find(arg => !arg.startsWith('-')) || 'I:/gemini/oauth_creds.json';
  
  const result = checkOAuth(credsPath, silent);
  process.exit(result.valid ? 0 : 1);
}

module.exports = checkOAuth;
