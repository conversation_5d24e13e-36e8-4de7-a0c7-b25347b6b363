#!/usr/bin/env node

/**
 * OAuth Token到期监控工具
 * 实时监控OAuth token状态，在即将到期时发出提醒
 * 
 * 使用方法:
 * node oauth_expiry_monitor.js [凭据文件路径] [检查间隔分钟]
 * 
 * 示例:
 * node oauth_expiry_monitor.js I:/gemini/oauth_creds.json 5
 */

const fs = require('fs');
const path = require('path');

class OAuthExpiryMonitor {
  constructor(credsPath, checkIntervalMinutes = 5) {
    this.credsPath = credsPath;
    this.checkInterval = checkIntervalMinutes * 60 * 1000; // 转换为毫秒
    this.lastWarningTime = 0;
    this.isRunning = false;
  }

  // 检查token状态
  checkTokenStatus() {
    try {
      if (!fs.existsSync(this.credsPath)) {
        console.log(`❌ [${new Date().toLocaleString()}] 凭据文件不存在: ${this.credsPath}`);
        return { status: 'file_not_found', timeLeft: 0 };
      }

      const credsContent = fs.readFileSync(this.credsPath, 'utf8');
      const creds = JSON.parse(credsContent);
      
      if (!creds.expiry_date) {
        console.log(`❌ [${new Date().toLocaleString()}] 凭据文件中缺少expiry_date字段`);
        return { status: 'invalid_format', timeLeft: 0 };
      }

      const now = Date.now();
      const expiryDate = creds.expiry_date;
      const timeLeft = expiryDate - now;

      return {
        status: timeLeft > 0 ? 'valid' : 'expired',
        timeLeft: timeLeft,
        expiryDate: expiryDate
      };

    } catch (error) {
      console.log(`❌ [${new Date().toLocaleString()}] 检查token状态失败: ${error.message}`);
      return { status: 'error', timeLeft: 0 };
    }
  }

  // 格式化时间显示
  formatTimeLeft(milliseconds) {
    if (milliseconds <= 0) return '已过期';
    
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天 ${hours % 24}小时 ${minutes % 60}分钟`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟 ${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  // 发送通知提醒
  sendNotification(message, level = 'info') {
    const timestamp = new Date().toLocaleString();
    const icons = {
      'info': 'ℹ️',
      'warning': '⚠️',
      'error': '❌',
      'success': '✅',
      'urgent': '🚨'
    };
    
    const icon = icons[level] || 'ℹ️';
    console.log(`${icon} [${timestamp}] ${message}`);
    
    // 在Windows上可以使用系统通知（可选）
    if (process.platform === 'win32' && (level === 'warning' || level === 'urgent')) {
      try {
        const { exec } = require('child_process');
        const title = 'OAuth Token 到期提醒';
        const cmd = `powershell -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.MessageBox]::Show('${message}', '${title}', 'OK', 'Warning')"`;
        exec(cmd, () => {}); // 异步执行，不等待结果
      } catch (error) {
        // 忽略通知错误
      }
    }
  }

  // 主监控循环
  startMonitoring() {
    if (this.isRunning) {
      console.log('⚠️  监控已在运行中');
      return;
    }

    this.isRunning = true;
    console.log(`🚀 开始监控OAuth token状态...`);
    console.log(`📁 凭据文件: ${this.credsPath}`);
    console.log(`⏰ 检查间隔: ${this.checkInterval / 60000}分钟`);
    console.log(`🔄 按 Ctrl+C 停止监控\n`);

    // 立即检查一次
    this.performCheck();

    // 设置定期检查
    this.monitorInterval = setInterval(() => {
      this.performCheck();
    }, this.checkInterval);

    // 处理程序退出
    process.on('SIGINT', () => {
      this.stopMonitoring();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      this.stopMonitoring();
      process.exit(0);
    });
  }

  // 执行检查
  performCheck() {
    const result = this.checkTokenStatus();
    const now = Date.now();

    switch (result.status) {
      case 'expired':
        this.sendNotification(
          `OAuth Token已过期！请立即运行 'gemini' 命令重新认证`,
          'urgent'
        );
        break;

      case 'valid':
        const timeLeft = result.timeLeft;
        const timeLeftStr = this.formatTimeLeft(timeLeft);
        
        // 根据剩余时间发送不同级别的提醒
        if (timeLeft <= 5 * 60 * 1000) { // 5分钟内
          this.sendNotification(
            `🚨 紧急：OAuth Token将在 ${timeLeftStr} 后过期！请立即更新`,
            'urgent'
          );
        } else if (timeLeft <= 30 * 60 * 1000) { // 30分钟内
          // 避免频繁提醒，每10分钟提醒一次
          if (now - this.lastWarningTime > 10 * 60 * 1000) {
            this.sendNotification(
              `⚠️  OAuth Token将在 ${timeLeftStr} 后过期，建议准备更新`,
              'warning'
            );
            this.lastWarningTime = now;
          }
        } else if (timeLeft <= 60 * 60 * 1000) { // 1小时内
          // 每30分钟提醒一次
          if (now - this.lastWarningTime > 30 * 60 * 1000) {
            this.sendNotification(
              `⏰ OAuth Token将在 ${timeLeftStr} 后过期`,
              'info'
            );
            this.lastWarningTime = now;
          }
        } else {
          // 正常状态，只在首次检查或每小时显示一次
          if (this.lastWarningTime === 0 || now - this.lastWarningTime > 60 * 60 * 1000) {
            this.sendNotification(
              `✅ OAuth Token状态正常，剩余时间: ${timeLeftStr}`,
              'success'
            );
            this.lastWarningTime = now;
          }
        }
        break;

      case 'file_not_found':
        this.sendNotification(
          `凭据文件不存在，请检查路径: ${this.credsPath}`,
          'error'
        );
        break;

      case 'invalid_format':
        this.sendNotification(
          `凭据文件格式无效，请重新获取OAuth凭据`,
          'error'
        );
        break;

      case 'error':
        this.sendNotification(
          `检查token状态时发生错误，请检查凭据文件`,
          'error'
        );
        break;
    }
  }

  // 停止监控
  stopMonitoring() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    this.isRunning = false;
    console.log('\n🛑 OAuth token监控已停止');
  }

  // 显示帮助信息
  static showHelp() {
    console.log(`
📖 OAuth Token到期监控工具使用说明

用法:
  node oauth_expiry_monitor.js [凭据文件路径] [检查间隔分钟]

参数:
  凭据文件路径    OAuth凭据JSON文件的路径 (可选)
                 默认: I:/gemini/oauth_creds.json
  检查间隔分钟    检查token状态的间隔时间，单位分钟 (可选)
                 默认: 5分钟

示例:
  node oauth_expiry_monitor.js
  node oauth_expiry_monitor.js I:/gemini/oauth_creds.json
  node oauth_expiry_monitor.js I:/gemini/oauth_creds.json 3

提醒级别:
  🚨 紧急 (5分钟内过期)    - 立即更新
  ⚠️  警告 (30分钟内过期)  - 建议准备更新  
  ⏰ 信息 (1小时内过期)    - 注意即将过期
  ✅ 正常 (1小时以上)      - 状态良好

功能特性:
  ✅ 实时监控token状态
  ✅ 智能提醒频率控制
  ✅ 多级别警告系统
  ✅ 系统通知支持 (Windows)
  ✅ 优雅的程序退出处理

注意事项:
  - 监控程序会持续运行直到手动停止
  - 按 Ctrl+C 可以安全停止监控
  - 建议在后台运行以持续监控
`);
  }
}

// 主程序
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    OAuthExpiryMonitor.showHelp();
    return;
  }
  
  const credsPath = args[0] || 'I:/gemini/oauth_creds.json';
  const checkInterval = parseInt(args[1]) || 5;
  
  if (checkInterval < 1) {
    console.log('❌ 检查间隔必须至少为1分钟');
    process.exit(1);
  }
  
  const monitor = new OAuthExpiryMonitor(credsPath, checkInterval);
  monitor.startMonitoring();
}

if (require.main === module) {
  main();
}

module.exports = OAuthExpiryMonitor;
