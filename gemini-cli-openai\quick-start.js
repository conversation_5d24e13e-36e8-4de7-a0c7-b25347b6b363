#!/usr/bin/env node

/**
 * 🚀 快速启动脚本
 * 简化版启动，支持OAuth和API Key两种认证方式
 */

const { spawn } = require('child_process');
const fs = require('fs');

class QuickStart {
    constructor() {
        this.devVarsPath = '.dev.vars';
    }

    // 检查认证配置
    checkAuth() {
        if (!fs.existsSync(this.devVarsPath)) {
            console.log('❌ 未找到配置文件，请先运行认证配置：');
            console.log('   node setup-auth.js');
            return false;
        }

        const content = fs.readFileSync(this.devVarsPath, 'utf8');
        const now = new Date();

        console.log('🔍 认证状态检查:');

        if (content.includes('GEMINI_API_KEY=')) {
            const match = content.match(/GEMINI_API_KEY=(.+)/);
            if (match && match[1].trim()) {
                const key = match[1].trim();
                console.log('✅ 认证方式: API Key');
                console.log(`🔑 API Key: ${key.substring(0, 20)}...`);
                console.log('⏰ 过期时间: 🟢 永不过期');
                console.log(`📅 检查时间: ${now.toLocaleString()}`);
                return true;
            }
        }

        if (content.includes('GCP_SERVICE_ACCOUNT=')) {
            try {
                const match = content.match(/GCP_SERVICE_ACCOUNT=(.+)/);
                if (match) {
                    const creds = JSON.parse(match[1]);
                    const expiryDate = new Date(creds.expiry_date);
                    const timeLeft = creds.expiry_date - Date.now();
                    const minutes = Math.floor(timeLeft / 60000);
                    const hours = Math.floor(minutes / 60);

                    console.log('✅ 认证方式: OAuth');
                    console.log(`⏰ 过期时间: ${expiryDate.toLocaleString()}`);
                    console.log(`📅 检查时间: ${now.toLocaleString()}`);

                    if (timeLeft > 0) {
                        if (hours > 0) {
                            console.log(`⏳ 剩余时间: 🟢 ${hours}小时${minutes % 60}分钟`);
                        } else if (minutes > 10) {
                            console.log(`⏳ 剩余时间: 🟡 ${minutes}分钟`);
                        } else {
                            console.log(`⏳ 剩余时间: 🔴 ${minutes}分钟 (即将过期)`);
                        }
                        return true;
                    } else {
                        console.log('⏳ 剩余时间: 🔴 已过期');
                        console.log('❌ OAuth token已过期，请更新：');
                        console.log('   node setup-auth.js --oauth');
                        return false;
                    }
                }
            } catch (error) {
                console.log('❌ OAuth配置格式错误');
                return false;
            }
        }

        console.log('❌ 未配置认证方式，请运行：');
        console.log('   node setup-auth.js');
        return false;
    }

    // 启动服务器
    start() {
        console.log('🚀 Gemini CLI OpenAI 快速启动');
        console.log('=' .repeat(50));

        if (!this.checkAuth()) {
            process.exit(1);
        }

        console.log('🔄 启动wrangler dev服务器...');
        console.log('📍 服务地址: http://127.0.0.1:8787');
        console.log('🤖 默认模型: gemini-2.5-flash');
        console.log('');

        const wrangler = spawn('npm', ['run', 'dev'], {
            stdio: 'inherit',
            shell: true
        });

        wrangler.on('error', (error) => {
            console.error('❌ 启动失败:', error.message);
            process.exit(1);
        });

        wrangler.on('close', (code) => {
            if (code !== 0) {
                console.log(`❌ 服务器退出，代码: ${code}`);
            }
        });

        // 优雅关闭
        process.on('SIGINT', () => {
            console.log('\n👋 正在关闭服务器...');
            wrangler.kill('SIGINT');
        });

        process.on('SIGTERM', () => {
            console.log('\n👋 正在关闭服务器...');
            wrangler.kill('SIGTERM');
        });
    }
}

// 显示帮助
function showHelp() {
    console.log(`
🚀 Gemini CLI OpenAI 快速启动工具

用法:
  node quick-start.js              # 快速启动服务器
  node quick-start.js --help       # 显示帮助

配置认证:
  node setup-auth.js               # 交互式配置认证
  node setup-auth.js --status      # 查看认证状态

支持的认证方式:
  1. API Key (推荐) - 永不过期
  2. OAuth - 每小时自动刷新

服务信息:
  📍 地址: http://127.0.0.1:8787
  🤖 默认模型: gemini-2.5-flash
  📚 API文档: http://127.0.0.1:8787/v1/models
    `);
}

// 主程序
function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        showHelp();
        return;
    }

    const quickStart = new QuickStart();
    quickStart.start();
}

if (require.main === module) {
    main();
}

module.exports = QuickStart;
