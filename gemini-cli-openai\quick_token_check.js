#!/usr/bin/env node

/**
 * 快速OAuth Token检查工具
 * 快速检查token状态并显示到期提醒
 */

const fs = require('fs');

function quickTokenCheck(credsPath = 'I:/gemini/oauth_creds.json') {
  try {
    if (!fs.existsSync(credsPath)) {
      console.log('❌ OAuth凭据文件不存在');
      return false;
    }

    const creds = JSON.parse(fs.readFileSync(credsPath, 'utf8'));
    const now = Date.now();
    const expiryDate = creds.expiry_date;
    const timeLeft = expiryDate - now;

    if (timeLeft <= 0) {
      console.log('🚨 OAuth Token已过期！请运行: gemini');
      return false;
    }

    const minutes = Math.floor(timeLeft / 60000);
    const hours = Math.floor(minutes / 60);

    if (minutes < 5) {
      console.log(`🚨 紧急：Token将在 ${minutes} 分钟后过期！`);
    } else if (minutes < 30) {
      console.log(`⚠️  警告：Token将在 ${minutes} 分钟后过期`);
    } else if (hours < 2) {
      console.log(`⏰ 提醒：Token将在 ${hours} 小时 ${minutes % 60} 分钟后过期`);
    } else {
      console.log(`✅ Token状态正常 (剩余 ${hours} 小时)`);
    }

    return true;
  } catch (error) {
    console.log('❌ 检查Token失败:', error.message);
    return false;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const credsPath = process.argv[2] || 'I:/gemini/oauth_creds.json';
  quickTokenCheck(credsPath);
}

module.exports = quickTokenCheck;
