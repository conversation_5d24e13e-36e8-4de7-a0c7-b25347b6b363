# 🚀 优化的启动脚本系统

## 📋 新增的npm脚本命令

| 命令 | 功能 | 说明 |
|------|------|------|
| `npm start` | 智能启动服务器 | 自动检查OAuth + 启动服务器 + 监控 |
| `npm run start:no-monitor` | 启动服务器（无监控） | 仅启动服务器，不监控OAuth |
| `npm run check` | 检查OAuth状态 | 显示详细的token状态信息 |
| `npm run check:silent` | 静默检查 | 仅返回状态码，用于脚本 |
| `npm run update-oauth` | 更新OAuth凭据 | 自动更新OAuth配置 |
| `npm run dev` | 原始启动方式 | 直接运行wrangler dev |

## 🎯 推荐使用方式

### 日常开发启动
```bash
npm start
```
这将：
- ✅ 自动检查OAuth token状态
- ✅ 根据token状态调整监控频率
- ✅ 启动开发服务器
- ✅ 提供实时OAuth监控

### 快速检查token状态
```bash
npm run check
```

### 更新OAuth凭据
```bash
npm run update-oauth
```

## 🔧 智能启动特性

### 1. 自动OAuth检查
启动前自动检查token状态：
- 🚨 **已过期/不存在**: 阻止启动，提示更新
- 🚨 **5分钟内过期**: 警告并等待确认
- ⚠️ **30分钟内过期**: 警告但继续启动
- ✅ **正常状态**: 直接启动

### 2. 智能监控频率
根据token状态自动调整检查频率：
- 🚨 **紧急状态** (< 5分钟): 每1分钟检查
- ⚠️ **警告状态** (< 30分钟): 每2分钟检查  
- ✅ **正常状态** (> 30分钟): 每5分钟检查

### 3. 优雅的错误处理
- 自动查找OAuth凭据文件
- 智能提示和错误恢复
- 安全的程序退出处理

## 📊 使用示例

### 启动输出示例
```bash
$ npm start

🎯 Gemini CLI OpenAI 智能启动器
==================================================
✅ Token状态正常 (剩余 2 小时)

🚀 启动Gemini CLI OpenAI开发服务器...
👁️  启动OAuth监控 (每5分钟检查一次)...

📝 服务器信息:
   🌐 本地地址: http://127.0.0.1:8787
   🌐 网络地址: http://192.168.3.120:8787
   ⌨️  按 Ctrl+C 停止服务器
   👁️  OAuth监控: 每5分钟检查一次
```

### 检查输出示例
```bash
$ npm run check
✅ Token正常 (2h15m)

$ npm run check
⚠️  Token将在 25 分钟后过期

$ npm run check
🚨 Token将在 3 分钟后过期
```

## 🔄 OAuth管理工作流

### 1. 日常启动
```bash
npm start  # 自动检查并启动
```

### 2. Token即将过期时
```bash
# 系统会自动提醒，然后：
gemini                    # 重新认证
npm run update-oauth      # 更新配置
npm start                 # 重新启动
```

### 3. Token已过期时
```bash
npm start                 # 会提示token过期
gemini                    # 重新认证
npm run update-oauth      # 更新配置
npm start                 # 重新启动
```

## 🛠️ 高级用法

### 1. CI/CD集成
```bash
# 在CI脚本中检查token状态
npm run check:silent
if [ $? -ne 0 ]; then
  echo "OAuth token无效，跳过部署"
  exit 1
fi
```

### 2. 自定义监控
```bash
# 禁用监控启动
npm run start:no-monitor

# 或者使用原始方式
npm run dev
```

### 3. 批量操作
```bash
# 检查状态并根据结果执行操作
npm run check && npm start || echo "需要更新OAuth"
```

## 📁 文件结构

```
scripts/
├── start-with-oauth-check.js  # 智能启动器
├── check-oauth.js             # OAuth检查工具
├── update-oauth.js            # OAuth更新工具
└── README.md                  # 本文档

oauth_backups/                 # 自动备份目录
├── .dev.vars.backup.xxx       # 配置备份文件
└── ...
```

## 🚨 故障排除

### 问题1: 启动失败
```bash
# 检查OAuth状态
npm run check

# 如果token过期，重新认证
gemini
npm run update-oauth
```

### 问题2: 监控不工作
```bash
# 使用无监控模式启动
npm run start:no-monitor

# 或使用原始方式
npm run dev
```

### 问题3: 找不到凭据文件
```bash
# 手动指定凭据文件路径
npm run update-oauth path/to/oauth_creds.json
```

## 💡 最佳实践

1. **日常使用**: 始终使用 `npm start` 而不是 `npm run dev`
2. **定期检查**: 每天开始工作时运行 `npm run check`
3. **及时更新**: 收到过期警告时及时更新token
4. **备份管理**: 定期清理 `oauth_backups` 目录中的旧备份

## 🔗 相关命令对比

| 旧方式 | 新方式 | 优势 |
|--------|--------|------|
| `npm run dev` | `npm start` | 自动OAuth检查 + 监控 |
| 手动检查token | `npm run check` | 标准化检查流程 |
| 手动更新配置 | `npm run update-oauth` | 自动化更新流程 |
| 无监控 | 智能监控 | 主动提醒，避免中断 |

通过这些优化的启动脚本，您可以更高效、更安全地管理Gemini CLI OpenAI项目！
