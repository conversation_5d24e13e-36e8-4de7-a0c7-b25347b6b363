#!/usr/bin/env node

/**
 * 优化的启动脚本 - 集成OAuth检查和监控
 * 自动检查OAuth token状态，并提供智能启动选项
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

class SmartStarter {
  constructor() {
    this.credsPath = 'I:/gemini/oauth_creds.json';
    this.serverProcess = null;
    this.monitorProcess = null;
  }

  // 检查OAuth token状态
  checkOAuthStatus() {
    try {
      if (!fs.existsSync(this.credsPath)) {
        return { status: 'missing', message: '❌ OAuth凭据文件不存在' };
      }

      const creds = JSON.parse(fs.readFileSync(this.credsPath, 'utf8'));
      const now = Date.now();
      const timeLeft = creds.expiry_date - now;

      if (timeLeft <= 0) {
        return { status: 'expired', message: '🚨 OAuth Token已过期' };
      }

      const minutes = Math.floor(timeLeft / 60000);
      const hours = Math.floor(minutes / 60);

      if (minutes < 5) {
        return { status: 'critical', message: `🚨 紧急：Token将在 ${minutes} 分钟后过期`, timeLeft };
      } else if (minutes < 30) {
        return { status: 'warning', message: `⚠️  警告：Token将在 ${minutes} 分钟后过期`, timeLeft };
      } else if (hours < 2) {
        return { status: 'caution', message: `⏰ 提醒：Token将在 ${hours} 小时 ${minutes % 60} 分钟后过期`, timeLeft };
      } else {
        return { status: 'good', message: `✅ Token状态正常 (剩余 ${hours} 小时)`, timeLeft };
      }
    } catch (error) {
      return { status: 'error', message: `❌ 检查Token失败: ${error.message}` };
    }
  }

  // 启动开发服务器
  startServer() {
    console.log('🚀 启动Gemini CLI OpenAI开发服务器...');
    
    this.serverProcess = spawn('wrangler', ['dev', '--local'], {
      stdio: 'inherit',
      shell: true
    });

    this.serverProcess.on('error', (error) => {
      console.error('❌ 服务器启动失败:', error.message);
    });

    this.serverProcess.on('exit', (code) => {
      if (code !== 0) {
        console.log(`\n🛑 服务器异常退出 (代码: ${code})`);
      }
      this.cleanup();
    });
  }

  // 启动简化监控
  startLightweightMonitor(checkInterval = 5) {
    console.log(`👁️  启动OAuth监控 (每${checkInterval}分钟检查一次)...`);
    
    const checkToken = () => {
      const status = this.checkOAuthStatus();
      const timestamp = new Date().toLocaleTimeString();
      
      if (status.status === 'critical' || status.status === 'expired') {
        console.log(`\n🚨 [${timestamp}] ${status.message}`);
        console.log('   请立即运行: gemini');
      } else if (status.status === 'warning') {
        console.log(`\n⚠️  [${timestamp}] ${status.message}`);
        console.log('   建议准备更新token');
      }
    };

    // 立即检查一次
    setTimeout(checkToken, 3000);
    
    // 设置定期检查
    this.monitorInterval = setInterval(checkToken, checkInterval * 60 * 1000);
  }

  // 清理资源
  cleanup() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
    
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
  }

  // 主启动流程
  async start(options = {}) {
    console.log('🎯 Gemini CLI OpenAI 智能启动器');
    console.log('=' .repeat(50));

    // 1. 检查OAuth状态
    const oauthStatus = this.checkOAuthStatus();
    console.log(oauthStatus.message);

    // 2. 根据状态决定启动策略
    if (oauthStatus.status === 'missing' || oauthStatus.status === 'expired') {
      console.log('\n❌ 无法启动服务器，请先解决OAuth问题：');
      console.log('   1. 运行: gemini');
      console.log('   2. 选择 "Login with Google"');
      console.log('   3. 完成认证后重新启动');
      process.exit(1);
    }

    if (oauthStatus.status === 'critical') {
      console.log('\n⚠️  Token即将过期，建议先更新再启动');
      console.log('   继续启动请按 Enter，更新token请按 Ctrl+C');
      
      // 等待用户确认（简化版）
      await new Promise(resolve => {
        process.stdin.once('data', () => resolve());
        setTimeout(resolve, 5000); // 5秒后自动继续
      });
    }

    console.log('');

    // 3. 启动服务器
    this.startServer();

    // 4. 根据token状态决定监控频率
    let monitorInterval = 5; // 默认5分钟
    if (oauthStatus.status === 'critical') {
      monitorInterval = 1; // 紧急状态每分钟检查
    } else if (oauthStatus.status === 'warning') {
      monitorInterval = 2; // 警告状态每2分钟检查
    }

    if (options.monitor !== false) {
      this.startLightweightMonitor(monitorInterval);
    }

    // 5. 处理程序退出
    process.on('SIGINT', () => {
      console.log('\n🛑 正在停止服务器...');
      this.cleanup();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 正在停止服务器...');
      this.cleanup();
      process.exit(0);
    });

    // 6. 显示使用信息
    setTimeout(() => {
      console.log('\n📝 服务器信息:');
      console.log('   🌐 本地地址: http://127.0.0.1:8787');
      console.log('   🌐 网络地址: http://192.168.3.120:8787');
      console.log('   ⌨️  按 Ctrl+C 停止服务器');
      
      if (options.monitor !== false) {
        console.log(`   👁️  OAuth监控: 每${monitorInterval}分钟检查一次`);
      }
      console.log('');
    }, 2000);
  }
}

// 命令行参数处理
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    monitor: true,
    help: false
  };

  for (const arg of args) {
    switch (arg) {
      case '--no-monitor':
        options.monitor = false;
        break;
      case '--help':
      case '-h':
        options.help = true;
        break;
    }
  }

  return options;
}

// 显示帮助信息
function showHelp() {
  console.log(`
📖 Gemini CLI OpenAI 智能启动器

用法:
  node scripts/start-with-oauth-check.js [选项]

选项:
  --no-monitor    禁用OAuth监控
  --help, -h      显示帮助信息

功能:
  ✅ 启动前自动检查OAuth token状态
  ✅ 根据token状态调整监控频率
  ✅ 智能提醒和错误处理
  ✅ 优雅的程序退出处理

监控频率:
  🚨 紧急状态 (< 5分钟)   - 每1分钟检查
  ⚠️  警告状态 (< 30分钟)  - 每2分钟检查
  ⏰ 正常状态 (> 30分钟)  - 每5分钟检查

示例:
  npm run start           # 标准启动（带监控）
  npm run start:no-monitor # 启动但不监控
  npm run dev             # 原始启动方式
`);
}

// 主程序
async function main() {
  const options = parseArgs();
  
  if (options.help) {
    showHelp();
    return;
  }
  
  const starter = new SmartStarter();
  await starter.start(options);
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ 启动失败:', error.message);
    process.exit(1);
  });
}

module.exports = SmartStarter;
