#!/usr/bin/env node

/**
 * OAuth更新脚本
 * 自动化OAuth凭据更新流程
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

class OAuthUpdater {
  constructor() {
    this.credsPath = 'I:/gemini/oauth_creds.json';
    this.devVarsPath = '.dev.vars';
    this.backupDir = 'oauth_backups';
  }

  // 创建备份
  createBackup() {
    try {
      if (!fs.existsSync(this.backupDir)) {
        fs.mkdirSync(this.backupDir, { recursive: true });
      }

      if (fs.existsSync(this.devVarsPath)) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = path.join(this.backupDir, `.dev.vars.backup.${timestamp}`);
        fs.copyFileSync(this.devVarsPath, backupPath);
        console.log(`💾 配置已备份: ${backupPath}`);
      }

      return true;
    } catch (error) {
      console.error('❌ 备份失败:', error.message);
      return false;
    }
  }

  // 更新环境变量
  updateDevVars(credsPath) {
    try {
      if (!fs.existsSync(credsPath)) {
        throw new Error(`凭据文件不存在: ${credsPath}`);
      }

      const creds = JSON.parse(fs.readFileSync(credsPath, 'utf8'));
      const credsJson = JSON.stringify(creds);

      // 确保.dev.vars存在
      if (!fs.existsSync(this.devVarsPath)) {
        if (fs.existsSync('.dev.vars.example')) {
          fs.copyFileSync('.dev.vars.example', this.devVarsPath);
        } else {
          fs.writeFileSync(this.devVarsPath, '');
        }
      }

      let content = fs.readFileSync(this.devVarsPath, 'utf8');
      const gcpLine = `GCP_SERVICE_ACCOUNT=${credsJson}`;

      if (content.includes('GCP_SERVICE_ACCOUNT=')) {
        content = content.replace(/^GCP_SERVICE_ACCOUNT=.*$/m, gcpLine);
      } else {
        content = `${gcpLine}\n\n${content}`;
      }

      fs.writeFileSync(this.devVarsPath, content);
      console.log('✅ 环境变量已更新');
      return true;

    } catch (error) {
      console.error('❌ 更新失败:', error.message);
      return false;
    }
  }

  // 主更新流程
  async update(sourceCredsPath = null) {
    console.log('🔄 开始OAuth更新流程...');

    // 1. 确定凭据源
    const credsSource = sourceCredsPath || this.credsPath;
    
    if (!fs.existsSync(credsSource)) {
      console.log('❌ 凭据文件不存在，尝试从Gemini CLI获取...');
      
      // 尝试从默认位置获取
      const defaultPaths = [
        path.join(process.env.HOME || process.env.USERPROFILE, '.gemini', 'oauth_creds.json'),
        'C:\\Users\\<USER>\\.gemini\\oauth_creds.json'
      ];

      let found = false;
      for (const defaultPath of defaultPaths) {
        if (fs.existsSync(defaultPath)) {
          console.log(`📁 找到凭据文件: ${defaultPath}`);
          // 复制到工作目录
          fs.copyFileSync(defaultPath, this.credsPath);
          found = true;
          break;
        }
      }

      if (!found) {
        console.log('❌ 未找到OAuth凭据，请运行: gemini');
        return false;
      }
    }

    // 2. 验证凭据
    try {
      const creds = JSON.parse(fs.readFileSync(this.credsPath, 'utf8'));
      const timeLeft = creds.expiry_date - Date.now();
      
      if (timeLeft <= 0) {
        console.log('⚠️  警告: Token已过期，但仍会更新配置');
      } else {
        const minutes = Math.floor(timeLeft / 60000);
        console.log(`✅ Token有效 (剩余 ${minutes} 分钟)`);
      }
    } catch (error) {
      console.error('❌ 凭据格式无效:', error.message);
      return false;
    }

    // 3. 备份当前配置
    if (!this.createBackup()) {
      console.log('⚠️  备份失败，继续更新...');
    }

    // 4. 更新环境变量
    if (!this.updateDevVars(this.credsPath)) {
      return false;
    }

    console.log('🎉 OAuth更新完成！');
    console.log('📝 后续步骤:');
    console.log('   1. 重启服务器以应用新配置');
    console.log('   2. 运行 npm run check 验证状态');
    
    return true;
  }
}

// 显示帮助
function showHelp() {
  console.log(`
📖 OAuth更新工具

用法:
  npm run update-oauth [凭据文件路径]

示例:
  npm run update-oauth                    # 使用默认路径
  npm run update-oauth path/to/creds.json # 使用指定文件

功能:
  ✅ 自动查找OAuth凭据文件
  ✅ 备份当前配置
  ✅ 更新环境变量
  ✅ 验证凭据有效性
`);
}

// 主程序
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }

  const credsPath = args[0];
  const updater = new OAuthUpdater();
  
  const success = await updater.update(credsPath);
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ 更新失败:', error.message);
    process.exit(1);
  });
}

module.exports = OAuthUpdater;
