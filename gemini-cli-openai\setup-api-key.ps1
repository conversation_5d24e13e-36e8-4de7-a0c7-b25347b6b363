# Gemini CLI OpenAI API密钥配置脚本
# 用于生成sk-api格式密钥并配置给Roo/Cline使用

param(
    [switch]$Help,
    [switch]$Generate,
    [switch]$Show,
    [string]$CustomKey
)

# 颜色输出函数
function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    $colors = @{
        "Red" = "Red"; "Green" = "Green"; "Yellow" = "Yellow"; 
        "Blue" = "Blue"; "Cyan" = "Cyan"; "Magenta" = "Magenta"; "White" = "White"
    }
    Write-Host $Text -ForegroundColor $colors[$Color]
}

# 生成随机API密钥
function New-ApiKey {
    $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    $keyLength = 48
    $randomKey = -join ((1..$keyLength) | ForEach-Object { $chars[(Get-Random -Maximum $chars.Length)] })
    return "sk-$randomKey"
}

# 更新.dev.vars文件
function Update-DevVars {
    param([string]$ApiKey)
    
    $devVarsPath = ".\.dev.vars"
    
    if (-not (Test-Path $devVarsPath)) {
        Write-ColorText "❌ 错误: .dev.vars文件不存在" "Red"
        return $false
    }
    
    try {
        # 读取文件内容
        $content = Get-Content $devVarsPath -Raw
        
        # 备份原文件
        $backupPath = ".\.dev.vars.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        Copy-Item $devVarsPath $backupPath
        Write-ColorText "💾 已备份原文件到: $backupPath" "Yellow"
        
        # 替换API密钥（处理重复行）
        $newContent = $content -replace 'OPENAI_API_KEY=.*', "OPENAI_API_KEY=$ApiKey"
        
        # 写入新内容
        $newContent | Set-Content $devVarsPath -NoNewline
        
        Write-ColorText "✅ API密钥已更新到 .dev.vars" "Green"
        return $true
        
    }
    catch {
        Write-ColorText "❌ 更新.dev.vars失败: $($_.Exception.Message)" "Red"
        return $false
    }
}

# 显示配置信息
function Show-Configuration {
    $devVarsPath = ".\.dev.vars"
    
    if (-not (Test-Path $devVarsPath)) {
        Write-ColorText "❌ .dev.vars文件不存在" "Red"
        return
    }
    
    $content = Get-Content $devVarsPath
    $apiKeyLine = $content | Where-Object { $_ -match '^OPENAI_API_KEY=' } | Select-Object -First 1
    
    if ($apiKeyLine) {
        $currentKey = ($apiKeyLine -split '=', 2)[1]
        Write-ColorText "`n📋 当前配置信息:" "Cyan"
        Write-ColorText "   API密钥: $currentKey" "White"
        Write-ColorText "   服务地址: http://127.0.0.1:8787" "White"
        Write-ColorText "   推荐模型: gemini-2.5-flash" "Green"
    }
    else {
        Write-ColorText "❌ 未找到API密钥配置" "Red"
    }
}

# 生成Roo/Cline配置说明
function Show-ClientConfiguration {
    param([string]$ApiKey)
    
    Write-ColorText "`n🔧 Roo/Cline配置说明:" "Cyan"
    Write-ColorText "=" * 50 "Blue"
    
    Write-ColorText "`n📱 Cline (VS Code扩展) 配置:" "Yellow"
    Write-ColorText "   1. 打开VS Code，安装Cline扩展" "White"
    Write-ColorText "   2. 打开Cline设置" "White"
    Write-ColorText "   3. 配置以下参数:" "White"
    Write-ColorText "      • API Provider: OpenAI" "Green"
    Write-ColorText "      • Base URL: http://127.0.0.1:8787/v1" "Green"
    Write-ColorText "      • API Key: $ApiKey" "Green"
    Write-ColorText "      • Model: gemini-2.5-flash" "Green"
    
    Write-ColorText "`n🤖 Roo 配置:" "Yellow"
    Write-ColorText "   1. 设置环境变量或配置文件:" "White"
    Write-ColorText "      • OPENAI_API_BASE=http://127.0.0.1:8787/v1" "Green"
    Write-ColorText "      • OPENAI_API_KEY=$ApiKey" "Green"
    Write-ColorText "      • MODEL=gemini-2.5-flash" "Green"
    
    Write-ColorText "`n🌐 通用OpenAI客户端配置:" "Yellow"
    Write-ColorText "   • Base URL: http://127.0.0.1:8787/v1" "Green"
    Write-ColorText "   • API Key: $ApiKey" "Green"
    Write-ColorText "   • Model: gemini-2.5-flash" "Green"
    
    Write-ColorText "`n📝 测试命令:" "Yellow"
    Write-ColorText "   curl -X POST http://127.0.0.1:8787/v1/chat/completions \" "White"
    Write-ColorText "     -H `"Authorization: Bearer $ApiKey`" \" "White"
    Write-ColorText "     -H `"Content-Type: application/json`" \" "White"
    Write-ColorText "     -d '{`"model`":`"gemini-2.5-flash`",`"messages`":[{`"role`":`"user`",`"content`":`"你好`"}]}'" "White"
}

# 显示帮助信息
function Show-Help {
    Write-ColorText "`n📖 Gemini CLI OpenAI API密钥配置工具" "Cyan"
    Write-ColorText "=" * 50 "Blue"
    
    Write-ColorText "`n用法:" "Yellow"
    Write-ColorText "   .\setup-api-key.ps1 [选项]" "White"
    
    Write-ColorText "`n选项:" "Yellow"
    Write-ColorText "   -Generate          生成新的API密钥并更新配置" "Green"
    Write-ColorText "   -Show              显示当前配置信息" "Green"
    Write-ColorText "   -CustomKey [key]   使用自定义API密钥" "Green"
    Write-ColorText "   -Help              显示帮助信息" "Green"
    
    Write-ColorText "`n示例:" "Yellow"
    Write-ColorText "   .\setup-api-key.ps1 -Generate                    # 生成新密钥" "White"
    Write-ColorText "   .\setup-api-key.ps1 -Show                        # 显示配置" "White"
    Write-ColorText "   .\setup-api-key.ps1 -CustomKey sk-abc123...      # 使用自定义密钥" "White"
    
    Write-ColorText "`n功能:" "Yellow"
    Write-ColorText "   ✅ 生成符合OpenAI格式的API密钥 (sk-...)" "Green"
    Write-ColorText "   ✅ 自动更新.dev.vars配置文件" "Green"
    Write-ColorText "   ✅ 提供Roo/Cline详细配置说明" "Green"
    Write-ColorText "   ✅ 自动备份原配置文件" "Green"
    Write-ColorText "   ✅ 生成测试命令" "Green"
}

# 主程序
function Main {
    Write-ColorText "🚀 Gemini CLI OpenAI API密钥配置工具" "Cyan"
    Write-ColorText "=" * 50 "Blue"
    
    # 检查是否在正确目录
    if (-not (Test-Path ".\.dev.vars")) {
        Write-ColorText "❌ 错误: 请在gemini-cli-openai项目目录中运行此脚本" "Red"
        Write-ColorText "   当前目录: $(Get-Location)" "Yellow"
        Write-ColorText "   预期文件: .\.dev.vars" "Yellow"
        return
    }
    
    if ($Help) {
        Show-Help
        return
    }
    
    if ($Show) {
        Show-Configuration
        return
    }
    
    $apiKey = $null
    
    if ($CustomKey) {
        if ($CustomKey -match '^sk-[a-zA-Z0-9]{48,}$') {
            $apiKey = $CustomKey
            Write-ColorText "🔑 使用自定义API密钥: $apiKey" "Green"
        }
        else {
            Write-ColorText "❌ 错误: 自定义密钥格式不正确" "Red"
            Write-ColorText "   正确格式: sk-[48位以上字母数字]" "Yellow"
            return
        }
    }
    elseif ($Generate) {
        $apiKey = New-ApiKey
        Write-ColorText "🔑 生成新API密钥: $apiKey" "Green"
    }
    else {
        # 默认行为：生成新密钥
        $apiKey = New-ApiKey
        Write-ColorText "🔑 生成新API密钥: $apiKey" "Green"
    }
    
    # 更新配置文件
    if (Update-DevVars -ApiKey $apiKey) {
        Write-ColorText "`n✅ 配置更新成功！" "Green"
        
        # 显示配置信息
        Show-Configuration
        
        # 显示客户端配置说明
        Show-ClientConfiguration -ApiKey $apiKey
        
        Write-ColorText "`n📋 后续步骤:" "Cyan"
        Write-ColorText "   1. 启动服务器: npm run dev" "White"
        Write-ColorText "   2. 配置Roo/Cline使用上述参数" "White"
        Write-ColorText "   3. 测试连接是否正常" "White"
        
    }
    else {
        Write-ColorText "`n❌ 配置更新失败！" "Red"
    }
}

# 执行主程序
Main
