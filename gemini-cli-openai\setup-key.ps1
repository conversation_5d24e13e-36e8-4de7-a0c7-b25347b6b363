# Gemini API Key Setup for Roo/Cline
param(
    [switch]$Help,
    [switch]$Show,
    [string]$CustomKey = ""
)

function New-ApiKey {
    $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    $keyLength = 48
    $randomKey = ""
    for ($i = 0; $i -lt $keyLength; $i++) {
        $randomKey += $chars[(Get-Random -Maximum $chars.Length)]
    }
    return "sk-$randomKey"
}

function Update-DevVars {
    param([string]$ApiKey)
    
    $devVarsPath = ".dev.vars"
    
    if (-not (Test-Path $devVarsPath)) {
        Write-Host "Error: .dev.vars file not found" -ForegroundColor Red
        return $false
    }
    
    try {
        # Backup original file
        $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
        $backupPath = ".dev.vars.backup.$timestamp"
        Copy-Item $devVarsPath $backupPath
        Write-Host "Backed up to: $backupPath" -ForegroundColor Yellow
        
        # Read and replace content
        $content = Get-Content $devVarsPath
        $newContent = @()
        $keyUpdated = $false

        foreach ($line in $content) {
            if ($line -match '^OPENAI_API_KEY=') {
                if (-not $keyUpdated) {
                    $newContent += "OPENAI_API_KEY=$ApiKey"
                    $keyUpdated = $true
                }
                # Skip duplicate OPENAI_API_KEY lines
            }
            else {
                $newContent += $line
            }
        }
        
        # Write new content
        $newContent | Set-Content $devVarsPath
        
        Write-Host "API key updated in .dev.vars" -ForegroundColor Green
        return $true
        
    }
    catch {
        Write-Host "Failed to update .dev.vars: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Show-Configuration {
    $devVarsPath = ".dev.vars"
    
    if (-not (Test-Path $devVarsPath)) {
        Write-Host ".dev.vars file not found" -ForegroundColor Red
        return
    }
    
    $content = Get-Content $devVarsPath
    $apiKeyLine = $content | Where-Object { $_ -match '^OPENAI_API_KEY=' } | Select-Object -First 1
    
    if ($apiKeyLine) {
        $currentKey = ($apiKeyLine -split '=', 2)[1]
        Write-Host "`nCurrent Configuration:" -ForegroundColor Cyan
        Write-Host "   API Key: $currentKey" -ForegroundColor White
        Write-Host "   Server: http://127.0.0.1:8787" -ForegroundColor White
        Write-Host "   Model: gemini-2.5-flash" -ForegroundColor Green
    }
    else {
        Write-Host "API key not found in configuration" -ForegroundColor Red
    }
}

function Show-ClientConfiguration {
    param([string]$ApiKey)
    
    Write-Host "`n=== Roo/Cline Configuration ===" -ForegroundColor Cyan
    
    Write-Host "`nCline (VS Code Extension):" -ForegroundColor Yellow
    Write-Host "   1. Install Cline extension in VS Code"
    Write-Host "   2. Open Cline settings"
    Write-Host "   3. Configure:"
    Write-Host "      • API Provider: OpenAI" -ForegroundColor Green
    Write-Host "      • Base URL: http://127.0.0.1:8787/v1" -ForegroundColor Green
    Write-Host "      • API Key: $ApiKey" -ForegroundColor Green
    Write-Host "      • Model: gemini-2.5-flash" -ForegroundColor Green

    Write-Host "`nQuick Copy for Cline:" -ForegroundColor Cyan
    Write-Host "Base URL: http://127.0.0.1:8787/v1" -ForegroundColor White
    Write-Host "API Key: $ApiKey" -ForegroundColor White
    
    Write-Host "`nRoo Configuration:" -ForegroundColor Yellow
    Write-Host "   Set environment variables:"
    Write-Host "      • OPENAI_API_BASE=http://127.0.0.1:8787/v1" -ForegroundColor Green
    Write-Host "      • OPENAI_API_KEY=$ApiKey" -ForegroundColor Green
    Write-Host "      • MODEL=gemini-2.5-flash" -ForegroundColor Green
    
    Write-Host "`nGeneral OpenAI Client:" -ForegroundColor Yellow
    Write-Host "   • Base URL: http://127.0.0.1:8787/v1" -ForegroundColor Green
    Write-Host "   • API Key: $ApiKey" -ForegroundColor Green
    Write-Host "   • Model: gemini-2.5-flash" -ForegroundColor Green
    
    Write-Host "`nTest Command:" -ForegroundColor Yellow
    Write-Host "curl -X POST http://127.0.0.1:8787/v1/chat/completions \\" -ForegroundColor White
    Write-Host "  -H `"Authorization: Bearer $ApiKey`" \\" -ForegroundColor White
    Write-Host "  -H `"Content-Type: application/json`" \\" -ForegroundColor White
    Write-Host "  -d '{`"model`":`"gemini-2.5-flash`",`"messages`":[{`"role`":`"user`",`"content`":`"Hello`"}]}'" -ForegroundColor White
}

function Show-Help {
    Write-Host "`nGemini CLI OpenAI API Key Setup Tool" -ForegroundColor Cyan
    Write-Host "====================================" -ForegroundColor Blue
    
    Write-Host "`nUsage:" -ForegroundColor Yellow
    Write-Host "   .\setup-key.ps1 [options]"
    
    Write-Host "`nOptions:" -ForegroundColor Yellow
    Write-Host "   -Show              Show current configuration" -ForegroundColor Green
    Write-Host "   -CustomKey [key]   Use custom API key" -ForegroundColor Green
    Write-Host "   -Help              Show help information" -ForegroundColor Green
    
    Write-Host "`nExamples:" -ForegroundColor Yellow
    Write-Host "   .\setup-key.ps1                           # Generate new key"
    Write-Host "   .\setup-key.ps1 -Show                     # Show config"
    Write-Host "   .\setup-key.ps1 -CustomKey sk-abc123...   # Use custom key"
    
    Write-Host "`nFeatures:" -ForegroundColor Yellow
    Write-Host "   ✅ Generate OpenAI-format API key (sk-...)" -ForegroundColor Green
    Write-Host "   ✅ Auto-update .dev.vars config file" -ForegroundColor Green
    Write-Host "   ✅ Provide Roo/Cline setup instructions" -ForegroundColor Green
    Write-Host "   ✅ Auto-backup original config" -ForegroundColor Green
}

# Main Program
Write-Host "Gemini CLI OpenAI API Key Setup Tool" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Blue

# Check if in correct directory
if (-not (Test-Path ".dev.vars")) {
    Write-Host "Error: Please run this script in the gemini-cli-openai project directory" -ForegroundColor Red
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
    exit 1
}

if ($Help) {
    Show-Help
    exit 0
}

if ($Show) {
    Show-Configuration
    exit 0
}

# Determine API key to use
$apiKey = ""
if ($CustomKey -ne "") {
    if ($CustomKey -match '^sk-[a-zA-Z0-9]{48,}$') {
        $apiKey = $CustomKey
        Write-Host "Using custom API key: $apiKey" -ForegroundColor Green
    }
    else {
        Write-Host "Error: Invalid custom key format" -ForegroundColor Red
        Write-Host "Correct format: sk-[48+ alphanumeric characters]" -ForegroundColor Yellow
        exit 1
    }
}
else {
    $apiKey = New-ApiKey
    Write-Host "Generated new API key: $apiKey" -ForegroundColor Green
}

# Update configuration file
if (Update-DevVars -ApiKey $apiKey) {
    Write-Host "`nConfiguration updated successfully!" -ForegroundColor Green
    
    # Show configuration
    Show-Configuration
    
    # Show client configuration
    Show-ClientConfiguration -ApiKey $apiKey
    
    Write-Host "`nNext Steps:" -ForegroundColor Cyan
    Write-Host "   1. Start server: npm run dev"
    Write-Host "   2. Configure Roo/Cline with above parameters"
    Write-Host "   3. Test the connection"
    
}
else {
    Write-Host "`nConfiguration update failed!" -ForegroundColor Red
    exit 1
}
