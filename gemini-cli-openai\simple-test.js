#!/usr/bin/env node

/**
 * 简单的模型测试
 */

const http = require('http');

// 获取当前API密钥
function getCurrentApiKey() {
  const fs = require('fs');
  try {
    if (fs.existsSync('./.dev.vars')) {
      const content = fs.readFileSync('./.dev.vars', 'utf8');
      const lines = content.split('\n');
      const apiKeyLine = lines.find(line => line.startsWith('OPENAI_API_KEY='));

      if (apiKeyLine) {
        return apiKeyLine.split('=')[1].trim();
      }
    }
    return 'sk-your-secret-api-key-here';
  } catch (error) {
    return 'sk-your-secret-api-key-here';
  }
}

async function simpleTest(model) {
  console.log(`\n🤖 测试模型: ${model}`);
  console.log('📝 发送消息: "你好"');

  const apiKey = getCurrentApiKey();
  console.log(`🔑 使用API密钥: ${apiKey}`);

  const payload = {
    model: model,
    messages: [{ role: 'user', content: '你好' }],
    stream: false,
    max_tokens: 100
  };

  const postData = JSON.stringify(payload);

  const options = {
    hostname: '127.0.0.1',
    port: 8787,
    path: '/v1/chat/completions',
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  return new Promise((resolve) => {
    const startTime = Date.now();
    
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        try {
          const response = JSON.parse(data);
          
          if (res.statusCode === 200) {
            const content = response.choices?.[0]?.message?.content || '无响应内容';
            const usage = response.usage;
            
            console.log('✅ 响应成功');
            console.log(`⏱️  响应时间: ${responseTime}ms`);
            console.log(`💬 响应内容: ${content}`);
            if (usage) {
              console.log(`📊 Token使用: ${usage.prompt_tokens} + ${usage.completion_tokens} = ${usage.total_tokens}`);
            }
            
            resolve({ success: true, model, responseTime, content, usage });
          } else {
            console.log('❌ 响应失败');
            console.log(`📄 状态码: ${res.statusCode}`);
            console.log(`📄 错误: ${JSON.stringify(response, null, 2)}`);
            resolve({ success: false, model, error: response });
          }
        } catch (error) {
          console.log('❌ 解析失败');
          console.log(`📄 原始响应: ${data}`);
          console.log(`📄 错误: ${error.message}`);
          resolve({ success: false, model, error: error.message });
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ 请求失败: ${error.message}`);
      resolve({ success: false, model, error: error.message });
    });

    req.setTimeout(30000, () => {
      console.log('❌ 请求超时');
      req.destroy();
      resolve({ success: false, model, error: '请求超时' });
    });

    req.write(postData);
    req.end();
  });
}

async function main() {
  console.log('🚀 简单模型测试');
  console.log('=' .repeat(40));

  const models = ['gemini-2.5-flash', 'gemini-2.5-pro'];
  const results = [];

  for (const model of models) {
    const result = await simpleTest(model);
    results.push(result);
    
    if (model !== models[models.length - 1]) {
      console.log('\n⏸️  等待2秒...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  // 总结
  console.log('\n' + '=' .repeat(40));
  console.log('📊 测试总结');
  console.log('=' .repeat(40));

  const successCount = results.filter(r => r.success).length;
  console.log(`✅ 成功: ${successCount}/${results.length} 个模型\n`);

  results.forEach(result => {
    if (result.success) {
      console.log(`🤖 ${result.model}:`);
      console.log(`   ⏱️  ${result.responseTime}ms`);
      console.log(`   📝 ${result.content?.length || 0} 字符`);
      if (result.usage) {
        console.log(`   📊 ${result.usage.total_tokens} tokens`);
      }
    } else {
      console.log(`❌ ${result.model}: 失败`);
    }
  });
}

if (require.main === module) {
  main().catch(console.error);
}
