import { <PERSON>o } from "hono";
import { Env } from "../types";
import { AuthManager } from "../auth";
import { GeminiApiClient } from "../gemini-client";

/**
 * Debug and testing routes for troubleshooting authentication and API functionality.
 */
export const DebugRoute = new Hono<{ Bindings: Env }>();

// Check KV cache status
DebugRoute.get("/cache", async (c) => {
	try {
		const authManager = new AuthManager(c.env);
		const cacheInfo = await authManager.getCachedTokenInfo();

		// Remove sensitive information from the response
		const sanitizedInfo = {
			status: "ok",
			cached: cacheInfo.cached,
			cached_at: cacheInfo.cached_at,
			expires_at: cacheInfo.expires_at,
			time_until_expiry_seconds: cacheInfo.time_until_expiry_seconds,
			is_expired: cacheInfo.is_expired,
			message: cacheInfo.message
			// Explicitly exclude token_preview and any other sensitive data
		};

		return c.json(sanitizedInfo);
	} catch (e: unknown) {
		const errorMessage = e instanceof Error ? e.message : String(e);
		return c.json(
			{
				status: "error",
				message: errorMessage
			},
			500
		);
	}
});

// Simple token test endpoint
DebugRoute.post("/token-test", async (c) => {
	try {
		console.log("Token test endpoint called");
		const authManager = new AuthManager(c.env);

		// Test authentication only
		await authManager.initializeAuth();
		console.log("Token test passed");

		return c.json({
			status: "ok",
			message: "Token authentication successful"
		});
	} catch (e: unknown) {
		const errorMessage = e instanceof Error ? e.message : String(e);
		console.error("Token test error:", e);
		return c.json(
			{
				status: "error",
				message: errorMessage
				// Removed stack trace for security
			},
			500
		);
	}
});

// Full functionality test endpoint
DebugRoute.post("/test", async (c) => {
	try {
		console.log("Test endpoint called");
		const authManager = new AuthManager(c.env);
		const geminiClient = new GeminiApiClient(c.env, authManager);

		// Test authentication
		await authManager.initializeAuth();
		console.log("Auth test passed");

		// Test project discovery
		const projectId = await geminiClient.discoverProjectId();
		console.log("Project discovery test passed");

		return c.json({
			status: "ok",
			message: "Authentication and project discovery successful",
			project_available: !!projectId
			// Removed actual projectId for security
		});
	} catch (e: unknown) {
		const errorMessage = e instanceof Error ? e.message : String(e);
		console.error("Test endpoint error:", e);
		return c.json(
			{
				status: "error",
				message: errorMessage
				// Removed stack trace and detailed error message for security
			},
			500
		);
	}
});

// API key validation endpoint for debugging authentication issues
DebugRoute.get("/api-key-check", async (c) => {
	try {
		console.log("API key check endpoint called");

		// Get server-side API key from environment
		const serverApiKey = c.env.OPENAI_API_KEY;

		// Get client-side API key from Authorization header
		const authHeader = c.req.header("Authorization");
		const clientApiKey = authHeader?.replace(/^Bearer\s+/, "") || null;

		// Perform comparison and generate diagnostic information
		const keysMatch = serverApiKey && clientApiKey && serverApiKey === clientApiKey;

		// Generate safe diagnostic information
		const diagnosticInfo = {
			status: "ok",
			keys_match: keysMatch,
			server_key_present: !!serverApiKey,
			client_key_present: !!clientApiKey,
			server_key_length: serverApiKey?.length || 0,
			client_key_length: clientApiKey?.length || 0,
			server_key_prefix: serverApiKey?.substring(0, 8) || "none",
			client_key_prefix: clientApiKey?.substring(0, 8) || "none",
			auth_header_format_valid: authHeader ? /^Bearer\s+.+$/.test(authHeader) : false,
			timestamp: new Date().toISOString()
		};

		console.log("API key check completed:", {
			keys_match: keysMatch,
			server_present: !!serverApiKey,
			client_present: !!clientApiKey
		});

		return c.json(diagnosticInfo);
	} catch (e: unknown) {
		const errorMessage = e instanceof Error ? e.message : String(e);
		console.error("API key check error:", e);
		return c.json(
			{
				status: "error",
				message: errorMessage,
				timestamp: new Date().toISOString()
			},
			500
		);
	}
});