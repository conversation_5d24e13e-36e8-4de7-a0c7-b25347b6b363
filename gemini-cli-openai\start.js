#!/usr/bin/env node

/**
 * Gemini CLI OpenAI 终极启动脚本
 * 自动检测OAuth过期、更新凭据、生成API密钥并启动服务器
 */

const fs = require('fs');
const path = require('path');
const { spawn, exec } = require('child_process');
const os = require('os');

class GeminiStarter {
    constructor() {
        this.windowsCredsPath = path.join(os.homedir(), '.gemini', 'oauth_creds.json');
        this.devVarsPath = './.dev.vars';
        this.serverProcess = null;
        this.monitorInterval = null;
        
        // 解析命令行参数
        this.options = this.parseArgs();
        
        // 绑定清理函数
        process.on('SIGINT', () => this.cleanup());
        process.on('SIGTERM', () => this.cleanup());
        process.on('exit', () => this.cleanup());
    }

    // 解析命令行参数
    parseArgs() {
        const args = process.argv.slice(2);
        return {
            help: args.includes('--help') || args.includes('-h'),
            force: args.includes('--force') || args.includes('-f'),
            skipOAuth: args.includes('--skip-oauth'),
            noMonitor: args.includes('--no-monitor'),
            verbose: args.includes('--verbose') || args.includes('-v')
        };
    }

    // 状态输出函数
    log(message, type = 'info') {
        const colors = {
            success: '\x1b[32m', // 绿色
            warning: '\x1b[33m', // 黄色
            error: '\x1b[31m',   // 红色
            info: '\x1b[36m',    // 青色
            debug: '\x1b[90m'    // 灰色
        };

        const prefixes = {
            success: '✅',
            warning: '⚠️ ',
            error: '❌',
            info: 'ℹ️ ',
            debug: '🔍'
        };

        const reset = '\x1b[0m';
        const timestamp = new Date().toLocaleTimeString();
        
        console.log(`${colors[type]}${prefixes[type]} [${timestamp}] ${message}${reset}`);
    }

    // 检查OAuth凭据状态
    async checkOAuthCredentials() {
        this.log('检查OAuth凭据状态...', 'info');

        if (!fs.existsSync(this.windowsCredsPath)) {
            this.log(`OAuth凭据文件不存在: ${this.windowsCredsPath}`, 'error');
            return { status: 'missing', message: '凭据文件不存在' };
        }

        try {
            const credsContent = fs.readFileSync(this.windowsCredsPath, 'utf8');
            const creds = JSON.parse(credsContent);
            const now = Date.now();
            const expiryDate = creds.expiry_date;

            if (!expiryDate) {
                this.log('凭据文件格式错误，缺少过期时间', 'error');
                return { status: 'invalid', message: '凭据格式错误' };
            }

            const timeLeft = expiryDate - now;
            const minutesLeft = Math.floor(timeLeft / 60000);

            if (timeLeft <= 0) {
                this.log('OAuth Token已过期', 'error');
                return { status: 'expired', message: 'Token已过期', minutesLeft };
            } else if (minutesLeft < 5) {
                this.log(`Token将在 ${minutesLeft} 分钟后过期`, 'warning');
                return { status: 'critical', message: '即将过期', minutesLeft };
            } else if (minutesLeft < 30) {
                this.log(`Token将在 ${minutesLeft} 分钟后过期`, 'warning');
                return { status: 'warning', message: '需要关注', minutesLeft };
            } else {
                const hoursLeft = Math.floor(minutesLeft / 60);
                this.log(`Token状态正常 (剩余 ${hoursLeft} 小时 ${minutesLeft % 60} 分钟)`, 'success');
                return { status: 'valid', message: 'Token有效', minutesLeft };
            }
        } catch (error) {
            this.log(`检查OAuth凭据失败: ${error.message}`, 'error');
            return { status: 'error', message: '检查失败' };
        }
    }

    // 运行Gemini CLI进行认证
    async runGeminiAuth() {
        this.log('启动Gemini CLI进行OAuth认证...', 'info');
        this.log('请在打开的窗口中选择 "Login with Google" 并完成认证', 'warning');

        return new Promise((resolve) => {
            const geminiProcess = spawn('gemini', [], {
                stdio: 'inherit',
                shell: true
            });

            geminiProcess.on('close', (code) => {
                if (code === 0) {
                    this.log('Gemini认证完成', 'success');
                    setTimeout(() => resolve(true), 2000);
                } else {
                    this.log(`Gemini认证失败，退出代码: ${code}`, 'error');
                    resolve(false);
                }
            });

            geminiProcess.on('error', (error) => {
                this.log(`启动Gemini CLI失败: ${error.message}`, 'error');
                this.log('请确保已安装Gemini CLI: npm install -g @google/gemini-cli', 'info');
                resolve(false);
            });
        });
    }

    // 更新项目OAuth凭据
    async updateProjectCredentials() {
        this.log('更新项目OAuth凭据...', 'info');

        if (!fs.existsSync(this.windowsCredsPath)) {
            this.log('OAuth凭据文件仍然不存在', 'error');
            return false;
        }

        try {
            if (fs.existsSync('./update_oauth_credentials.js')) {
                this.log('运行OAuth凭据更新脚本...', 'info');
                
                return new Promise((resolve) => {
                    const updateProcess = spawn('node', ['update_oauth_credentials.js', this.windowsCredsPath], {
                        stdio: 'inherit'
                    });

                    updateProcess.on('close', (code) => {
                        if (code === 0) {
                            this.log('OAuth凭据更新成功', 'success');
                            resolve(true);
                        } else {
                            this.log('OAuth凭据更新失败', 'error');
                            resolve(false);
                        }
                    });

                    updateProcess.on('error', (error) => {
                        this.log(`更新OAuth凭据时出错: ${error.message}`, 'error');
                        resolve(false);
                    });
                });
            } else {
                this.log('OAuth更新脚本不存在，跳过更新', 'warning');
                return true;
            }
        } catch (error) {
            this.log(`更新OAuth凭据时出错: ${error.message}`, 'error');
            return false;
        }
    }

    // 生成API密钥
    async generateApiKey() {
        this.log('生成新的API密钥...', 'info');

        try {
            if (fs.existsSync('./setup-key.ps1')) {
                return new Promise((resolve) => {
                    const keyProcess = spawn('powershell', ['-ExecutionPolicy', 'Bypass', '-File', 'setup-key.ps1'], {
                        stdio: 'inherit'
                    });

                    keyProcess.on('close', (code) => {
                        if (code === 0) {
                            this.log('API密钥生成成功', 'success');
                            resolve(true);
                        } else {
                            this.log('API密钥生成失败', 'error');
                            resolve(false);
                        }
                    });

                    keyProcess.on('error', (error) => {
                        this.log(`生成API密钥时出错: ${error.message}`, 'error');
                        resolve(false);
                    });
                });
            } else {
                this.log('API密钥生成脚本不存在', 'warning');
                return true;
            }
        } catch (error) {
            this.log(`生成API密钥时出错: ${error.message}`, 'error');
            return false;
        }
    }

    // 启动开发服务器
    async startDevServer() {
        this.log('启动Gemini CLI OpenAI开发服务器...', 'info');

        try {
            // 检查端口是否被占用
            await this.killExistingProcesses();

            this.log('执行: npm run dev', 'debug');
            this.serverProcess = spawn('npm', ['run', 'dev'], {
                stdio: 'inherit',
                shell: true
            });

            // 等待服务器启动
            this.log('等待服务器启动...', 'info');
            await new Promise(resolve => setTimeout(resolve, 5000));

            // 检查服务器是否正常运行
            try {
                const { exec } = require('child_process');
                await new Promise((resolve, reject) => {
                    exec('curl -s http://127.0.0.1:8787/v1/models', { timeout: 10000 }, (error, stdout, stderr) => {
                        if (error) {
                            reject(error);
                        } else {
                            resolve(stdout);
                        }
                    });
                });
                
                this.log('服务器启动成功！', 'success');
                this.log('本地地址: http://127.0.0.1:8787', 'info');
                this.log('网络地址: http://192.168.3.120:8787', 'info');
                return true;
            } catch (error) {
                this.log('服务器可能未完全启动，但进程已运行', 'warning');
                return true;
            }
        } catch (error) {
            this.log(`启动服务器失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 终止现有进程
    async killExistingProcesses() {
        try {
            if (process.platform === 'win32') {
                exec('taskkill /F /IM wrangler.exe 2>nul', () => {});
                exec('netstat -ano | findstr :8787', (error, stdout) => {
                    if (stdout) {
                        this.log('端口8787被占用，尝试终止现有进程...', 'warning');
                        const lines = stdout.split('\n');
                        lines.forEach(line => {
                            const match = line.match(/\s+(\d+)$/);
                            if (match) {
                                exec(`taskkill /F /PID ${match[1]} 2>nul`, () => {});
                            }
                        });
                    }
                });
            }
            await new Promise(resolve => setTimeout(resolve, 2000));
        } catch (error) {
            // 忽略错误
        }
    }

    // 启动OAuth状态监控
    startOAuthMonitor() {
        if (this.options.noMonitor) {
            return;
        }

        this.log('启动OAuth状态监控...', 'info');

        this.monitorInterval = setInterval(async () => {
            if (fs.existsSync(this.windowsCredsPath)) {
                try {
                    const credsContent = fs.readFileSync(this.windowsCredsPath, 'utf8');
                    const creds = JSON.parse(credsContent);
                    const now = Date.now();
                    const timeLeft = creds.expiry_date - now;
                    const minutesLeft = Math.floor(timeLeft / 60000);

                    if (minutesLeft < 10) {
                        this.log(`警告: OAuth Token将在 ${minutesLeft} 分钟后过期！`, 'warning');
                        this.log('建议运行: node start.js --force 更新凭据', 'warning');
                    }
                } catch (error) {
                    // 忽略监控错误
                }
            }
        }, 300000); // 每5分钟检查一次

        this.log('OAuth监控已启动 (后台运行)', 'success');
    }

    // 获取当前API密钥
    getCurrentApiKey() {
        try {
            if (fs.existsSync(this.devVarsPath)) {
                const content = fs.readFileSync(this.devVarsPath, 'utf8');
                const lines = content.split('\n');
                const apiKeyLine = lines.find(line => line.startsWith('OPENAI_API_KEY='));

                if (apiKeyLine) {
                    return apiKeyLine.split('=')[1].trim();
                }
            }
            return 'sk-your-secret-api-key-here';
        } catch (error) {
            return 'sk-your-secret-api-key-here';
        }
    }

    // 显示帮助信息
    showHelp() {
        console.log(`
🚀 Gemini CLI OpenAI 终极启动脚本

用法:
    node start.js [选项]

选项:
    --force, -f       强制重新认证，即使Token未过期
    --skip-oauth      跳过OAuth检查，直接启动服务器
    --no-monitor      不启动OAuth状态监控
    --verbose, -v     显示详细日志
    --help, -h        显示帮助信息

功能:
    ✅ 自动检测OAuth Token过期状态
    ✅ 过期时自动启动Gemini CLI认证
    ✅ 自动更新项目OAuth凭据
    ✅ 自动生成API密钥
    ✅ 启动开发服务器
    ✅ 后台监控OAuth状态

示例:
    node start.js              # 标准启动流程
    node start.js --force      # 强制重新认证
    node start.js --skip-oauth # 跳过OAuth检查
    node start.js --no-monitor # 不启动监控
`);
    }

    // 清理函数
    cleanup() {
        this.log('正在停止服务...', 'info');

        if (this.serverProcess && !this.serverProcess.killed) {
            this.serverProcess.kill('SIGTERM');
            this.log('服务器进程已停止', 'success');
        }

        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
        }

        this.log('清理完成', 'success');
    }

    // 主程序
    async run() {
        console.log(`
🚀 Gemini CLI OpenAI 终极启动脚本
================================`);

        // 检查是否在正确目录
        if (!fs.existsSync(this.devVarsPath)) {
            this.log('错误: 请在gemini-cli-openai项目目录中运行此脚本', 'error');
            this.log(`当前目录: ${process.cwd()}`, 'debug');
            process.exit(1);
        }

        if (this.options.help) {
            this.showHelp();
            process.exit(0);
        }

        try {
            // 1. 检查OAuth状态
            if (!this.options.skipOAuth) {
                const oauthStatus = await this.checkOAuthCredentials();

                if (this.options.force || ['missing', 'expired', 'critical'].includes(oauthStatus.status)) {
                    this.log('需要更新OAuth凭据', 'warning');

                    // 运行Gemini认证
                    if (!(await this.runGeminiAuth())) {
                        this.log('OAuth认证失败，无法继续', 'error');
                        process.exit(1);
                    }

                    // 更新项目凭据
                    if (!(await this.updateProjectCredentials())) {
                        this.log('更新项目凭据失败', 'error');
                        process.exit(1);
                    }
                } else {
                    this.log('OAuth凭据状态良好，继续启动...', 'success');
                }
            } else {
                this.log('跳过OAuth检查', 'warning');
            }

            // 2. 生成API密钥
            if (!(await this.generateApiKey())) {
                this.log('API密钥生成失败，但继续启动...', 'warning');
            }

            // 3. 启动服务器
            if (!(await this.startDevServer())) {
                this.log('服务器启动失败', 'error');
                process.exit(1);
            }

            // 4. 启动监控
            this.startOAuthMonitor();

            // 5. 显示完成信息
            this.log('🎉 启动完成！', 'success');

            // 获取当前API密钥
            const currentApiKey = this.getCurrentApiKey();

            console.log(`
📋 服务信息:
   🌐 本地地址: http://127.0.0.1:8787
   🌐 网络地址: http://192.168.3.120:8787
   🤖 推荐模型: gemini-2.5-flash

📱 Roo/Cline配置:
   • Base URL: http://127.0.0.1:8787/v1
   • API Key: ${currentApiKey}
   • Model: gemini-2.5-flash

⌨️  按 Ctrl+C 停止服务器`);

            // 保持脚本运行
            this.log('服务器正在运行，按 Ctrl+C 停止...', 'info');
            
            // 监控服务器进程
            if (this.serverProcess) {
                this.serverProcess.on('exit', (code) => {
                    this.log(`服务器进程意外退出，代码: ${code}`, 'error');
                    process.exit(1);
                });
            }

            // 保持进程运行
            await new Promise(() => {}); // 永远等待，直到被中断

        } catch (error) {
            this.log(`启动过程中发生错误: ${error.message}`, 'error');
            process.exit(1);
        }
    }
}

// 执行主程序
if (require.main === module) {
    const starter = new GeminiStarter();
    starter.run().catch(error => {
        console.error('启动失败:', error.message);
        process.exit(1);
    });
}

module.exports = GeminiStarter;
