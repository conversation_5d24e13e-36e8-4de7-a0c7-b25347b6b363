#!/usr/bin/env node

/**
 * 带OAuth监控的服务器启动脚本
 * 启动开发服务器的同时监控OAuth token状态
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class ServerWithMonitor {
  constructor() {
    this.serverProcess = null;
    this.monitorProcess = null;
    this.credsPath = 'I:/gemini/oauth_creds.json';
  }

  // 检查初始token状态
  checkInitialToken() {
    console.log('🔍 检查OAuth token状态...');
    
    try {
      if (!fs.existsSync(this.credsPath)) {
        console.log('❌ OAuth凭据文件不存在，请先运行: gemini');
        return false;
      }

      const creds = JSON.parse(fs.readFileSync(this.credsPath, 'utf8'));
      const now = Date.now();
      const timeLeft = creds.expiry_date - now;

      if (timeLeft <= 0) {
        console.log('🚨 OAuth Token已过期！');
        console.log('   请运行: gemini');
        console.log('   然后重新启动服务器');
        return false;
      }

      const minutes = Math.floor(timeLeft / 60000);
      const hours = Math.floor(minutes / 60);

      if (minutes < 30) {
        console.log(`⚠️  警告：Token将在 ${minutes} 分钟后过期`);
        console.log('   建议在服务器启动后及时更新token');
      } else {
        console.log(`✅ Token状态正常 (剩余 ${hours} 小时 ${minutes % 60} 分钟)`);
      }

      return true;
    } catch (error) {
      console.log('❌ 检查token失败:', error.message);
      return false;
    }
  }

  // 启动开发服务器
  startServer() {
    console.log('🚀 启动开发服务器...');
    
    this.serverProcess = spawn('npm', ['run', 'dev'], {
      stdio: 'inherit',
      shell: true
    });

    this.serverProcess.on('error', (error) => {
      console.error('❌ 服务器启动失败:', error.message);
    });

    this.serverProcess.on('exit', (code) => {
      console.log(`\n🛑 服务器已停止 (退出码: ${code})`);
      this.cleanup();
    });
  }

  // 启动token监控
  startMonitor() {
    console.log('👁️  启动OAuth token监控...');
    
    // 等待3秒让服务器先启动
    setTimeout(() => {
      this.monitorProcess = spawn('node', ['oauth_expiry_monitor.js', this.credsPath, '3'], {
        stdio: 'pipe',
        shell: true
      });

      this.monitorProcess.stdout.on('data', (data) => {
        const message = data.toString().trim();
        if (message) {
          console.log(`[监控] ${message}`);
        }
      });

      this.monitorProcess.stderr.on('data', (data) => {
        const message = data.toString().trim();
        if (message) {
          console.error(`[监控错误] ${message}`);
        }
      });

      this.monitorProcess.on('error', (error) => {
        console.error('❌ 监控启动失败:', error.message);
      });

    }, 3000);
  }

  // 清理资源
  cleanup() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
    
    if (this.monitorProcess) {
      this.monitorProcess.kill();
      this.monitorProcess = null;
    }
  }

  // 主启动流程
  start() {
    console.log('🎯 Gemini CLI OpenAI 服务器启动器');
    console.log('=' .repeat(50));

    // 1. 检查token状态
    if (!this.checkInitialToken()) {
      process.exit(1);
    }

    console.log('');

    // 2. 启动服务器
    this.startServer();

    // 3. 启动监控（如果监控脚本存在）
    if (fs.existsSync('oauth_expiry_monitor.js')) {
      this.startMonitor();
    } else {
      console.log('⚠️  OAuth监控脚本不存在，跳过监控启动');
    }

    // 4. 处理程序退出
    process.on('SIGINT', () => {
      console.log('\n🛑 正在停止服务器和监控...');
      this.cleanup();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 正在停止服务器和监控...');
      this.cleanup();
      process.exit(0);
    });

    // 5. 显示使用提示
    setTimeout(() => {
      console.log('\n📝 使用提示:');
      console.log('   - 服务器地址: http://127.0.0.1:8787');
      console.log('   - 按 Ctrl+C 停止服务器和监控');
      console.log('   - OAuth监控每3分钟检查一次token状态');
      console.log('   - 如收到过期警告，请及时更新token');
      console.log('');
    }, 5000);
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
📖 带OAuth监控的服务器启动器

用法:
  node start_with_monitor.js

功能:
  ✅ 启动前检查OAuth token状态
  ✅ 同时启动开发服务器和token监控
  ✅ 实时显示token到期提醒
  ✅ 优雅的程序退出处理

注意事项:
  - 确保OAuth凭据文件存在: I:/gemini/oauth_creds.json
  - 如果token已过期，请先运行 'gemini' 重新认证
  - 监控会每3分钟检查一次token状态
  - 按 Ctrl+C 可以同时停止服务器和监控

相关命令:
  gemini                           # 重新认证获取新token
  node quick_token_check.js        # 快速检查token状态
  node oauth_expiry_monitor.js     # 单独运行token监控
  npm run dev                      # 仅启动开发服务器
`);
}

// 主程序
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }
  
  const launcher = new ServerWithMonitor();
  launcher.start();
}

if (require.main === module) {
  main();
}

module.exports = ServerWithMonitor;
