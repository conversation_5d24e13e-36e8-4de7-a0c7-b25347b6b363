# Test API key validation endpoint
$headers = @{
    'Authorization' = 'Bearer sk-19pX9ydDx7T0i43HQKnQr3jv4cc28xwwFCIW9AZVuagAamIX'
}

try {
    $response = Invoke-RestMethod -Uri 'http://127.0.0.1:8788/v1/debug/api-key-check' -Method Get -Headers $headers -TimeoutSec 5
    Write-Host "API Key Check Results:"
    Write-Host "Keys Match: $($response.keys_match)"
    Write-Host "Server Key Present: $($response.server_key_present)"
    Write-Host "Client Key Present: $($response.client_key_present)"
    Write-Host "Server Key Length: $($response.server_key_length)"
    Write-Host "Client Key Length: $($response.client_key_length)"
    Write-Host "Server Key Prefix: $($response.server_key_prefix)"
    Write-Host "Client Key Prefix: $($response.client_key_prefix)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
}
