#!/usr/bin/env node

/**
 * 简单的API测试脚本
 */

const https = require('https');
const http = require('http');

async function testAPI() {
  console.log('🧪 测试Gemini CLI OpenAI API...\n');
  
  const options = {
    hostname: '127.0.0.1',
    port: 8787,
    path: '/v1/models',
    method: 'GET',
    headers: {
      'Authorization': 'Bearer sk-your-secret-api-key-here',
      'Content-Type': 'application/json'
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (res.statusCode === 200) {
            console.log('✅ API响应正常');
            console.log(`📋 可用模型: ${response.data.map(m => m.id).join(', ')}`);
            resolve(true);
          } else {
            console.log('❌ API测试失败:', response.error?.message || 'Unknown error');
            resolve(false);
          }
        } catch (error) {
          console.log('❌ 解析响应失败:', error.message);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ 连接失败:', error.message);
      resolve(false);
    });

    req.setTimeout(10000, () => {
      console.log('❌ 请求超时');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

if (require.main === module) {
  testAPI().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = testAPI;
