# Test actual chat API
$headers = @{
    'Authorization' = 'Bearer sk-19pX9ydDx7T0i43HQKnQr3jv4cc28xwwFCIW9AZVuagAamIX'
    'Content-Type' = 'application/json'
}

$body = @{
    model = 'gemini-2.5-flash'
    messages = @(
        @{
            role = 'user'
            content = 'Hello, please respond with just "API working!"'
        }
    )
    max_tokens = 10
} | ConvertTo-Json -Depth 3

try {
    Write-Host "Testing chat API..."
    $response = Invoke-RestMethod -Uri 'http://127.0.0.1:8788/v1/chat/completions' -Method Post -Headers $headers -Body $body -TimeoutSec 15
    Write-Host "Success! Response:"
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody"
    }
}
