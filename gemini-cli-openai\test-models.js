#!/usr/bin/env node

/**
 * 测试Gemini模型响应脚本
 * 分别测试gemini-2.5-pro和gemini-2.5-flash模型
 */

const http = require('http');

class ModelTester {
  constructor() {
    this.baseUrl = 'http://127.0.0.1:8787';
    this.apiKey = 'sk-your-secret-api-key-here';
    this.models = ['gemini-2.5-pro', 'gemini-2.5-flash'];
  }

  // 发送聊天请求
  async sendChatRequest(model, message) {
    const payload = {
      model: model,
      messages: [
        {
          role: 'user',
          content: message
        }
      ],
      stream: false // 使用非流式响应便于测试
    };

    const postData = JSON.stringify(payload);
    
    const options = {
      hostname: '127.0.0.1',
      port: 8787,
      path: '/v1/chat/completions',
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    return new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            resolve({
              statusCode: res.statusCode,
              response: response
            });
          } catch (error) {
            reject(new Error(`解析响应失败: ${error.message}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(new Error(`请求失败: ${error.message}`));
      });

      req.setTimeout(30000, () => {
        req.destroy();
        reject(new Error('请求超时'));
      });

      req.write(postData);
      req.end();
    });
  }

  // 测试单个模型
  async testModel(model, message) {
    console.log(`\n🤖 测试模型: ${model}`);
    console.log(`📝 发送消息: "${message}"`);
    console.log('⏳ 等待响应...\n');

    const startTime = Date.now();
    
    try {
      const result = await this.sendChatRequest(model, message);
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      if (result.statusCode === 200) {
        const response = result.response;
        const content = response.choices?.[0]?.message?.content || '无响应内容';
        const usage = response.usage;

        console.log('✅ 响应成功');
        console.log(`⏱️  响应时间: ${responseTime}ms`);
        console.log(`📊 Token使用: ${usage?.prompt_tokens || 0} prompt + ${usage?.completion_tokens || 0} completion = ${usage?.total_tokens || 0} total`);
        console.log(`💬 响应内容:\n${content}`);
        
        return {
          success: true,
          model: model,
          responseTime: responseTime,
          content: content,
          usage: usage
        };
      } else {
        console.log('❌ 响应失败');
        console.log(`📄 状态码: ${result.statusCode}`);
        console.log(`📄 错误信息: ${JSON.stringify(result.response, null, 2)}`);
        
        return {
          success: false,
          model: model,
          error: result.response
        };
      }
    } catch (error) {
      console.log('❌ 请求失败');
      console.log(`📄 错误信息: ${error.message}`);
      
      return {
        success: false,
        model: model,
        error: error.message
      };
    }
  }

  // 测试所有模型
  async testAllModels(message = '你好') {
    console.log('🚀 开始测试Gemini模型响应');
    console.log('=' .repeat(50));

    const results = [];
    
    for (const model of this.models) {
      const result = await this.testModel(model, message);
      results.push(result);
      
      // 在测试之间稍作延迟
      if (model !== this.models[this.models.length - 1]) {
        console.log('\n⏸️  等待2秒后测试下一个模型...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    // 显示总结
    console.log('\n' + '=' .repeat(50));
    console.log('📊 测试总结');
    console.log('=' .repeat(50));

    const successCount = results.filter(r => r.success).length;
    console.log(`✅ 成功: ${successCount}/${results.length} 个模型`);

    results.forEach(result => {
      if (result.success) {
        console.log(`\n🤖 ${result.model}:`);
        console.log(`   ⏱️  响应时间: ${result.responseTime}ms`);
        console.log(`   📊 Token使用: ${result.usage?.total_tokens || 0}`);
        console.log(`   💬 响应长度: ${result.content?.length || 0} 字符`);
      } else {
        console.log(`\n❌ ${result.model}: 测试失败`);
        console.log(`   📄 错误: ${typeof result.error === 'string' ? result.error : JSON.stringify(result.error)}`);
      }
    });

    return results;
  }
}

// 主程序
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
📖 Gemini模型测试工具

用法:
  node test-models.js [消息内容]

参数:
  消息内容    要发送给模型的测试消息 (默认: "你好")

示例:
  node test-models.js                    # 发送默认消息"你好"
  node test-models.js "解释什么是AI"      # 发送自定义消息

功能:
  ✅ 测试gemini-2.5-pro模型
  ✅ 测试gemini-2.5-flash模型
  ✅ 显示响应时间和Token使用情况
  ✅ 对比两个模型的性能
`);
    return;
  }

  const message = args[0] || '你好';
  const tester = new ModelTester();
  
  try {
    const results = await tester.testAllModels(message);
    const allSuccess = results.every(r => r.success);
    process.exit(allSuccess ? 0 : 1);
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = ModelTester;
