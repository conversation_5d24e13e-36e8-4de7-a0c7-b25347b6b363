# Test Roo issue - simple chat request
$headers = @{
    'Authorization' = 'Bearer sk-eTXRUZYLykgnNVz1NkTIJpkXXXKPd3Wh27Kibl2vsmHr9H3w'
    'Content-Type' = 'application/json'
}

$body = @{
    model = 'gemini-2.5-flash'
    messages = @(
        @{
            role = 'user'
            content = 'Hello, just say "Hi" back'
        }
    )
    max_tokens = 5
    stream = $false
} | ConvertTo-Json -Depth 3

Write-Host "Testing simple chat request..."
Write-Host "Body: $body"

try {
    $response = Invoke-RestMethod -Uri 'http://127.0.0.1:8787/v1/chat/completions' -Method Post -Headers $headers -Body $body -TimeoutSec 15
    Write-Host "Success! Response:"
    $response | ConvertTo-Json -Depth 5
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody"
    }
}
