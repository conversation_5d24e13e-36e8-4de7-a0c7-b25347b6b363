#!/usr/bin/env node

/**
 * 测试流式响应
 */

const http = require('http');

async function testStreamResponse(model, message) {
  console.log(`\n🌊 测试流式响应 - 模型: ${model}`);
  console.log(`📝 发送消息: "${message}"`);
  console.log('⏳ 等待流式响应...\n');

  const payload = {
    model: model,
    messages: [{ role: 'user', content: message }],
    stream: true
  };

  const postData = JSON.stringify(payload);
  
  const options = {
    hostname: '127.0.0.1',
    port: 8787,
    path: '/v1/chat/completions',
    method: 'POST',
    headers: {
      'Authorization': 'Bearer sk-your-secret-api-key-here',
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    let fullResponse = '';
    let chunkCount = 0;
    let usage = null;

    const req = http.request(options, (res) => {
      console.log(`📡 开始接收流式数据 (状态码: ${res.statusCode})\n`);
      
      res.on('data', (chunk) => {
        const data = chunk.toString();
        const lines = data.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ') && line !== 'data: [DONE]') {
            try {
              const jsonData = JSON.parse(line.substring(6));
              const delta = jsonData.choices?.[0]?.delta;
              
              if (delta?.content) {
                process.stdout.write(delta.content);
                fullResponse += delta.content;
                chunkCount++;
              }
              
              if (jsonData.usage) {
                usage = jsonData.usage;
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      });
      
      res.on('end', () => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        console.log('\n\n' + '='.repeat(50));
        console.log('📊 流式响应统计:');
        console.log(`⏱️  总响应时间: ${responseTime}ms`);
        console.log(`📦 接收块数: ${chunkCount}`);
        console.log(`📝 响应长度: ${fullResponse.length} 字符`);
        if (usage) {
          console.log(`🔢 Token使用: ${usage.prompt_tokens} prompt + ${usage.completion_tokens} completion = ${usage.total_tokens} total`);
        }
        
        resolve({
          success: true,
          model: model,
          responseTime: responseTime,
          content: fullResponse,
          chunkCount: chunkCount,
          usage: usage
        });
      });
    });

    req.on('error', (error) => {
      console.log(`❌ 请求失败: ${error.message}`);
      resolve({
        success: false,
        model: model,
        error: error.message
      });
    });

    req.setTimeout(60000, () => {
      console.log('❌ 请求超时');
      req.destroy();
      resolve({
        success: false,
        model: model,
        error: '请求超时'
      });
    });

    req.write(postData);
    req.end();
  });
}

async function main() {
  const args = process.argv.slice(2);
  const message = args[0] || '你好，请简单介绍一下你自己';
  
  console.log('🚀 开始测试流式响应');
  console.log('=' .repeat(50));

  // 测试两个模型
  const models = ['gemini-2.5-flash', 'gemini-2.5-pro'];
  const results = [];

  for (const model of models) {
    const result = await testStreamResponse(model, message);
    results.push(result);
    
    if (model !== models[models.length - 1]) {
      console.log('\n⏸️  等待3秒后测试下一个模型...\n');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }

  // 显示总结
  console.log('\n' + '='.repeat(50));
  console.log('📊 流式测试总结');
  console.log('=' .repeat(50));

  results.forEach(result => {
    if (result.success) {
      console.log(`\n✅ ${result.model}:`);
      console.log(`   ⏱️  响应时间: ${result.responseTime}ms`);
      console.log(`   📦 流式块数: ${result.chunkCount}`);
      console.log(`   📝 响应长度: ${result.content?.length || 0} 字符`);
      if (result.usage) {
        console.log(`   🔢 Token使用: ${result.usage.total_tokens}`);
      }
    } else {
      console.log(`\n❌ ${result.model}: ${result.error}`);
    }
  });
}

if (require.main === module) {
  main().catch(console.error);
}
