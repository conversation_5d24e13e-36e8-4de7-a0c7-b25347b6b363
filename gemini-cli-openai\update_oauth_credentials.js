#!/usr/bin/env node

/**
 * OAuth凭据自动更新工具
 * 自动从Windows Gemini CLI路径更新OAuth凭据到.dev.vars
 *
 * 使用方法:
 * node update_oauth_credentials.js [选项]
 *
 * 功能:
 * - 自动检测Windows Gemini CLI凭据路径
 * - 清理Unicode字符避免ByteString错误
 * - 自动更新.dev.vars文件
 * - 清理无用的脚本文件
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

class AutoOAuthUpdater {
  constructor() {
    this.devVarsPath = './.dev.vars';
    this.backupDir = './oauth_backups';
    this.windowsCredsPath = path.join(os.homedir(), '.gemini', 'oauth_creds.json');
    this.legacyCredsPath = 'I:/gemini/oauth_creds.json';
    this.obsoleteScripts = [
      '../clean_oauth_credentials.js',
      './scripts/check-oauth.js',
      './scripts/start-with-oauth-check.js'
    ];
  }

  // 自动检测凭据文件路径
  detectCredentialsPath() {
    console.log('🔍 自动检测OAuth凭据文件...');

    const possiblePaths = [
      this.windowsCredsPath,
      this.legacyCredsPath,
      './oauth_creds.json',
      '../oauth_creds.json'
    ];

    for (const credPath of possiblePaths) {
      if (fs.existsSync(credPath)) {
        console.log(`✅ 找到凭据文件: ${credPath}`);
        return credPath;
      }
    }

    console.log('❌ 未找到OAuth凭据文件');
    console.log('📝 请确保已运行 gemini 命令进行认证');
    console.log('   预期路径:');
    possiblePaths.forEach(p => console.log(`   - ${p}`));
    return null;
  }

  // 清理字符串中的非ASCII字符
  cleanString(str) {
    if (typeof str !== 'string') return str;
    return str.replace(/[^\x00-\x7F]/g, '?');
  }

  // 递归清理对象中的所有字符串
  cleanObject(obj) {
    if (typeof obj === 'string') {
      return this.cleanString(obj);
    } else if (Array.isArray(obj)) {
      return obj.map(item => this.cleanObject(item));
    } else if (obj !== null && typeof obj === 'object') {
      const cleaned = {};
      for (const [key, value] of Object.entries(obj)) {
        cleaned[key] = this.cleanObject(value);
      }
      return cleaned;
    }
    return obj;
  }

  // 创建备份目录
  ensureBackupDir() {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
      console.log(`📁 创建备份目录: ${this.backupDir}`);
    }
  }

  // 验证并清理凭据文件
  validateAndCleanCredentials(credsPath) {
    try {
      console.log(`🔍 验证凭据文件: ${credsPath}`);

      if (!fs.existsSync(credsPath)) {
        throw new Error(`凭据文件不存在: ${credsPath}`);
      }

      const credsContent = fs.readFileSync(credsPath, 'utf8');
      const rawCreds = JSON.parse(credsContent);

      // 清理Unicode字符
      console.log('🧹 清理Unicode字符...');
      const creds = this.cleanObject(rawCreds);

      const requiredFields = [
        'access_token', 'refresh_token', 'scope',
        'token_type', 'id_token', 'expiry_date'
      ];

      const missingFields = [];
      for (const field of requiredFields) {
        if (!creds[field]) {
          missingFields.push(field);
        }
      }

      if (missingFields.length > 0) {
        throw new Error(`缺少必需字段: ${missingFields.join(', ')}`);
      }

      // 检查token过期时间
      const now = Date.now();
      const expiryDate = creds.expiry_date;

      if (expiryDate < now) {
        const expiredTime = new Date(expiryDate).toLocaleString();
        console.log(`⚠️  警告: Token已过期 (过期时间: ${expiredTime})`);
        console.log('   建议重新运行 gemini 命令获取新token');
      } else {
        const timeLeft = Math.floor((expiryDate - now) / 1000 / 60);
        console.log(`✅ Token有效 (剩余时间: ${timeLeft}分钟)`);
      }

      console.log('✅ 凭据格式验证通过');
      return creds;

    } catch (error) {
      console.error('❌ 凭据验证失败:', error.message);
      return null;
    }
  }

  // 备份当前配置
  backupCurrentConfig() {
    try {
      if (!fs.existsSync(this.devVarsPath)) {
        console.log('⚠️  .dev.vars文件不存在，跳过备份');
        return null;
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = path.join(this.backupDir, `.dev.vars.backup.${timestamp}`);
      
      fs.copyFileSync(this.devVarsPath, backupPath);
      console.log(`💾 配置已备份到: ${backupPath}`);
      return backupPath;
      
    } catch (error) {
      console.error('❌ 备份失败:', error.message);
      return null;
    }
  }

  // 清理无用脚本文件
  cleanupObsoleteScripts() {
    console.log('🧹 清理无用脚本文件...');

    let cleanedCount = 0;
    for (const scriptPath of this.obsoleteScripts) {
      try {
        if (fs.existsSync(scriptPath)) {
          // 备份到备份目录
          const scriptName = path.basename(scriptPath);
          const backupPath = path.join(this.backupDir, `${scriptName}.backup`);
          fs.copyFileSync(scriptPath, backupPath);

          // 删除原文件
          fs.unlinkSync(scriptPath);
          console.log(`🗑️  已删除: ${scriptPath} (备份到: ${backupPath})`);
          cleanedCount++;
        }
      } catch (error) {
        console.log(`⚠️  无法删除 ${scriptPath}: ${error.message}`);
      }
    }

    if (cleanedCount > 0) {
      console.log(`✅ 已清理 ${cleanedCount} 个无用脚本文件`);
    } else {
      console.log('✅ 没有发现需要清理的脚本文件');
    }
  }

  // 更新环境变量文件
  updateDevVars(credentials) {
    try {
      console.log('🔄 更新环境变量文件...');

      // 将凭据转换为单行JSON
      const credsJson = JSON.stringify(credentials);

      if (!fs.existsSync(this.devVarsPath)) {
        // 如果.dev.vars不存在，从示例文件复制
        const examplePath = './.dev.vars.example';
        if (fs.existsSync(examplePath)) {
          fs.copyFileSync(examplePath, this.devVarsPath);
          console.log('📋 从示例文件创建.dev.vars');
        } else {
          // 创建基本的.dev.vars文件
          const basicContent = `# Gemini CLI OpenAI Worker Environment Variables

# Required: OAuth2 credentials JSON from Gemini CLI authentication
GCP_SERVICE_ACCOUNT=${credsJson}

# Optional: API key for authentication (if not set, API is public)
OPENAI_API_KEY=sk-your-secret-api-key-here

# Optional: Enable fake thinking output for thinking models
ENABLE_FAKE_THINKING=true

# Optional: Enable real Gemini thinking output
ENABLE_REAL_THINKING=true

# Optional: Stream thinking as content with <thinking> tags
STREAM_THINKING_AS_CONTENT=true

# Optional: Auto switch from Pro to flash when rate-limited
ENABLE_AUTO_MODEL_SWITCHING=true
`;
          fs.writeFileSync(this.devVarsPath, basicContent);
          console.log('📋 创建新的.dev.vars文件');
          return true;
        }
      }

      // 读取当前配置
      let content = fs.readFileSync(this.devVarsPath, 'utf8');

      // 替换GCP_SERVICE_ACCOUNT行
      const gcpServiceAccountRegex = /^GCP_SERVICE_ACCOUNT=.*$/m;
      const newLine = `GCP_SERVICE_ACCOUNT=${credsJson}`;

      if (gcpServiceAccountRegex.test(content)) {
        content = content.replace(gcpServiceAccountRegex, newLine);
      } else {
        // 如果没找到GCP_SERVICE_ACCOUNT行，添加到文件开头
        content = `${newLine}\n\n${content}`;
      }

      // 写入更新后的内容
      fs.writeFileSync(this.devVarsPath, content);
      console.log('✅ 环境变量已更新');

      return true;

    } catch (error) {
      console.error('❌ 更新环境变量失败:', error.message);
      return false;
    }
  }

  // 测试新凭据
  async testCredentials() {
    console.log('🧪 测试新凭据...');
    
    try {
      // 检查服务器是否运行
      const fetch = require('node-fetch');
      const response = await fetch('http://127.0.0.1:8787/health', {
        timeout: 5000
      });
      
      if (response.ok) {
        console.log('✅ 服务器响应正常');
        
        // 测试API功能
        const modelsResponse = await fetch('http://127.0.0.1:8787/v1/models', {
          headers: {
            'Authorization': 'Bearer sk-your-secret-api-key-here'
          },
          timeout: 10000
        });
        
        if (modelsResponse.ok) {
          const models = await modelsResponse.json();
          console.log('✅ API功能正常');
          console.log(`📋 可用模型: ${models.data.map(m => m.id).join(', ')}`);
        } else {
          console.log('⚠️  API测试失败，可能需要重启服务器');
        }
      } else {
        console.log('⚠️  服务器未运行，请启动服务器测试新凭据');
      }
      
    } catch (error) {
      console.log('⚠️  无法连接到服务器，请手动启动服务器测试');
      console.log('   运行: cd gemini-cli-openai && npm run dev');
    }
  }

  // 主要更新流程
  async update(credsPath = null) {
    console.log('🚀 开始OAuth凭据自动更新流程...\n');

    // 1. 确保备份目录存在
    this.ensureBackupDir();

    // 2. 自动检测或使用指定的凭据路径
    const detectedPath = credsPath || this.detectCredentialsPath();
    if (!detectedPath) {
      return false;
    }

    // 3. 验证并清理新凭据
    const credentials = this.validateAndCleanCredentials(detectedPath);
    if (!credentials) {
      return false;
    }

    // 4. 备份当前配置
    this.backupCurrentConfig();

    // 5. 清理无用脚本
    this.cleanupObsoleteScripts();

    // 6. 更新环境变量
    const updateSuccess = this.updateDevVars(credentials);
    if (!updateSuccess) {
      return false;
    }

    // 7. 测试新凭据
    await this.testCredentials();

    console.log('\n🎉 OAuth凭据自动更新完成！');
    console.log('📝 后续步骤:');
    console.log('   1. 重启服务器: npm run dev');
    console.log('   2. 测试API功能');
    console.log('   3. 如有问题，可从备份恢复配置');
    console.log('\n📋 更新内容:');
    console.log(`   ✅ 凭据来源: ${detectedPath}`);
    console.log('   ✅ Unicode字符已清理');
    console.log('   ✅ 环境变量已更新');
    console.log('   ✅ 无用脚本已清理');

    return true;
  }

  // 显示帮助信息
  showHelp() {
    console.log(`
📖 OAuth凭据自动更新工具使用说明

用法:
  node update_oauth_credentials.js [选项] [凭据文件路径]

选项:
  --help, -h      显示帮助信息
  --clean         仅清理无用脚本文件

参数:
  凭据文件路径    OAuth凭据JSON文件的路径 (可选)
                 默认自动检测: ${this.windowsCredsPath}

示例:
  node update_oauth_credentials.js                    # 自动检测并更新
  node update_oauth_credentials.js --clean            # 仅清理无用脚本
  node update_oauth_credentials.js custom_creds.json  # 使用指定凭据文件

功能:
  ✅ 自动检测Windows Gemini CLI凭据路径
  ✅ 清理Unicode字符避免ByteString错误
  ✅ 验证OAuth凭据格式和有效期
  ✅ 自动备份当前配置
  ✅ 更新.dev.vars环境变量文件
  ✅ 清理无用的脚本文件
  ✅ 测试新凭据功能
  ✅ 提供详细的操作反馈

自动检测路径:
  1. ${this.windowsCredsPath}
  2. ${this.legacyCredsPath}
  3. ./oauth_creds.json
  4. ../oauth_creds.json

清理的无用脚本:
  - ../clean_oauth_credentials.js
  - ./scripts/check-oauth.js
  - ./scripts/start-with-oauth-check.js

注意事项:
  - 运行前请确保在gemini-cli-openai项目目录中
  - 更新后需要重启服务器以应用新凭据
  - 所有操作都会自动备份，可安全恢复
`);
  }
}

// 主程序
async function main() {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    new AutoOAuthUpdater().showHelp();
    return;
  }

  const updater = new AutoOAuthUpdater();

  // 处理仅清理选项
  if (args.includes('--clean')) {
    console.log('🧹 仅执行清理无用脚本...\n');
    updater.ensureBackupDir();
    updater.cleanupObsoleteScripts();
    console.log('\n✅ 清理完成！');
    return;
  }

  // 获取凭据路径参数（排除选项参数）
  const credsPath = args.find(arg => !arg.startsWith('--')) || null;

  try {
    const success = await updater.update(credsPath);
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ 更新过程中发生错误:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = AutoOAuthUpdater;
