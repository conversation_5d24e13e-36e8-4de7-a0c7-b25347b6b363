#!/usr/bin/env node

/**
 * OAuth凭据验证工具
 * 用于验证Gemini CLI OpenAI项目的OAuth凭据格式和有效性
 * 
 * 使用方法:
 * node validate_oauth.js [凭据文件路径]
 * 
 * 示例:
 * node validate_oauth.js I:/gemini/oauth_creds.json
 */

const fs = require('fs');

function validateOAuthCreds(credsPath) {
  try {
    console.log('🔍 验证OAuth凭据...');
    console.log(`📁 文件路径: ${credsPath}`);
    
    // 检查文件是否存在
    if (!fs.existsSync(credsPath)) {
      console.log(`❌ 错误: 凭据文件不存在: ${credsPath}`);
      return false;
    }
    
    // 读取并解析JSON
    const credsContent = fs.readFileSync(credsPath, 'utf8');
    let creds;
    
    try {
      creds = JSON.parse(credsContent);
    } catch (parseError) {
      console.log('❌ 错误: JSON格式无效');
      console.log('   ', parseError.message);
      return false;
    }
    
    // 检查必需字段
    const requiredFields = [
      'access_token', 'refresh_token', 'scope', 
      'token_type', 'id_token', 'expiry_date'
    ];
    
    console.log('\n📋 字段检查:');
    let allFieldsPresent = true;
    
    for (const field of requiredFields) {
      if (!creds[field]) {
        console.log(`❌ 缺少必需字段: ${field}`);
        allFieldsPresent = false;
      } else {
        // 显示字段存在状态和部分内容（安全起见）
        let displayValue = creds[field];
        if (typeof displayValue === 'string' && displayValue.length > 20) {
          displayValue = displayValue.substring(0, 20) + '...';
        }
        console.log(`✅ ${field}: ${displayValue}`);
      }
    }
    
    if (!allFieldsPresent) {
      console.log('\n❌ 凭据验证失败: 缺少必需字段');
      return false;
    }
    
    // 检查token类型
    if (creds.token_type !== 'Bearer') {
      console.log(`⚠️  警告: token_type应为"Bearer"，当前为"${creds.token_type}"`);
    }
    
    // 检查scope
    const requiredScopes = [
      'https://www.googleapis.com/auth/cloud-platform',
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/userinfo.profile'
    ];
    
    console.log('\n🔐 权限范围检查:');
    const currentScopes = creds.scope.split(' ');
    
    for (const requiredScope of requiredScopes) {
      if (currentScopes.includes(requiredScope)) {
        console.log(`✅ ${requiredScope}`);
      } else {
        console.log(`❌ 缺少权限: ${requiredScope}`);
      }
    }
    
    // 检查token过期时间
    console.log('\n⏰ Token有效性检查:');
    const now = Date.now();
    const expiryDate = creds.expiry_date;
    const timeLeft = expiryDate - now;
    
    if (timeLeft <= 0) {
      const expiredTime = new Date(expiryDate).toLocaleString();
      console.log(`❌ Token已过期`);
      console.log(`   过期时间: ${expiredTime}`);
      console.log(`   建议运行: gemini 重新认证`);
      return false;
    } else {
      const expireTime = new Date(expiryDate).toLocaleString();
      console.log(`✅ Token有效`);
      console.log(`   过期时间: ${expireTime}`);
      
      // 计算剩余时间
      const secondsLeft = Math.floor(timeLeft / 1000);
      const minutesLeft = Math.floor(secondsLeft / 60);
      const hoursLeft = Math.floor(minutesLeft / 60);
      const daysLeft = Math.floor(hoursLeft / 24);
      
      if (daysLeft > 0) {
        console.log(`   剩余时间: ${daysLeft}天 ${hoursLeft % 24}小时`);
      } else if (hoursLeft > 0) {
        console.log(`   剩余时间: ${hoursLeft}小时 ${minutesLeft % 60}分钟`);
      } else {
        console.log(`   剩余时间: ${minutesLeft}分钟`);
      }
      
      // 过期警告
      if (timeLeft < 3600000) { // 1小时
        console.log(`⚠️  警告: Token将在1小时内过期，建议更新`);
      } else if (timeLeft < 86400000) { // 24小时
        console.log(`⚠️  提醒: Token将在24小时内过期`);
      }
    }
    
    // 文件权限检查（Unix系统）
    if (process.platform !== 'win32') {
      try {
        const stats = fs.statSync(credsPath);
        const mode = stats.mode & parseInt('777', 8);
        const modeStr = mode.toString(8);
        
        console.log('\n🔒 文件权限检查:');
        console.log(`   当前权限: ${modeStr}`);
        
        if (mode & parseInt('044', 8)) { // 检查是否其他用户可读
          console.log(`⚠️  警告: 文件对其他用户可读，建议设置为600`);
          console.log(`   运行: chmod 600 ${credsPath}`);
        } else {
          console.log(`✅ 文件权限安全`);
        }
      } catch (error) {
        console.log('⚠️  无法检查文件权限');
      }
    }
    
    console.log('\n🎉 OAuth凭据验证通过！');
    return true;
    
  } catch (error) {
    console.log('❌ 验证过程中发生错误:', error.message);
    return false;
  }
}

function showHelp() {
  console.log(`
📖 OAuth凭据验证工具使用说明

用法:
  node validate_oauth.js [凭据文件路径]

参数:
  凭据文件路径    OAuth凭据JSON文件的路径 (可选)
                 默认: I:/gemini/oauth_creds.json

示例:
  node validate_oauth.js
  node validate_oauth.js I:/gemini/oauth_creds.json
  node validate_oauth.js ~/.gemini/oauth_creds.json

检查项目:
  ✅ 文件存在性
  ✅ JSON格式有效性
  ✅ 必需字段完整性
  ✅ Token有效期
  ✅ 权限范围
  ✅ 文件安全权限

返回值:
  0 - 验证通过
  1 - 验证失败
`);
}

// 主程序
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }
  
  const credsPath = args[0] || 'I:/gemini/oauth_creds.json';
  
  try {
    const isValid = validateOAuthCreds(credsPath);
    process.exit(isValid ? 0 : 1);
  } catch (error) {
    console.error('❌ 验证过程中发生未预期错误:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { validateOAuthCreds };
