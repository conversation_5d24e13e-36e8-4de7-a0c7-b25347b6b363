# 🚀 Gemini CLI OpenAI 使用教程

## 📋 目录
- [快速开始](#快速开始)
- [认证方式选择](#认证方式选择)
- [API Key方式 (推荐)](#api-key方式-推荐)
- [OAuth方式](#oauth方式)
- [启动服务器](#启动服务器)
- [常用命令](#常用命令)
- [故障排除](#故障排除)

---

## 🎯 快速开始

### 1. 配置认证
```bash
# 交互式配置 (推荐新手)
npm run setup

# 或者直接配置API Key
node setup-auth.js --api-key YOUR_API_KEY
```

### 2. 启动服务器
```bash
# 快速启动 (推荐)
npm start

# 或者使用原生命令
npm run dev
```

### 3. 使用服务
- 🌐 **服务地址**: http://127.0.0.1:8787
- 🤖 **默认模型**: gemini-2.5-flash
- 📚 **API文档**: http://127.0.0.1:8787/v1/models

---

## 🔐 认证方式选择

### 📊 对比表格

| 特性 | API Key (推荐) | OAuth |
|------|---------------|-------|
| **过期时间** | 🟢 永不过期 | 🟡 每小时过期 |
| **维护成本** | 🟢 零维护 | 🔴 需要定期刷新 |
| **稳定性** | 🟢 极高 | 🟡 中等 |
| **配置难度** | 🟢 简单 | 🟡 中等 |
| **功能完整性** | 🟢 完整 | 🟢 完整 |

**💡 建议**: 优先选择API Key方式，除非有特殊需求。

---

## 🔑 API Key方式 (推荐)

### 获取API Key
1. 访问 [Google AI Studio](https://aistudio.google.com/app/apikey)
2. 登录Google账号
3. 点击"Create API Key"
4. 复制生成的API Key

### 配置方法

#### 方法1: 交互式配置
```bash
npm run setup
# 选择 "1. API Key (推荐 - 永不过期)"
# 输入你的API Key
```

#### 方法2: 命令行配置
```bash
node setup-auth.js --api-key AIzaSyDznvinSFtDL8VXi2EukEUo3UFxUexs5YA
```

### 验证配置
```bash
npm run status
```

**输出示例**:
```
📊 当前认证配置状态:
============================================================
✅ 认证方式: API Key (推荐)
🔑 Gemini API Key: AIzaSyDznvinSFtDL8VX...
⏰ 过期时间: 🟢 永不过期
🔄 刷新需求: 无需刷新
💡 优势: 稳定可靠，无需维护
🔗 OpenAI兼容密钥: sk-eTXRUZYLykgn...
📅 检查时间: 2025/7/30 11:01:57
🌐 服务地址: http://127.0.0.1:8787
🤖 默认模型: gemini-2.5-flash
```

---

## 🔄 OAuth方式

### 适用场景
- 需要使用企业Google账号
- 有特殊的权限要求
- API Key方式不可用时

### 配置步骤

#### 1. 获取OAuth凭据
```bash
# 运行Gemini CLI认证
gemini auth
# 按提示完成Google账号登录
```

#### 2. 配置OAuth
```bash
# 交互式配置
npm run setup
# 选择 "2. OAuth (每小时需要刷新)"

# 或命令行配置
node setup-auth.js --oauth "C:\Users\<USER>\.gemini\oauth_creds.json"
```

### 验证配置
```bash
npm run status
```

**输出示例**:
```
📊 当前认证配置状态:
============================================================
✅ 认证方式: OAuth
⏰ 过期时间: 2025/7/30 12:20:14
⏳ 剩余时间: 🟢 1小时18分钟
🔄 刷新需求: 每小时自动刷新
💡 提示: 如遇问题可运行 node setup-auth.js --oauth
🔗 OpenAI兼容密钥: sk-eTXRUZYLykgn...
📅 检查时间: 2025/7/30 11:01:57
🌐 服务地址: http://127.0.0.1:8787
🤖 默认模型: gemini-2.5-flash
```

---

## 🚀 启动服务器

### 推荐方式: 快速启动
```bash
npm start
```

**启动输出示例**:
```
🚀 Gemini CLI OpenAI 快速启动
==================================================
🔍 认证状态检查:
✅ 认证方式: API Key
🔑 API Key: AIzaSyDznvinSFtDL8VX...
⏰ 过期时间: 🟢 永不过期
📅 检查时间: 2025/7/30 11:01:57
🔄 启动wrangler dev服务器...
📍 服务地址: http://127.0.0.1:8787
🤖 默认模型: gemini-2.5-flash
```

### 其他启动方式
```bash
# 原生wrangler启动
npm run dev

# 高级启动脚本 (包含监控等功能)
npm run advanced
```

---

## 📝 常用命令

### 认证管理
```bash
npm run setup          # 交互式配置认证
npm run status          # 查看认证状态
```

### 服务器管理
```bash
npm start              # 快速启动 (推荐)
npm run dev            # 原生启动
npm run advanced       # 高级启动
```

### 配置管理
```bash
# API Key配置
node setup-auth.js --api-key YOUR_API_KEY

# OAuth配置
node setup-auth.js --oauth PATH_TO_OAUTH_FILE

# 查看状态
node setup-auth.js --status
```

---

## 🔧 故障排除

### 常见问题

#### 1. "Cannot read properties of undefined"
**原因**: 认证token过期或无效
**解决**:
```bash
# 检查认证状态
npm run status

# 重新配置认证
npm run setup
```

#### 2. API返回空内容
**原因**: OAuth token过期
**解决**:
```bash
# 切换到API Key方式 (推荐)
node setup-auth.js --api-key YOUR_API_KEY

# 或更新OAuth
gemini auth
node setup-auth.js --oauth "C:\Users\<USER>\.gemini\oauth_creds.json"
```

#### 3. 服务器启动失败
**原因**: 端口被占用或配置错误
**解决**:
```bash
# 检查端口占用
netstat -ano | findstr :8787

# 重新配置
npm run setup
```

### 获取帮助
```bash
node setup-auth.js --help      # 认证配置帮助
node quick-start.js --help     # 启动脚本帮助
```

---

## 🎉 完成！

现在你可以：
1. ✅ 使用稳定的API Key认证 (推荐)
2. ✅ 或使用OAuth认证 (高级用户)
3. ✅ 快速启动服务器
4. ✅ 查看详细的过期时间信息
5. ✅ 享受稳定的Gemini API服务

**服务地址**: http://127.0.0.1:8787  
**默认模型**: gemini-2.5-flash  
**API兼容**: OpenAI格式
