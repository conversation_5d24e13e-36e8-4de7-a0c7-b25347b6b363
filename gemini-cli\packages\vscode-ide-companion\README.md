# Gemini CLI Companion

The Gemini CLI Companion extension seamlessly integrates [Gemini CLI](https://github.com/google-gemini/gemini-cli) into your VS Code environment. This integration enables Gemini CLI to access active information like your open files.

# Features

- Open Editor File Context: Gemini CLI gains awareness of the files you have open in your editor, providing it with a richer understanding of your project's structure and content.

- Selection Context: Gemini CLI can easily access your cursor's position and selected text within the editor, giving it valuable context directly from your current work.

# Requirements

To use this extension, you'll need:

- VS Code version 1.101.0 or newer
- Gemini CLI (installed separately) running within the VS Code integrated terminal

# Terms of Service and Privacy Notice

By installing this extension, you agree to the [Terms of Service](https://github.com/google-gemini/gemini-cli/blob/main/docs/tos-privacy.md).
