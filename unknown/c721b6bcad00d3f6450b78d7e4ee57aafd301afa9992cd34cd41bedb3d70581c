# 🚀 Gemini CLI OpenAI Worker - 中文详细说明

[!["Buy Me A Coffee"](https://www.buymeacoffee.com/assets/img/custom_images/orange_img.png)](https://www.buymeacoffee.com/mrproper)

使用Cloudflare Workers将Google的Gemini模型转换为OpenAI兼容的端点。通过OAuth2认证和与官方Gemini CLI相同的基础设施，以熟悉的OpenAI API模式访问Google最先进的AI模型。

## ✨ 主要特性

- 🔐 **OAuth2认证** - 无需API密钥，使用您的Google账户
- 🎯 **OpenAI兼容API** - 可直接替换OpenAI端点
- 📚 **OpenAI SDK支持** - 兼容官方OpenAI SDK和库
- 🖼️ **视觉支持** - 支持图像的多模态对话（base64和URL）
- 🔧 **工具调用支持** - 与Gemini API集成的函数调用
- 🧠 **高级推理** - 支持Gemini的思维能力和努力控制
- 🛡️ **内容安全** - 可配置的Gemini审核设置
- 🌐 **第三方集成** - 兼容Open WebUI、ChatGPT客户端等
- ⚡ **Cloudflare Workers** - 全球边缘部署，低延迟
- 🔄 **智能Token缓存** - 使用KV存储的智能token管理
- 🆓 **免费层访问** - 通过Code Assist API利用Google的免费层
- 📡 **实时流式传输** - 带token使用情况的实时响应服务器发送事件
- 🎭 **多种模型** - 访问最新的Gemini模型，包括实验性模型

## 🤖 支持的模型

| 模型ID | 上下文窗口 | 最大Token | 思维支持 | 描述 |
|--------|------------|-----------|----------|------|
| `gemini-2.5-pro` | 1M | 65K | ✅ | 具有推理能力的最新Gemini 2.5 Pro模型 |
| `gemini-2.5-flash` | 1M | 65K | ✅ | 具有推理能力的快速Gemini 2.5 Flash模型 |

> **注意**: Gemini 2.5模型默认启用思维功能。API自动管理：
> - 当禁用真实思维时（环境变量），思维预算设置为0以禁用
> - 当启用真实思维时（环境变量），思维预算默认为-1（由Gemini动态分配）

**思维支持**有两种模式：
- **虚假思维**: 设置`ENABLE_FAKE_THINKING=true`生成合成推理文本（适合测试）
- **真实思维**: 设置`ENABLE_REAL_THINKING=true`使用Gemini的原生推理能力

真实思维完全由`ENABLE_REAL_THINKING`环境变量控制。您可以选择在请求中设置`"thinking_budget"`（推理的token限制，-1为动态分配，0为完全禁用思维）。

- **推理努力支持**: 您可以通过在请求体中包含`reasoning_effort`来控制思维模型的推理努力程度（例如`extra_body`或`model_params`）。此参数允许您微调模型的内部推理过程，在速度和思维深度之间取得平衡。
  - `none`: 禁用思维（`thinking_budget = 0`）
  - `low`: 设置`thinking_budget = 1024`
  - `medium`: Flash模型设置`thinking_budget = 12288`，其他模型设置`16384`
  - `high`: Flash模型设置`thinking_budget = 24576`，其他模型设置`32768`

> 设置`STREAM_THINKING_AS_CONTENT=true`以DeepSeek R1风格将推理作为带有`<thinking>`标签的内容流式传输，而不是使用推理字段。

## 🛠️ 安装设置

### 前提条件

1. **Google账户**，可访问Gemini
2. **Cloudflare账户**，启用Workers
3. **Wrangler CLI**已安装（`npm install -g wrangler`）

### 步骤1：获取OAuth2凭据

您需要从已访问Gemini的Google账户获取OAuth2凭据。最简单的方法是通过官方Gemini CLI。

#### 使用Gemini CLI

1. **安装Gemini CLI**:
   ```bash
   npm install -g @google/gemini-cli
   ```

2. **启动Gemini CLI**:
   ```bash
   gemini
   ```

3. **使用Google认证**:
   选择`● Login with Google`。
   浏览器窗口将打开，提示您使用Google账户登录。

4. **找到凭据文件**:
   **Windows:**
   ```
   C:\Users\<USER>\.gemini\oauth_creds.json
   ```
   
   **macOS/Linux:**
   ```
   ~/.gemini/oauth_creds.json
   ```

5. **复制凭据**: 文件包含此格式的JSON:
   ```json
   {
     "access_token": "ya29.a0AS3H6Nx...",
     "refresh_token": "1//09FtpJYpxOd...",
     "scope": "https://www.googleapis.com/auth/cloud-platform ...",
     "token_type": "Bearer",
     "id_token": "eyJhbGciOiJSUzI1NiIs...",
     "expiry_date": *************
   }
   ```

### 步骤2：创建KV命名空间

```bash
# 为token缓存创建KV命名空间
wrangler kv namespace create "GEMINI_CLI_KV"
```

记下返回的命名空间ID。在`wrangler.toml`中更新您的KV命名空间ID：

```toml
kv_namespaces = [
  { binding = "GEMINI_CLI_KV", id = "your-kv-namespace-id" }
]
```

### 步骤3：环境设置

创建`.dev.vars`文件：

```bash
# 必需：来自Gemini CLI认证的OAuth2凭据JSON
GCP_SERVICE_ACCOUNT={"access_token":"ya29...","refresh_token":"1//...","scope":"...","token_type":"Bearer","id_token":"eyJ...","expiry_date":*************}

# 可选：Google Cloud项目ID（如果未设置则自动发现）
# GEMINI_PROJECT_ID=your-project-id

# 可选：用于认证的API密钥（如果未设置，API为公开）
# 设置后，客户端必须包含"Authorization: Bearer <your-api-key>"头
# 示例：sk-1234567890abcdef1234567890abcdef
OPENAI_API_KEY=sk-your-secret-api-key-here
```

对于生产环境，设置密钥：

```bash
wrangler secret put GCP_SERVICE_ACCOUNT
wrangler secret put OPENAI_API_KEY  # 可选，仅在需要认证时
```

### 步骤4：部署

```bash
# 安装依赖
npm install

# 部署到Cloudflare Workers
npm run deploy

# 或本地开发运行
npm run dev
```

## 🔧 配置

### 环境变量

| 变量 | 必需 | 描述 |
|------|------|------|
| `GCP_SERVICE_ACCOUNT` | ✅ | OAuth2凭据JSON字符串 |
| `GEMINI_PROJECT_ID` | ❌ | Google Cloud项目ID（如果未设置则自动发现） |
| `OPENAI_API_KEY` | ❌ | 用于认证的API密钥（如果未设置，API为公开） |
| `ENABLE_FAKE_THINKING` | ❌ | 为思维模型启用合成思维输出（设置为"true"启用） |
| `ENABLE_REAL_THINKING` | ❌ | 启用真实Gemini思维输出（设置为"true"启用） |
| `STREAM_THINKING_AS_CONTENT` | ❌ | 以`<thinking>`标签形式流式传输思维内容（DeepSeek R1风格） |
| `ENABLE_AUTO_MODEL_SWITCHING` | ❌ | 在速率限制时启用从pro到flash模型的自动回退（设置为"true"启用） |

**认证安全**：
- 设置`OPENAI_API_KEY`时，所有`/v1/*`端点都需要认证
- 客户端必须包含头：`Authorization: Bearer <your-api-key>`
- 没有此环境变量时，API为公开访问
- 推荐格式：`sk-`后跟随机字符串（例如`sk-1234567890abcdef...`）

**思维模型**：
- **虚假思维**: 当`ENABLE_FAKE_THINKING`设置为"true"时，标记为`thinking: true`的模型将在实际响应前生成合成推理文本
- **真实思维**: 当`ENABLE_REAL_THINKING`设置为"true"时，带有`include_reasoning: true`的请求将使用Gemini的原生思维能力
- 真实思维提供来自Gemini的真实推理，需要支持思维的模型（如Gemini 2.5 Pro/Flash）
- 您可以使用`thinking_budget`参数控制推理token预算
- 默认情况下，推理输出作为OpenAI兼容响应格式中的`reasoning`块流式传输
- 当`STREAM_THINKING_AS_CONTENT`也设置为"true"时，推理将作为包装在`<thinking></thinking>`标签中的常规内容流式传输（DeepSeek R1风格）
- **优化用户体验**: `</thinking>`标签仅在实际LLM响应开始时发送，消除思维和响应之间的尴尬停顿
- 如果两种思维模式都未启用，思维模型将表现得像常规模型

**自动模型切换**：
- 当`ENABLE_AUTO_MODEL_SWITCHING`设置为"true"时，系统将在遇到速率限制错误（HTTP 429或503）时自动从`gemini-2.5-pro`回退到`gemini-2.5-flash`
- 这在Pro模型配额耗尽时提供无缝连续性
- 回退在响应中用通知消息指示
- 仅适用于支持的模型对（当前：pro → flash）
- 适用于流式和非流式请求

### KV命名空间

| 绑定 | 用途 |
|------|------|
| `GEMINI_CLI_KV` | Token缓存和会话管理 |

## 💻 使用示例

### OpenAI SDK (Python)

```python
from openai import OpenAI

# 使用您的worker端点初始化
client = OpenAI(
    base_url="https://your-worker.workers.dev/v1",
    api_key="sk-your-secret-api-key-here"  # 如果启用认证，使用您的OPENAI_API_KEY
)

# 聊天完成
response = client.chat.completions.create(
    model="gemini-2.5-flash",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Explain machine learning in simple terms"}
    ],
    stream=True
)

for chunk in response:
    if chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="")

# 真实思维模式
response = client.chat.completions.create(
    model="gemini-2.5-pro",
    messages=[
        {"role": "user", "content": "Solve this step by step: What is the derivative of x^3 + 2x^2 - 5x + 3?"}
    ],
    extra_body={
        "include_reasoning": True,
        "thinking_budget": 1024
    },
    stream=True
)

for chunk in response:
    # 真实思维出现在推理字段中
    if hasattr(chunk.choices[0].delta, 'reasoning') and chunk.choices[0].delta.reasoning:
        print(f"[Thinking] {chunk.choices[0].delta.reasoning}")
    if chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="")
```

### OpenAI SDK (JavaScript/TypeScript)

```javascript
import OpenAI from 'openai';

const openai = new OpenAI({
  baseURL: 'https://your-worker.workers.dev/v1',
  apiKey: 'sk-your-secret-api-key-here', // 如果启用认证，使用您的OPENAI_API_KEY
});

const stream = await openai.chat.completions.create({
  model: 'gemini-2.5-flash',
  messages: [
    { role: 'user', content: 'Write a haiku about coding' }
  ],
  stream: true,
});

for await (const chunk of stream) {
  const content = chunk.choices[0]?.delta?.content || '';
  process.stdout.write(content);
}
```

### cURL

```bash
curl -X POST https://your-worker.workers.dev/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-secret-api-key-here" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [
      {"role": "user", "content": "Explain quantum computing"}
    ]
  }'
```

### 原生JavaScript/TypeScript

```javascript
const response = await fetch('https://your-worker.workers.dev/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    model: 'gemini-2.5-flash',
    messages: [
      { role: 'user', content: 'Hello, world!' }
    ]
  })
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;

  const chunk = decoder.decode(value);
  const lines = chunk.split('\n');

  for (const line of lines) {
    if (line.startsWith('data: ') && line !== 'data: [DONE]') {
      const data = JSON.parse(line.substring(6));
      const content = data.choices[0]?.delta?.content;
      if (content) {
        console.log(content);
      }
    }
  }
}
```

### 原生Python（不使用SDK）

```python
import requests
import json

url = "https://your-worker.workers.dev/v1/chat/completions"
data = {
    "model": "gemini-2.5-flash",
    "messages": [
        {"role": "user", "content": "Write a Python function to calculate fibonacci"}
    ]
}

response = requests.post(url, json=data, stream=True)

for line in response.iter_lines():
    if line and line.startswith(b'data: '):
        try:
            chunk = json.loads(line[6:].decode())
            content = chunk['choices'][0]['delta'].get('content', '')
            if content:
                print(content, end='')
        except json.JSONDecodeError:
            continue
```

## 🔧 工具调用支持

Worker支持OpenAI兼容的工具调用（函数调用），与Gemini的函数调用能力无缝集成。

### 使用工具调用

在请求中包含`tools`和可选的`tool_choice`：

```javascript
const response = await fetch('/v1/chat/completions', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    model: 'gemini-2.5-pro',
    messages: [
      { role: 'user', content: 'What is the weather in New York?' }
    ],
    tools: [
      {
        type: 'function',
        function: {
          name: 'get_weather',
          description: 'Get weather information for a location',
          parameters: {
            type: 'object',
            properties: {
              location: { type: 'string', description: 'City name' }
            },
            required: ['location']
          }
        }
      }
    ],
    tool_choice: 'auto'
  })
});
```

### 工具选择选项

- `auto`: 让模型决定是否调用函数
- `none`: 禁用函数调用
- `{"type": "function", "function": {"name": "function_name"}}`: 强制特定函数调用

## 🖼️ 图像支持（视觉）

Worker支持具有视觉能力的模型进行图像的多模态对话。图像可以作为base64编码的数据URL或外部URL提供。

### 支持的图像格式

- JPEG、PNG、GIF、WebP
- Base64编码（推荐以确保可靠性）
- 外部URL（某些服务可能有限制）

### 支持视觉的模型

- `gemini-2.5-pro`
- `gemini-2.5-flash`
- `gemini-2.0-flash-001`
- `gemini-2.0-flash-lite-preview-02-05`
- `gemini-2.0-pro-exp-02-05`

### Base64图像示例

```python
from openai import OpenAI
import base64

# 编码您的图像
with open("image.jpg", "rb") as image_file:
    base64_image = base64.b64encode(image_file.read()).decode('utf-8')

client = OpenAI(
    base_url="https://your-worker.workers.dev/v1",
    api_key="sk-your-secret-api-key-here"
)

response = client.chat.completions.create(
    model="gemini-2.5-flash",
    messages=[
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "What do you see in this image?"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_image}"
                    }
                }
            ]
        }
    ]
)

print(response.choices[0].message.content)
```

### 图像URL示例

```bash
curl -X POST https://your-worker.workers.dev/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-secret-api-key-here" \
  -d '{
    "model": "gemini-2.5-pro",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "Describe this image in detail."
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "https://example.com/image.jpg",
              "detail": "high"
            }
          }
        ]
      }
    ]
  }'
```

### 多图像支持

您可以在单个消息中包含多个图像：

```json
{
  "model": "gemini-2.5-pro",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Compare these two images."
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,..."
          }
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/png;base64,..."
          }
        }
      ]
    }
  ]
}
```

## 🛡️ 内容安全设置

使用dev.vars中的环境变量配置Gemini的内置安全过滤器：

```bash
# 安全阈值选项：BLOCK_NONE, BLOCK_FEW, BLOCK_SOME, BLOCK_ONLY_HIGH, HARM_BLOCK_THRESHOLD_UNSPECIFIED
GEMINI_MODERATION_HARASSMENT_THRESHOLD=BLOCK_NONE
GEMINI_MODERATION_HATE_SPEECH_THRESHOLD=BLOCK_NONE
GEMINI_MODERATION_SEXUALLY_EXPLICIT_THRESHOLD=BLOCK_SOME
GEMINI_MODERATION_DANGEROUS_CONTENT_THRESHOLD=BLOCK_ONLY_HIGH
```

**安全类别**：
- `HARASSMENT`: 促进对个人/群体仇恨或暴力的内容
- `HATE_SPEECH`: 针对特定群体的贬损或诋毁语言
- `SEXUALLY_EXPLICIT`: 包含性或成人材料的内容
- `DANGEROUS_CONTENT`: 促进危险或有害活动的内容

## 📡 API端点

### 基础URL

```
https://your-worker.your-subdomain.workers.dev
```

### 列出模型

```http
GET /v1/models
```

**响应**：

```json
{
  "object": "list",
  "data": [
    {
      "id": "gemini-2.5-pro",
      "object": "model",
      "created": 1708976947,
      "owned_by": "google-gemini-cli"
    }
  ]
}
```

### 聊天完成

```http
POST /v1/chat/completions
Content-Type: application/json

{
  "model": "gemini-2.5-flash",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello! How are you?"
    }
  ]
}
```

#### 思维模式（真实推理）

对于支持思维的模型，您可以启用来自Gemini的真实推理：

```http
POST /v1/chat/completions
Content-Type: application/json

{
  "model": "gemini-2.5-pro",
  "messages": [
    {
      "role": "user",
      "content": "Solve this math problem step by step: What is 15% of 240?"
    }
  ],
  "include_reasoning": true,
  "thinking_budget": 1024
}
```

`include_reasoning`参数启用Gemini的原生思维模式，`thinking_budget`设置推理的token限制。

**响应（流式）**：

```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1708976947,"model":"gemini-2.5-flash","choices":[{"index":0,"delta":{"role":"assistant","content":"Hello"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1708976947,"model":"gemini-2.5-flash","choices":[{"index":0,"delta":{"content":"! I'm"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1708976947,"model":"gemini-2.5-flash","choices":[{"index":0,"delta":{},"finish_reason":"stop"}],"usage":{"prompt_tokens":22,"completion_tokens":553,"total_tokens":575}}

data: [DONE]
```

### 调试端点

#### 检查Token缓存

```http
GET /v1/debug/cache
```

#### 测试认证

```http
POST /v1/token-test
POST /v1/test
```

## 🐳 Docker开发指南

### 快速开始

1. **克隆并设置项目**：
   ```bash
   git clone https://github.com/GewoonJaap/gemini-cli-openai
   cd gemini-cli-openai
   ```

2. **创建环境文件**：
   ```bash
   cp .dev.vars.example .dev.vars
   # 编辑.dev.vars添加您的实际凭据
   ```

3. **启动开发环境**：
   ```bash
   npm run docker:dev
   ```

4. **测试API**：
   ```bash
   curl http://localhost:8787/health
   curl http://localhost:8787/v1/models
   ```

### 可用的Docker命令

| 命令 | 描述 |
|------|------|
| `npm run docker:build` | 构建Docker镜像 |
| `npm run docker:dev` | 构建并启动并显示日志（推荐用于开发） |
| `npm run docker:start` | 启动现有容器 |
| `npm run docker:stop` | 停止运行的容器 |
| `npm run docker:clean` | 停止容器并删除卷（清除KV数据） |
| `npm run docker:logs` | 查看容器日志 |
| `npm run docker:shell` | 在容器内打开shell |

### 架构说明

- **容器**: Alpine Linux with Node.js 20
- **Web框架**: Hono运行在Cloudflare Workers运行时（通过miniflare）
- **开发服务器**: Wrangler dev with miniflare用于本地模拟
- **存储**: KV数据持久化到Docker卷以保持数据持久性

### 环境变量

您的`.dev.vars`文件会被容器内的`wrangler dev`自动加载。支持以下变量：

- `GCP_SERVICE_ACCOUNT`: 必需的Google OAuth凭据
- `GEMINI_PROJECT_ID`: 可选的Google Cloud项目ID
- `ENABLE_REAL_THINKING`: 启用真实思维模式
- `ENABLE_FAKE_THINKING`: 启用虚假思维用于测试
- `STREAM_THINKING_AS_CONTENT`: 将思维作为内容流式传输
- `OPENAI_API_KEY`: 可选的API密钥用于认证

### 持久化数据

KV数据存储在名为`gemini_kv_data`的Docker卷中，在容器重启时保持持久。要清除所有数据：

```bash
npm run docker:clean
```

## 🚨 故障排除

### 常见问题

**401认证错误**
- 检查您的OAuth2凭据是否有效
- 确保刷新token正常工作
- 验证凭据格式完全匹配

**Token刷新失败**
- 凭据可能来自错误的OAuth2客户端
- 刷新token可能已过期或被撤销
- 检查调试缓存端点的token状态

**项目ID发现失败**
- 手动设置`GEMINI_PROJECT_ID`环境变量
- 确保您的Google账户可以访问Gemini

**容器无法启动**
1. **检查Docker是否运行**：
   ```bash
   docker --version
   docker-compose --version
   ```

2. **检查端口冲突**：
   ```bash
   lsof -i :8787  # 检查端口8787是否被占用
   ```

3. **查看详细日志**：
   ```bash
   docker-compose up --build
   ```

**环境变量不工作**
1. **验证.dev.vars存在**：
   ```bash
   ls -la .dev.vars
   ```

2. **检查.dev.vars格式**：
   - `=`周围没有空格
   - 除非是值的一部分，否则不要使用引号
   - JSON值应该在一行上

3. **在容器内测试环境**：
   ```bash
   npm run docker:shell
   cat .dev.vars
   ```

## 🏗️ 工作原理

Worker充当翻译层，将OpenAI API调用转换为Google的Code Assist API格式，同时自动管理OAuth2认证。

```mermaid
graph TD
    A[客户端请求] --> B[Cloudflare Worker]
    B --> C{KV缓存中有Token?}
    C -->|是| D[使用缓存Token]
    C -->|否| E[检查环境Token]
    E --> F{Token有效?}
    F -->|是| G[缓存并使用Token]
    F -->|否| H[刷新Token]
    H --> I[缓存新Token]
    D --> J[调用Gemini API]
    G --> J
    I --> J
    J --> K[流式响应]
    K --> L[OpenAI格式]
    L --> M[客户端响应]
```

## 🤝 贡献

1. Fork仓库
2. 创建功能分支
3. 进行更改
4. 彻底测试
5. 提交拉取请求

## 📄 许可证

此项目根据MIT许可证授权 - 有关详细信息，请参阅LICENSE文件。

## 🙏 致谢

- 受官方[Google Gemini CLI](https://github.com/google-gemini/gemini-cli)启发
- 基于[Cloudflare Workers](https://workers.cloudflare.com/)构建
- 使用[Hono](https://hono.dev/) web框架

---

**⚠️ 重要提示**: 此项目使用Google的Code Assist API，可能有使用限制和服务条款。使用此worker时请确保遵守Google的政策。

## 📊 Star历史

[![Star History Chart](https://api.star-history.com/svg?repos=GewoonJaap/gemini-cli-openai&type=Date)](https://www.star-history.com/#GewoonJaap/gemini-cli-openai&Date)
