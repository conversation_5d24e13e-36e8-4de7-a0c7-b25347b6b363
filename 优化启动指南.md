# 🚀 Gemini CLI OpenAI 优化启动指南

## 🎯 新的启动方式

我们已经为您的项目添加了智能启动系统，现在您可以使用更简单、更安全的方式来管理和启动项目。

## 📋 可用的npm命令

### 🚀 启动命令
```bash
# 推荐：智能启动（自动检查OAuth + 监控）
npm start

# 启动但不监控OAuth
npm run start:no-monitor

# 原始启动方式（不推荐日常使用）
npm run dev
```

### 🔍 检查命令
```bash
# 检查OAuth token状态
npm run check

# 静默检查（用于脚本）
npm run check:silent
```

### 🔄 更新命令
```bash
# 更新OAuth凭据
npm run update-oauth

# 指定凭据文件路径更新
npm run update-oauth path/to/oauth_creds.json
```

## ✨ 智能启动特性

### 1. 自动OAuth检查
启动前会自动检查您的OAuth token状态：

- ✅ **Token正常** (> 30分钟): 直接启动，每5分钟检查一次
- ⏰ **即将过期** (30分钟内): 警告提示，每2分钟检查一次  
- 🚨 **紧急状态** (5分钟内): 等待确认，每1分钟检查一次
- ❌ **已过期/不存在**: 阻止启动，提示先更新token

### 2. 实时监控
根据token状态智能调整监控频率，在即将过期时自动提醒。

### 3. 优雅处理
- 自动备份配置文件
- 智能错误提示
- 安全的程序退出

## 🎮 实际使用示例

### 日常启动（推荐）
```bash
npm start
```

**输出示例**:
```
🎯 Gemini CLI OpenAI 智能启动器
==================================================
⏰ 提醒：Token将在 0 小时 33 分钟后过期

🚀 启动Gemini CLI OpenAI开发服务器...
👁️  启动OAuth监控 (每5分钟检查一次)...

📝 服务器信息:
   🌐 本地地址: http://127.0.0.1:8787
   🌐 网络地址: http://192.168.3.120:8787
   ⌨️  按 Ctrl+C 停止服务器
   👁️  OAuth监控: 每5分钟检查一次
```

### 检查token状态
```bash
npm run check
```

**输出示例**:
```
⏰ Token将在 0h33m后过期
```

### 当token即将过期时
系统会自动提醒，您需要：

1. **重新认证**:
   ```bash
   gemini
   # 选择 "Login with Google"
   ```

2. **更新配置**:
   ```bash
   npm run update-oauth
   ```

3. **重启服务器**:
   ```bash
   npm start
   ```

## 🔧 高级功能

### 1. 自动预检查
每次运行 `npm start` 时会自动执行 `npm run check:silent` 进行预检查。

### 2. 配置备份
更新OAuth时会自动备份当前配置到 `oauth_backups/` 目录。

### 3. 智能文件查找
更新工具会自动在以下位置查找OAuth凭据：
- `I:/gemini/oauth_creds.json`
- `~/.gemini/oauth_creds.json` (Linux/macOS)
- `C:\Users\<USER>\.gemini\oauth_creds.json` (Windows)

## 📊 与原方式对比

| 功能 | 原方式 | 新方式 | 优势 |
|------|--------|--------|------|
| 启动服务器 | `npm run dev` | `npm start` | 自动OAuth检查 |
| 检查token | 手动查看文件 | `npm run check` | 标准化检查 |
| 更新OAuth | 手动编辑配置 | `npm run update-oauth` | 自动化流程 |
| 监控token | 无 | 自动监控 | 主动提醒 |
| 错误处理 | 手动排查 | 智能提示 | 快速定位问题 |

## 🎯 最佳实践建议

### 1. 日常开发
- 始终使用 `npm start` 而不是 `npm run dev`
- 每天开始工作时先运行 `npm run check`
- 收到过期警告时及时更新token

### 2. Token管理
- 定期检查token状态
- 及时响应过期提醒
- 保持OAuth凭据文件的安全

### 3. 故障处理
- 启动失败时先运行 `npm run check`
- 使用 `npm run update-oauth` 快速更新配置
- 必要时使用 `npm run start:no-monitor` 跳过监控

## 🚨 常见问题解决

### Q: 启动时提示token过期怎么办？
```bash
# 1. 重新认证
gemini

# 2. 更新配置
npm run update-oauth

# 3. 重新启动
npm start
```

### Q: 监控功能不需要怎么办？
```bash
# 使用无监控模式
npm run start:no-monitor

# 或使用原始方式
npm run dev
```

### Q: 找不到OAuth凭据文件怎么办？
```bash
# 先运行Gemini CLI认证
gemini

# 然后更新配置
npm run update-oauth

# 或手动指定文件路径
npm run update-oauth path/to/your/oauth_creds.json
```

## 🎉 总结

通过这些优化的启动脚本，您现在可以：

- ✅ **更安全**: 自动检查OAuth状态，避免服务中断
- ✅ **更智能**: 根据token状态调整监控策略
- ✅ **更简单**: 一个命令完成启动和监控
- ✅ **更可靠**: 自动备份和错误恢复机制

**推荐从现在开始使用 `npm start` 作为您的默认启动命令！**

---

## 📁 相关文件

- `scripts/start-with-oauth-check.js` - 智能启动器
- `scripts/check-oauth.js` - OAuth检查工具  
- `scripts/update-oauth.js` - OAuth更新工具
- `scripts/README.md` - 详细技术文档
- `oauth_backups/` - 自动备份目录

有任何问题或建议，请随时反馈！
