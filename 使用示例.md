# OAuth到期提醒工具使用示例

## 🎯 您当前的情况

根据刚才的检查，您的OAuth token将在约39分钟后过期。以下是具体的使用建议：

## 🚀 立即可用的命令

### 1. 快速检查当前token状态
```bash
node quick_token_check.js I:/gemini/oauth_creds.json
```
**当前输出**: `⏰ 提醒：Token将在 0 小时 39 分钟后过期`

### 2. 启动实时监控（推荐）
```bash
# 每分钟检查一次（因为即将过期）
node oauth_expiry_monitor.js I:/gemini/oauth_creds.json 1
```

### 3. 带监控启动服务器
```bash
# 停止当前服务器（Ctrl+C）
# 然后运行：
node start_with_monitor.js
```

## ⚠️ 紧急处理流程

由于您的token将在39分钟后过期，建议按以下步骤操作：

### 步骤1：启动监控
```bash
# 在新的终端窗口中运行
node oauth_expiry_monitor.js I:/gemini/oauth_creds.json 1
```

### 步骤2：准备更新token
```bash
# 当收到5分钟警告时，立即运行：
gemini
# 选择 "Login with Google" 重新认证
```

### 步骤3：更新项目配置
```bash
# 认证完成后，复制新凭据
cp ~/.gemini/oauth_creds.json I:/gemini/oauth_creds.json

# 或者在Windows中：
# copy C:\Users\<USER>\.gemini\oauth_creds.json I:\gemini\oauth_creds.json

# 然后重启服务器
# 按 Ctrl+C 停止当前服务器
npm run dev
```

## 📊 监控输出解读

### 正常状态
```
✅ [时间] OAuth Token状态正常，剩余时间: X小时 Y分钟
```
**操作**: 无需处理

### 即将过期（您当前的状态）
```
⏰ [时间] OAuth Token将在 39分钟 后过期
```
**操作**: 准备更新，继续监控

### 警告状态
```
⚠️ [时间] OAuth Token将在 25分钟 后过期，建议准备更新
```
**操作**: 开始准备更新流程

### 紧急状态
```
🚨 [时间] 紧急：OAuth Token将在 5分钟 后过期！请立即更新
```
**操作**: 立即运行 `gemini` 重新认证

## 🔧 实用技巧

### 1. 后台运行监控
```bash
# Linux/macOS
nohup node oauth_expiry_monitor.js I:/gemini/oauth_creds.json 1 > oauth_monitor.log 2>&1 &

# Windows PowerShell
Start-Process node -ArgumentList "oauth_expiry_monitor.js","I:/gemini/oauth_creds.json","1" -WindowStyle Hidden
```

### 2. 设置提醒音（可选）
在监控脚本中添加声音提醒：
```bash
# Windows
echo "^G" # 系统提示音

# Linux
echo -e "\a" # 终端提示音
```

### 3. 创建快捷脚本
```bash
# 创建 check.bat (Windows) 或 check.sh (Linux)
echo "node quick_token_check.js I:/gemini/oauth_creds.json" > check.bat
```

## 📱 移动端提醒（高级）

如果您希望在手机上收到提醒，可以集成以下服务：

### 1. 邮件通知
```javascript
// 在监控脚本中添加邮件发送功能
const nodemailer = require('nodemailer');

// 配置邮件发送
const transporter = nodemailer.createTransporter({
  // 邮件服务器配置
});

// 发送紧急提醒邮件
if (timeLeft < 5 * 60 * 1000) {
  await transporter.sendMail({
    to: '<EMAIL>',
    subject: 'OAuth Token即将过期',
    text: `您的Gemini OAuth token将在${minutes}分钟后过期，请立即更新！`
  });
}
```

### 2. 微信/钉钉机器人通知
```javascript
// 通过webhook发送消息到群聊
const webhook_url = 'your-webhook-url';
fetch(webhook_url, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    msgtype: 'text',
    text: { content: `OAuth Token即将过期：${timeLeftStr}` }
  })
});
```

## 🎯 最佳实践建议

### 1. 日常使用
- 每天启动开发环境时运行一次 `node quick_token_check.js`
- 使用 `node start_with_monitor.js` 替代 `npm run dev`
- 设置token过期前1小时的提醒

### 2. 团队协作
- 将监控脚本加入项目文档
- 在README中添加token管理说明
- 建立token更新的标准流程

### 3. 自动化
- 设置定时任务检查token状态
- 配置CI/CD流水线检查token有效性
- 建立token轮换的自动化流程

## 🆘 紧急联系

如果遇到问题：
1. 检查网络连接
2. 确认Google账户状态
3. 验证凭据文件格式
4. 重新运行 `gemini` 命令
5. 查看服务器日志输出

---

**当前建议**: 由于您的token将在39分钟后过期，建议立即启动监控并准备更新流程！
