# 🚀 Gemini CLI OpenAI 快速配置参考

## 📋 基本信息

### 服务器配置
```
Base URL: http://127.0.0.1:8787/v1
API Key: sk-your-secret-api-key-here
Models: gemini-2.5-pro, gemini-2.5-flash
```

### 启动命令
```bash
npm start              # 智能启动（推荐）
npm run check          # 检查OAuth状态
npm run update-oauth   # 更新OAuth凭据
```

## 🔧 AI插件配置模板

### Cline (VS Code)
```json
{
  "apiProvider": "openai-compatible",
  "baseUrl": "http://127.0.0.1:8787/v1",
  "apiKey": "sk-your-secret-api-key-here",
  "model": "gemini-2.5-flash"
}
```

### Continue.dev
```json
{
  "models": [
    {
      "title": "Gemini 2.5 Flash",
      "provider": "openai",
      "model": "gemini-2.5-flash",
      "apiBase": "http://127.0.0.1:8787/v1",
      "apiKey": "sk-your-secret-api-key-here"
    }
  ]
}
```

### Cursor IDE
```
Provider: OpenAI Compatible
Base URL: http://127.0.0.1:8787/v1
API Key: sk-your-secret-api-key-here
Model: gemini-2.5-flash
```

### Roo
```json
{
  "apiProvider": "openai-compatible",
  "baseURL": "http://127.0.0.1:8787/v1",
  "apiKey": "sk-your-secret-api-key-here",
  "model": "gemini-2.5-pro"
}
```

## 🧪 测试命令

### 健康检查
```bash
curl http://127.0.0.1:8787/health
```

### 模型列表
```bash
curl -H "Authorization: Bearer sk-your-secret-api-key-here" \
     http://127.0.0.1:8787/v1/models
```

### 聊天测试
```bash
curl -X POST http://127.0.0.1:8787/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-secret-api-key-here" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

## 🚨 故障排除

### OAuth问题
```bash
npm run check          # 检查状态
gemini                 # 重新认证
npm run update-oauth   # 更新配置
```

### 连接问题
```bash
npm start              # 重启服务器
curl http://127.0.0.1:8787/health  # 测试连接
```

### 常见错误
- **401 Unauthorized**: 检查API密钥
- **Connection refused**: 确保服务器运行
- **Model not found**: 使用正确模型名称

## 💡 模型选择指南

- **gemini-2.5-flash**: 快速响应，日常编程
- **gemini-2.5-pro**: 复杂分析，深度思考

## 📱 快速启动清单

1. ☐ `npm start` - 启动服务器
2. ☐ 验证健康状态
3. ☐ 配置AI插件
4. ☐ 测试连接
5. ☐ 开始使用！

---
**保存此文档以便快速参考配置信息** 📌
